/* =========================================================================================

   This is an auto-generated file: Any edits you make may be overwritten!

*/

#pragma once

namespace IRs
{
    //==============================================================================
    // N.B. These are part of the JUCE library and are only coverred by a JUCE licence
    extern const char*   guitar_amp_wav;
    const unsigned int   guitar_amp_wavSize = 90246;

    extern const char*   cassette_recorder_wav;
    const unsigned int   cassette_recorder_wavSize = 37902;
}

#include "IRData.cpp"
