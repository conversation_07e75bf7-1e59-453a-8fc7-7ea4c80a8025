/**
 * @file TracktionWrapperUtils_usage.cpp
 * @brief Example demonstrating how to use TracktionWrapperUtils
 * 
 * This example shows how to use the TracktionWrapperUtils utility class
 * to create TransportWrapper instances from EditWrapper instances.
 */

#include "TracktionWrapper/TracktionWrapperUtils.h"
#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/TransportWrapper.h"

void exampleUsage()
{
    // Assume you have an EditWrapper instance (typically from EngineModel)
    EditWrapper* editWrapper = nullptr; // This would come from your application
    
    // Example 1: Create a TransportWrapper using the utility
    auto transportWrapper = TracktionWrapperUtils::createTransportWrapper(editWrapper);
    
    if (transportWrapper) {
        // Successfully created TransportWrapper
        // You can now use it for transport operations
        transportWrapper->play();
        transportWrapper->stop();
        
        // The TransportWrapper can be passed to models that need it
        // For example: TransportModel constructor
    } else {
        // Failed to create TransportWrapper (editWrapper was null or invalid)
        // Handle the error case appropriately
    }
    
    // Example 2: Validate EditWrapper before using it
    if (TracktionWrapperUtils::isEditWrapperValid(editWrapper)) {
        // Safe to use the EditWrapper
        auto edit = TracktionWrapperUtils::getEditFromWrapper(editWrapper);
        auto transportControl = TracktionWrapperUtils::getTransportControlFromWrapper(editWrapper);
        
        if (edit && transportControl) {
            // Both components are available
            // You can use them directly or create wrappers
        }
    }
    
    // Example 3: Pattern used in AppModel::createEditDependentModels
    // Before TracktionWrapperUtils:
    /*
    tracktion::engine::Edit* edit = editWrapper ? &editWrapper->getEdit() : nullptr;
    tracktion::engine::TransportControl* transportControl = editWrapper
                                                                ? &editWrapper->getTransportControl()
                                                                : nullptr;
    
    mTransportModel = std::make_unique<TransportModel>(
        transportControl ? std::make_unique<TransportWrapper>(*transportControl, *edit) : nullptr,
        mEditModel.get(),
        this
    );
    */
    
    // After TracktionWrapperUtils:
    /*
    mTransportModel = std::make_unique<TransportModel>(
        TracktionWrapperUtils::createTransportWrapper(editWrapper),
        mEditModel.get(),
        this
    );
    */
}

/**
 * @brief Benefits of using TracktionWrapperUtils:
 * 
 * 1. **Consistency**: Ensures the same pattern is used throughout the codebase
 * 2. **Safety**: Includes proper null checking and exception handling
 * 3. **Maintainability**: Changes to the creation pattern only need to be made in one place
 * 4. **Readability**: Makes the intent clearer and reduces boilerplate code
 * 5. **Error Handling**: Provides consistent error handling and logging
 * 
 * @note The utility methods are designed to be safe with null pointers and
 *       will return nullptr if the input is invalid or if any errors occur.
 */
