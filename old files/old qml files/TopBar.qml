import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

RowLayout {
    id: topBar
    width: parent.width
    height: 50
    spacing: 10
    //padding: 10 //TODO find a way to add padding
    // anchors.top: parent.top
    // anchors.left: parent.left
    // anchors.right: parent.right

    property var transportController: appController.transportController // Access the C++ TransportController
    property var projectModel: appController.projectModel // Access the C++ ProjectModel

    ColumnLayout {
        Layout.leftMargin: 10
        Label {
            id: projectNameDisplay
            text: topBar.projectModel ? projectModel.projectName : "Loading Project..."
            font.pixelSize: 20
            font.bold: true
            verticalAlignment: Text.AlignVCenter
        }
        Label {
            text: "Audio block size: " + appController.engineContext.audioBlockSize + " Rate: " + appController.engineContext.audioSampleRate
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
        }
    }

    Label {
        id: barsDisplay
        text: {
            var totalBars = topBar.transportController.currentTime;
            var bars = Math.floor(totalBars);
            var beats = Math.floor((totalBars - bars) * 4); // Assuming 4 beats per bar
            var ticks = Math.floor(((totalBars - bars) * 4 - beats) * 960); // Assuming 960 ticks per beat

            return "Bars.Beats.Ticks: " +
                (bars < 10 ? "0" : "") + bars + ":" +
                (beats < 10 ? "0" : "") + beats + ":" +
                (ticks < 100 ? "0" : "") + ticks; // Ensure two digits for ticks
        }
        font.pixelSize: 18
        verticalAlignment: Text.AlignVCenter
        Layout.leftMargin: 20
    }

    Label {
        id: timeDisplay
        text: {
            var totalSeconds = topBar.transportController.currentTimeInSeconds;
            var minutes = Math.floor(totalSeconds / 60);
            var seconds = Math.floor(totalSeconds % 60);
            var milliseconds = Math.floor((totalSeconds - Math.floor(totalSeconds)) * 100); // Two decimal places for milliseconds

            var formattedMinutes = (minutes < 10 ? "0" : "") + minutes;
            var formattedSeconds = (seconds < 10 ? "0" : "") + seconds;
            var formattedMilliseconds = (milliseconds < 10 ? "0" : "") + milliseconds; // For two digits

            return "MM:SS:MS " + formattedMinutes + ":" + formattedSeconds + ":" + formattedMilliseconds;
        }
        font.pixelSize: 18
        verticalAlignment: Text.AlignVCenter
        Layout.leftMargin: 20
    }

    RowLayout {
        spacing: 5 // Adjust spacing as needed for proximity
        Button {
            text: "<<"
            onClicked: topBar.transportController.rewind()
        }

        Button {
            text: ">>"
            onClicked: topBar.transportController.fastForward()
        }
    }

    Button {
        text: topBar.transportController.isPlaying ? "Pause" : "Play"
        onClicked: topBar.transportController.togglePlayPause()
    }

    Button {
        text: "Stop"
        onClicked: topBar.transportController.stop()
    }

    // Tempo Controller
    RowLayout {
        spacing: 5
        Label {
            text: "Tempo:"
            verticalAlignment: Text.AlignVCenter
        }
        SpinBox {
            id: tempoSpinBox
            value: topBar.transportController.tempoBpm
            from: 40
            to: 240
            stepSize: 1
            onValueChanged: topBar.transportController.setTempoBpm(value)
            editable: true
            validator: IntValidator {
                bottom: 40; top: 240
            }
        }
        Label {
            text: "BPM"
            verticalAlignment: Text.AlignVCenter
        }
    }

    // Time Signature Display
    RowLayout {
        spacing: 5
        Label {
            text: "Time Signature:"
            verticalAlignment: Text.AlignVCenter
        }
        Label {
            text: appController.engineContext.timeSignatureNumerator + "/" + appController.engineContext.timeSignatureDenominator
            verticalAlignment: Text.AlignVCenter
            font.pixelSize: 18
        }
    }
}
