#pragma once

#include <QObject>
#include <juce_events/juce_events.h> // For juce::ChangeListener
#include <juce_audio_devices/juce_audio_devices.h> // For juce::AudioDeviceManager::Listener

// forward declare tracktion::engine classes
namespace tracktion
{
    inline namespace engine
    {
        class Engine;
        class Edit;
        class ArrangerTrack;
        class MarkerTrack;
        class TempoTrack;
        class ChordTrack;
        class MasterTrack;
    }
}


class EngineContext final : public QObject,
                            juce::ChangeListener
{
    Q_OBJECT
    Q_PROPERTY(int timeSignatureNumerator READ timeSignatureNumerator NOTIFY timeSignatureChanged)
    Q_PROPERTY(int timeSignatureDenominator READ timeSignatureDenominator NOTIFY timeSignatureChanged)
    Q_PROPERTY(int audioBlockSize READ audioBlockSize NOTIFY audioSettingsChanged)
    Q_PROPERTY(double audioSampleRate READ audioSampleRate NOTIFY audioSettingsChanged)

public:
    explicit EngineContext(QObject* parent = nullptr);
    ~EngineContext() override;

    tracktion::engine::Edit* getCurrentEdit() const;
    void loadProjectFromXml(const QString& xmlPath);
    void saveProjectToXml(const QString& xmlPath) const;

    int timeSignatureNumerator() const;
    int timeSignatureDenominator() const;
    int audioBlockSize() const;
    double audioSampleRate() const;

signals:
    void projectLoaded();
    void timeSignatureChanged();
    void audioSettingsChanged();

    /** Returns the global ArrangerTrack. */
    Q_INVOKABLE tracktion::engine::ArrangerTrack* getArrangerTrack() const;

    /** Returns the global MarkerTrack. */
    Q_INVOKABLE tracktion::engine::MarkerTrack* getMarkerTrack() const;

    /** Returns the global TempoTrack. */
    Q_INVOKABLE tracktion::engine::TempoTrack* getTempoTrack() const;

    /** Returns the global ChordTrack. */
    Q_INVOKABLE tracktion::engine::ChordTrack* getChordTrack() const;

    /** Returns the global MasterTrack. */
    Q_INVOKABLE tracktion::engine::MasterTrack* getMasterTrack() const;

private:
    std::unique_ptr<tracktion::engine::Engine> mEngine;
    std::unique_ptr<tracktion::engine::Edit> mEdit;

    // Implement juce::ChangeListener
    void changeListenerCallback(juce::ChangeBroadcaster* source) override;

};
