#include "AppController.h"
#include "EngineContext.h"
#include "TransportController.h"
#include "TrackModel.h"
#include "ClipModel.h"
#include "TrackWrapper.h"
#include "../Utils/TE/EngineHelpers.h"

#include <tracktion_engine/tracktion_engine.h>
#include <QDebug>
#include <QTimer>

// The 'te' namespace alias is already defined in EngineHelpers.h, so no need to redefine here.

AppController::AppController(EngineContext* engineCtx, QObject* parent) :
    QObject(parent), mEngineContext(engineCtx)
{
    mTransportController = new TransportController(mEngineContext, this);
    mProjectLoader = new ProjectLoader(mEngineContext, this);
    mProjectModel = new ProjectModel("Untitled Project", this);

    // Instantiate global track models
    mArrangerTrack = std::make_unique<GlobalTrackModel>(GlobalTrackModel::<PERSON><PERSON><PERSON>, this);
    mMarkerTrack = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Marker, this);
    mTempoTrack = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Tempo, this);
    mChordTrack = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Chord, this);
    mMasterTrack = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Master, this);

    // Add global tracks to mTracks list
    mTracks.append(mArrangerTrack.get());
    mTracks.append(mMarkerTrack.get());
    mTracks.append(mTempoTrack.get());
    mTracks.append(mChordTrack.get());
    mTracks.append(mMasterTrack.get());

    // Connect to project loaded signal to populate tracks and update project model
    connect(mEngineContext, &EngineContext::projectLoaded, this, [this]()
    {
        // Clear all existing tracks from the list, but only delete dynamic TrackModel objects.
        // GlobalTrackModel objects are owned by unique_ptr and should not be deleted here.
        for (int i = mTracks.size() - 1; i >= 0; --i)
        {
            QObject* track = mTracks.at(i);
            if (dynamic_cast<TrackModel*>(track))
            {
                delete mTracks.takeAt(i);
            }
            else if (dynamic_cast<GlobalTrackModel*>(track))
            {
                mTracks.removeAt(i);
            }
        }
        mTracks.clear();

        // Re-add existing global track models to mTracks list (ensuring they are at the beginning)
        // These are already managed by unique_ptr and should not be re-instantiated.
        mTracks.append(mArrangerTrack.get());
        mTracks.append(mMarkerTrack.get());
        mTracks.append(mTempoTrack.get());
        mTracks.append(mChordTrack.get());
        mTracks.append(mMasterTrack.get());

        populateTracks();
        emit tracksChanged();
        emit projectModelChanged();
    });

    // Initial population of dynamic tracks if a project is already loaded
    populateTracks();
    emit tracksChanged();
}

AppController::~AppController()
{
    // mTransportController, mProjectLoader, mProjectModel are parented to this, so they will be deleted automatically
    // Remove GlobalTrackModel objects from mTracks before deleting, as they are managed by unique_ptr
    // Iterate backwards to safely remove elements
    for (int i = mTracks.size() - 1; i >= 0; --i)
    {
        if (dynamic_cast<GlobalTrackModel*>(mTracks.at(i)))
        {
            mTracks.removeAt(i); // Remove pointer, but don't delete the object
        }
    }
    // Now, qDeleteAll will only delete TrackModel objects (which are parented to AppController or its children)
    qDeleteAll(mTracks); // This will delete TrackModel and ClipModel objects (if parented to TrackModel)
    mTracks.clear(); // Clear the list after deletion
}

TransportController* AppController::transportController() const
{
    return mTransportController;
}

ProjectModel* AppController::projectModel() const
{
    return mProjectModel;
}

QList<QObject*> AppController::tracks() const
{
    return mTracks;
}

GlobalTrackModel::TrackType AppController::globalTrackType() const
{
    // This is a static getter to expose the enum to QML
    return GlobalTrackModel::Arranger; // Any enum value will do, just to expose the type
}

void AppController::loadProject(const QString& file)
{
    // Use ProjectLoader to load the project
    std::unique_ptr<ProjectModel> loadedProject = mProjectLoader->loadProject(file);
    if (loadedProject)
    {
        // Transfer ownership to mProjectModel
        if (mProjectModel)
            mProjectModel->deleteLater(); // Delete old model
        mProjectModel = loadedProject.release();
        emit projectModelChanged(); // Notify QML that the project model has changed

        // Also load the project into the engine context
        mEngineContext->loadProjectFromXml(file); // This will trigger populateTracks via signal
    }
}

void AppController::saveProject(const QString& file) const
{
    // Use ProjectLoader to save the project
    if (mProjectModel)
    {
        mProjectLoader->saveProject(*mProjectModel, file);
    }
    // mEngineContext->saveProjectToXml(file); // This might be redundant if ProjectLoader handles all saving
}

void AppController::populateTracks()
{
    // This method now only populates dynamic tracks (audio/MIDI)
    // Global tracks are handled in the constructor and projectLoaded signal

    auto* edit = mEngineContext->getCurrentEdit();
    if (edit)
    {
        // Use EngineHelpers::getAudioTracks to get the list of tracks
        auto audioTracks = te::getAudioTracks(*edit);
        for (int i = 0; i < audioTracks.size(); ++i)
        {
            auto* teTrack = audioTracks[i];
            if (teTrack)
            {
                auto trackWrapper = std::make_shared<TrackWrapper>(*teTrack); // Create a TrackWrapper
                auto* trackModel = new TrackModel(trackWrapper, this); // Pass the TrackWrapper to TrackModel constructor

                for (auto* clip : teTrack->getClips())
                {
                    // Use WaveAudioClip as per tracktion_engine API
                    if (auto* waveAudioClip = dynamic_cast<te::WaveAudioClip*>(clip))
                    {
                        auto clipWrapper = std::make_shared<ClipWrapper>(*waveAudioClip); // Create a ClipWrapper
                        auto* clipModel = new ClipModel(clipWrapper, trackModel); // Pass the ClipWrapper to ClipModel constructor
                        trackModel->addClip(clipModel);
                    }
                }
                mTracks.append(trackModel);
            }
        }
    }
    // tracksChanged is emitted by the projectLoaded signal handler after all tracks are populated
    // No need to emit here if this is only called from the signal handler
}
