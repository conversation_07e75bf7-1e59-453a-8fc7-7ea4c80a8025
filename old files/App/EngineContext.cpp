#include "EngineContext.h"
#include <tracktion_engine/tracktion_engine.h>

EngineContext::EngineContext(QObject* parent) :
    QObject(parent)
{
    mEngine = std::make_unique<tracktion::engine::Engine>("AppQmlDAW");

    // Optional: Create empty edit
    mEdit = tracktion::engine::Edit::createSingleTrackEdit(*mEngine);
    emit projectLoaded();

    // Connect to projectLoaded signal to re-add listeners if mEdit changes
    connect(this, &EngineContext::projectLoaded, this, [this]()
    {
        // Removed: mEdit->addChangeListener(this);
        emit timeSignatureChanged();
        emit audioSettingsChanged(); // Force update
    });
}

EngineContext::~EngineContext()
{
    if (mEngine)
    {
        // Removed: mEngine->getDeviceManager().removeChangeListener(this);
    }
    // Removed: mEdit->removeChangeListener(this);
    mEdit.reset();
    mEngine.reset();
}

tracktion::engine::Edit* EngineContext::getCurrentEdit() const
{
    return mEdit.get();
}

void EngineContext::loadProjectFromXml(const QString& xmlPath)
{
    const juce::File file(xmlPath.toStdString());
    if (file.existsAsFile())
    {
        auto xml = juce::XmlDocument::parse(file);
        if (xml && xml->hasTagName("EDIT"))
        {
            mEdit = loadEditFromFile(*mEngine, file);
            emit projectLoaded(); // Emit signal after loading project
        }
    }
}

void EngineContext::saveProjectToXml(const QString& xmlPath) const
{
    if (!mEdit)
        return;

    auto xml = mEdit->state.createXml();
    if (xml)
        xml->writeTo(juce::File(xmlPath.toStdString()));
}


int EngineContext::timeSignatureNumerator() const
{
    if (mEdit)
    {
        return mEdit->tempoSequence.getTimeSigAt(tracktion::BeatPosition::fromBeats(0)).numerator;
    }
    return 4; // Default
}

int EngineContext::timeSignatureDenominator() const
{
    if (mEdit)
    {
        return mEdit->tempoSequence.getTimeSigAt(tracktion::BeatPosition::fromBeats(0)).denominator;
    }
    return 4; // Default
}

int EngineContext::audioBlockSize() const
{
    if (mEngine)
    {
        return mEngine->getDeviceManager().getBlockSize();
    }
    return 512; // Default
}

double EngineContext::audioSampleRate() const
{
    if (mEngine)
    {
        return mEngine->getDeviceManager().getSampleRate();
    }
    return 48000.0; // Default
}

// Implement juce::ChangeListener
void EngineContext::changeListenerCallback(juce::ChangeBroadcaster* source)
{
    // This is a temporary workaround until the correct ChangeBroadcaster for TempoSequence is identified.
    // Currently, EngineContext does not listen to any ChangeBroadcaster that would directly notify about time signature changes.
    // If other ChangeBroadcasters are added, this method will need to differentiate them.
    emit timeSignatureChanged();
}
