#pragma once

#include <QObject>
#include <QList>
#include "TransportController.h"
#include "Models/ProjectModel.h"
#include "IO/ProjectLoader.h"
#include "Models/GlobalTrackModel.h"

class EngineContext;
class TrackModel;
class ClipModel;

class AppController final : public QObject
{
    Q_OBJECT
    Q_PROPERTY(TransportController* transportController READ transportController CONSTANT)
    Q_PROPERTY(ProjectModel* projectModel READ projectModel NOTIFY projectModelChanged)
    Q_PROPERTY(QList<QObject*> tracks READ tracks NOTIFY tracksChanged)
    Q_PROPERTY(GlobalTrackModel::TrackType globalTrackType READ globalTrackType CONSTANT)

public:
    explicit AppController(EngineContext* engineCtx, QObject* parent = nullptr);
    ~AppController() override;

    TransportController* transportController() const;
    ProjectModel* projectModel() const;
    QList<QObject*> tracks() const;
    GlobalTrackModel::TrackType globalTrackType() const;

    Q_INVOKABLE void loadProject(const QString& file);
    Q_INVOKABLE void saveProject(const QString& file) const;

signals:
    void tracksChanged();
    void projectModelChanged();

private:
    EngineContext* mEngineContext;
    TransportController* mTransportController;
    ProjectLoader* mProjectLoader;
    ProjectModel* mProjectModel;
    QList<QObject*> mTracks;
    std::unique_ptr<GlobalTrackModel> mArrangerTrack;
    std::unique_ptr<GlobalTrackModel> mMarkerTrack;
    std::unique_ptr<GlobalTrackModel> mTempoTrack;
    std::unique_ptr<GlobalTrackModel> mChordTrack;
    std::unique_ptr<GlobalTrackModel> mMasterTrack;

    void populateTracks();
};
