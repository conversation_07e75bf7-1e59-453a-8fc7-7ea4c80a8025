#pragma once

#include <QObject>
#include <juce_events/juce_events.h> // For juce::ChangeListener

class QTimer;

class EngineContext;

class TransportController : public QObject,
                            public juce::ChangeListener
{
    Q_OBJECT
    Q_PROPERTY(bool isPlaying READ isPlaying NOTIFY isPlayingChanged)
    Q_PROPERTY(double currentTime READ currentTime NOTIFY currentTimeChanged)
    Q_PROPERTY(double currentTimeInSeconds READ currentTimeInSeconds NOTIFY currentTimeInSecondsChanged)
    Q_PROPERTY(double tempoBpm READ tempoBpm WRITE setTempoBpm NOTIFY tempoBpmChanged)

public:
    explicit TransportController(EngineContext* engineCtx, QObject* parent = nullptr);
    ~TransportController() override;

    Q_INVOKABLE void play();
    Q_INVOKABLE void stop();
    Q_INVOKABLE void togglePlayPause();
    Q_INVOKABLE void rewind();
    Q_INVOKABLE void fastForward();
    Q_INVOKABLE double currentTimeInSeconds() const;
    Q_INVOKABLE double tempoBpm() const;
    Q_INVOKABLE void setTempoBpm(double bpm);
    Q_INVOKABLE void setPositionInSeconds(double seconds);

    bool isPlaying() const;
    double currentTime() const; // In bars

signals:
    void isPlayingChanged();
    void currentTimeChanged();
    void currentTimeInSecondsChanged();
    void tempoBpmChanged();

private:
    EngineContext* mEngineContext;

    // Implement juce::ChangeListener
    void changeListenerCallback(juce::ChangeBroadcaster* source) override;

private slots:
    void onUpdateTimerTimeout();

private:
    QTimer* mUpdateTimer;
};
