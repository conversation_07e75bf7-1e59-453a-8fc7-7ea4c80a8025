cmake_minimum_required(VERSION 3.16)

project(QmlDAW VERSION 0.1 LANGUAGES C CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_BUILD_TYPE Debug) # Ensure debug symbols are included

# Required Qt modules
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets Quick Network Concurrent Xml)

#-------------------------------------- TRACKTION WRAPPER --------------------------------------------------------------

add_subdirectory(src/TracktionWrapper)

#------------------------------------------ Qml Models -----------------------------------------------------------------

add_subdirectory(src/QmlModels)

#--------------------------------------------- QmlDAW ------------------------------------------------------------------

# Main app executable
qt_add_executable(${PROJECT_NAME}
        src/App.cpp  # or consider moving to src/Models if tightly related
        src/main.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
        src
)

set_target_properties(${PROJECT_NAME} PROPERTIES
        AUTOMOC TRUE
        AUTORCC TRUE
        AUTOUIC TRUE
)

# Override the problematic OpenGL framework path
if (APPLE)
    target_compile_options(${PROJECT_NAME} PRIVATE "-F${Qt6_QtGui_FRAMEWORK_DIR}")
    target_link_libraries(${PROJECT_NAME} PRIVATE "-framework OpenGL")
    set(CMAKE_INSTALL_RPATH "@executable_path/../Frameworks")
endif ()

qt_add_resources(${PROJECT_NAME} "qml_resources"
        PREFIX "/"
        FILES
        qml/MainView.qml
        qml/components/dialogs/NewEditDialog.qml
        qml/components/MiniDawControls.qml
        qml/components/PanelContainer.qml
        qml/components/PanelSplitter.qml
        qml/components/Tab.qml
        qml/components/TabView.qml
        qml/components/bottom/ChannelStrip.qml
        qml/components/bottom/DeviceChain.qml
        qml/components/bottom/DevicePanel.qml
        qml/components/bottom/MixerPanel.qml
        qml/components/bottom/PluginUI.qml
        qml/components/center/ArrangementView.qml
        qml/components/center/AutomationLane.qml
        qml/components/center/BarsBeatsTimeline.qml
        qml/components/center/ClipItem.qml
        qml/components/center/SecondsTimeline.qml
        qml/components/center/Timeline.qml
        qml/components/center/TrackView.qml
        qml/components/center/TrackBodyView.qml
        qml/components/center/TrackFooterView.qml
        qml/components/center/TrackHeaderView.qml
        qml/components/left/FavoritesPanel.qml
        qml/components/left/FileBrowser.qml
        qml/components/left/PluginBrowser.qml
        qml/components/left/SearchBar.qml
        qml/components/right/ClipProperties.qml
        qml/components/right/HelpTooltip.qml
        qml/components/right/InfoPanel.qml
        qml/components/right/InspectorPanel.qml
        qml/components/right/PluginChain.qml
        qml/components/right/TrackProperties.qml
        qml/components/top/ClockDisplay.qml
        qml/components/top/MainMenu.qml
        qml/components/top/ProjectInfo.qml
        qml/components/top/TempoEditor.qml
        qml/components/top/TransportControls.qml
        qml/panels/BottomPanel.qml
        qml/panels/CenterPanel.qml
        qml/panels/LeftPanel.qml
        qml/panels/RightPanel.qml
        qml/panels/TopPanel.qml
)


target_link_libraries(${PROJECT_NAME}
        PRIVATE
        QmlModels
        TracktionWrapper

        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::Quick
        Qt6::Network
        Qt6::Concurrent
        Qt6::Xml
        Qt6::OpenGL
)

# macOS and Windows properties
set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_BUNDLE_GUI_IDENTIFIER com.yourcompany.qmldaw
        MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
        MACOSX_BUNDLE TRUE
        WIN32_EXECUTABLE TRUE
)

# Install Qml files directly into the bundle's Resources directory
install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/qml/"
        DESTINATION "${CMAKE_INSTALL_PREFIX}/appqmldaw.app/Contents/Resources/qml"
        COMPONENT Runtime
        FILES_MATCHING PATTERN "*.qml")

# Installation
include(GNUInstallDirs)
install(TARGETS ${PROJECT_NAME}
        BUNDLE DESTINATION .
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
