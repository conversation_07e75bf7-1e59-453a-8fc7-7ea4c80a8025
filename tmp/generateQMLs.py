import os

# Sample boilerplate content
def generate_boilerplate(component_name):
    return f"""import QtQuick 2.15
import QtQuick.Controls 2.15

Item {{
    id: {component_name.replace('.qml', '').lower()}
    width: 200
    height: 100

    // TODO: Implement {component_name.replace('.qml', '')}
    Rectangle {{
        anchors.fill: parent
        color: "lightgray"
        border.color: "gray"
        Text {{
            anchors.centerIn: parent
            text: "{component_name.replace('.qml', '')}"
        }}
    }}
}}
"""

if __name__ == "__main__":
    # Define base directory and component map
    base_dir = "./qml/"

    # Panel components and their corresponding files
    panel_components = {
        "TopPanel": [
            "TransportControls.qml",
            "TempoEditor.qml",
            "ClockDisplay.qml",
            "MainMenu.qml"
        ],
        "LeftPanel": [
            "FileBrowser.qml",
            "PluginBrowser.qml",
            "FavoritesPanel.qml",
            "SearchBar.qml"
        ],
        "CenterPanel": [
            "ArrangementView.qml",
            "TrackView.qml",
            "ClipItem.qml",
            "Timeline.qml",
            "AutomationLane.qml"
        ],
        "RightPanel": [
            "InspectorPanel.qml",
            "ClipProperties.qml",
            "TrackProperties.qml",
            "PluginChain.qml",
            "InfoPanel.qml",
            "HelpTooltip.qml"
        ],
        "BottomPanel": [
            "MixerPanel.qml",
            "ChannelStrip.qml",
            "DevicePanel.qml",
            "PluginUI.qml",
            "DeviceChain.qml"
        ]
    }

    # Create directories and files
    for panel, files in panel_components.items():
        panel_dir = os.path.join(base_dir, panel)
        os.makedirs(panel_dir, exist_ok=True)
        for file in files:
            path = os.path.join(panel_dir, file)
            with open(path, "w") as f:
                f.write(generate_boilerplate(file))

    # List generated QML files
    generated_files = []
    for panel, files in panel_components.items():
        for file in files:
            generated_files.append(f"{panel}/{file}")

    print("Generated QML files:")
    for file in generated_files:
        print(file)