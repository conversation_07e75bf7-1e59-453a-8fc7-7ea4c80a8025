# QML Architecture for Modular and Maintainable DAW UI

This document outlines best practices and recommendations for organizing a modular and maintainable QML-based Digital Audio Workstation (DAW) user interface that integrates with C++ models, particularly in the context of Tracktion Engine or similar architectures.

---

## 1. Root `AppModel` as Context Facade

Create a C++ `AppModel` class that acts as a facade, exposing sub-models used across the QML application.

```cpp
class AppModel : public QObject {
    Q_OBJECT
    Q_PROPERTY(TransportModel* transport READ transport CONSTANT)
    Q_PROPERTY(EditModel* edit READ edit CONSTANT)
    Q_PROPERTY(BrowserModel* browser READ browser CONSTANT)
    Q_PROPERTY(InspectorModel* inspector READ inspector CONSTANT)
    // ...

public:
    TransportModel* transport() const { return m_transport; }
    EditModel* edit() const { return m_edit; }
    BrowserModel* browser() const { return m_browser; }
    InspectorModel* inspector() const { return m_inspector; }

private:
    TransportModel* m_transport;
    EditModel* m_edit;
    BrowserModel* m_browser;
    InspectorModel* m_inspector;
};
```

Register globally:

```cpp
engine->appModel = std::make_unique<AppModel>(...);
engine->qmlEngine.rootContext()->setContextProperty("AppModel", engine->appModel.get());
```

---

## 2. Pass Sub-Models as Properties

Never access `AppModel` globally inside QML components. Always pass the required sub-models explicitly:

```qml
// TransportControls.qml
Item {
    property TransportModel transportModel: null
    Text { text: transportModel.isPlaying ? "Playing" : "Stopped" }
}

// MainView.qml
TransportControls {
    transportModel: AppModel.transport
}
```

### Benefits:

* Promotes modularity
* Enables reusability
* Supports testability and mocking
* Avoids tight coupling

---

## 3. Organize by Domain

Structure folders and QML components by their associated domain model:

```
src/
├── Models/
│   ├── AppModel.h/.cpp
│   ├── TransportModel.h/.cpp
│   └── ...
├── QML/
│   ├── Transport/
│   ├── Arrangement/
│   ├── Mixer/
│   ├── Inspector/
│   └── Browser/
```

Each folder contains components that are passed their domain-specific model.

---

## 4. Handle Optional Models Gracefully

Design components to work when passed `null` models:

```qml
property EditModel editModel: null

Repeater {
    model: editModel ? editModel.tracks : 0
}
```

---

## 5. Local Contexts for Dynamic Components

When dynamically loading components, set submodels via a local `QQmlContext`:

```cpp
QQmlContext* subContext = new QQmlContext(engine.rootContext());
subContext->setContextProperty("ClipModel", someClip);
loader->setContext(subContext);
loader->setSource(QUrl("qrc:/ClipEditor.qml"));
```

---

## 6. Folder Layout Scaffolding

```bash
mkdir -p src/Models
mkdir -p src/QML/{Transport,Arrangement,Mixer,Inspector,Device,Browser}

# Example files
touch src/Models/AppModel.h
touch src/Models/TransportModel.h

touch src/QML/Transport/TransportControls.qml
```

---

## Summary

| Goal                 | Recommendation                     |
| -------------------- | ---------------------------------- |
| Global access        | Use a top-level `AppModel`         |
| Component modularity | Pass models via QML properties     |
| Reusability          | Avoid direct `AppModel.*` access   |
| Organization         | Group components by domain         |
| Dynamic content      | Use `QQmlContext` with submodels   |
| Maintainability      | Keep context explicit and testable |
