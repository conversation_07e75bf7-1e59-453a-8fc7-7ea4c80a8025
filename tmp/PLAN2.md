# Tracktion QML Integration Plan

## Architecture Overview

We separate the system into three layers:

* **Tracktion Engine Classes** — Low-level C++ audio backend from Tracktion.
* **Wrapper Classes** — Pure C++ interfaces that wrap Tracktion classes and expose stable APIs.
* **QML Model Classes** — QObject-based classes (QML-friendly) that bind to UI logic.

This decoupling ensures that QML remains independent of Tracktion internals.

---

## Class Naming Convention

| QML Model          | C++ Wrapper          | Tracktion Class(es)                           |
| ------------------ | -------------------- | --------------------------------------------- |
| EngineModel        | EngineWrapper        | `tracktion::engine::Engine`                   |
| EditModel          | EditWrapper          | `tracktion::Edit`                             |
| TransportModel     | TransportWrapper     | `tracktion::TransportControl`                 |
| TrackModel         | TrackWrapper         | `tracktion::Track`, `AudioTrack`, `MidiTrack` |
| ClipModel          | ClipWrapper          | `AudioClip`, `MidiClip`, etc.                 |
| ProjectModel       | ProjectWrapper       | `tracktion::Project`                          |
| AudioFileModel     | AudioFileWrapper     | `tracktion::AudioFile`, `AudioFileCache`      |
| DeviceManagerModel | DeviceManagerWrapper | `tracktion::engine::DeviceManager`            |

---

## Directory Structure

```
src/
├── Wrappers/                  # Low-level wrappers
│   ├── EngineWrapper.h/.cpp
│   ├── EditWrapper.h/.cpp
│   ├── TransportWrapper.h/.cpp
│   ├── TrackWrapper.h/.cpp
│   └── ClipWrapper.h/.cpp
│   └── ProjectWrapper.h/.cpp
│   └── AudioFileWrapper.h/.cpp
│   └── DeviceManagerWrapper.h/.cpp
│
├── Models/                    # QML-facing models (QObject)
    ├── EngineModel.h/.cpp
    ├── EditModel.h/.cpp
    ├── TransportModel.h/.cpp
    ├── TrackModel.h/.cpp
    └── ClipModel.h/.cpp
    └── ProjectModel.h/.cpp
    └── AudioFileModel.h/.cpp
    └── DeviceManagerModel.h/.cpp
```

---

## Responsibilities

### Wrapper Classes

* Contain raw pointers or `juce::ReferenceCountedObjectPtr` to Tracktion classes.
* Never expose Tracktion headers to QML layer.
* Provide methods like `getName()`, `setGain(float)`, etc.
* Use `std::shared_ptr` for lifetime safety if needed.

### Model Classes

* Subclass `QObject` with `Q_PROPERTY` bindings.
* Connect to signals for reactive updates.
* Contain a reference to a `Wrapper` class instance.
* Are registered to QML via `qmlRegisterType`.

---

## Example Mapping

### `EngineModel.h`

```cpp
#pragma once
#include <QObject>
#include "EngineWrapper.h"

class EngineModel : public QObject {
    Q_OBJECT
    Q_PROPERTY(QString projectPath READ projectPath WRITE setProjectPath NOTIFY projectPathChanged)
public:
    explicit EngineModel(QObject* parent = nullptr);

    Q_INVOKABLE void loadProject();
    Q_INVOKABLE void saveProject();

    QString projectPath() const;
    void setProjectPath(const QString& path);

signals:
    void projectPathChanged();

private:
    std::unique_ptr<EngineWrapper> engine;
    QString currentProjectPath;
};
```

### `EngineWrapper.h`

```cpp
#pragma once
#include <tracktion_engine/tracktion_engine.h>

class EngineWrapper {
public:
    EngineWrapper();
    void loadProject(const juce::File&);
    void saveProject(const juce::File&);
    std::shared_ptr<class EditWrapper> getEdit();

private:
    tracktion::engine::Engine engine { "TracktionApp" };
};
```

---

## Scaffolding Script

Run this shell script to create all necessary files:

```sh
mkdir -p src/Wrappers src/Models

for name in Engine Edit Transport Track Clip Project AudioFile DeviceManager; do
    touch src/Wrappers/${name}Wrapper.h src/Wrappers/${name}Wrapper.cpp
    touch src/Models/${name}Model.h src/Models/${name}Model.cpp
done
```

---

## Next Steps

1. Implement `EngineWrapper` to own the Tracktion Engine instance.
2. Wire `EditWrapper` with `Edit` creation and loading.
3. Build `TransportModel` to allow play/stop from QML.
4. Test `TrackModel` by exposing a track list to QML `Repeater`.
5. Continue wiring `ClipModel`, `AudioFileModel`, etc.

---

## Optional Tools

* Use `CMake` + `qt_add_qml_module` to bundle QML models cleanly.
* Enable `AUTOMOC` and `AUTORCC` for QML projects.
* Consider wrapping with `juce::ScopedMessageManagerLock` where needed in wrappers (not in audio thread).
