### PLAN.md

#### **Project: MiniDAW Improvements**

This document outlines the plan to implement several new features and improvements to the MiniDAW application, focusing on enhancing the user interface and core functionality.

---

#### **1. Play/Pause Button Functionality**

**Objective:** Transform the existing "Play" button into a toggleable "Play/Pause" button.

**C++ (`src/App/TransportController.h`, `src/App/TransportController.cpp`):**
*   **`TransportController.h`:**
    *   Add a new `Q_INVOKABLE` method: `void togglePlayPause();`
*   **`TransportController.cpp`:**
    *   Implement `togglePlayPause()`:
        *   If `transport.isPlaying()` is true, call `transport.stop(true, false);` (pause with fade out, don't reset position).
        *   If `transport.isPlaying()` is false, call `transport.play(false);`.
        *   Ensure the `m_updateTimer` is started/stopped appropriately with playback state changes.

**QML (`qml/TopBar.qml`):**
*   Modify the "Play" button:
    *   Change `text` property to dynamically display "Play" or "Pause" based on `transportController.isPlaying`.
    *   Change `onClicked` to call `transportController.togglePlayPause()`.
    *   Remove the `enabled` binding, as the button should always be clickable to toggle.

---

#### **2. Rewind and Fast Forward Button Proximity**

**Objective:** Visually group the "Rewind" and "Fast Forward" buttons closer together.

**QML (`qml/TopBar.qml`):**
*   Introduce a nested `RowLayout` or `Item` to group the "Rewind" and "Fast Forward" buttons.
*   Adjust the `spacing` property of this new container to control their proximity, while maintaining the overall `RowLayout` structure of the `TopBar`.

---

#### **3. Time Spent Indicator (in Seconds)**

**Objective:** Display the current playback time in seconds.

**C++ (`src/App/TransportController.h`, `src/App/TransportController.cpp`):**
*   **`TransportController.h`:**
    *   Add a new `Q_PROPERTY`: `Q_PROPERTY(double currentTimeInSeconds READ currentTimeInSeconds NOTIFY currentTimeInSecondsChanged)`
    *   Add the corresponding getter declaration: `double currentTimeInSeconds() const;`
    *   Add the signal declaration: `void currentTimeInSecondsChanged();`
*   **`TransportController.cpp`:**
    *   Implement `currentTimeInSeconds()`:
        *   Retrieve `edit->getTransport().position.get()`.
        *   Return `tracktion::TimePosition::inSeconds()` from this position.
    *   Emit `currentTimeInSecondsChanged()` in `onUpdateTimerTimeout()` alongside `currentTimeChanged()`.

**QML (`qml/TopBar.qml`):**
*   Modify the `timeDisplay` Label:
    *   Update its `text` property to use `topBar.transportController.currentTimeInSeconds`.
    *   Format the displayed text to show seconds (e.g., `toFixed(2)` for two decimal places).

---

#### **4. Display Project Name**

**Objective:** Show the current project's name in the `TopBar`.

**C++ (`src/Model/ProjectModel.h`, `src/Model/ProjectModel.cpp`, `src/IO/ProjectLoader.h`, `src/IO/ProjectLoader.cpp`, `src/App/AppController.h`, `src/App/AppController.cpp`):**
*   **`ProjectModel.h`:**
    *   Add a `Q_PROPERTY`: `Q_PROPERTY(QString projectName READ projectName WRITE setProjectName NOTIFY projectNameChanged)`
    *   Add getter/setter declarations: `QString projectName() const; void setProjectName(const QString& name);`
    *   Add signal declaration: `void projectNameChanged();`
    *   Add a private member: `QString m_projectName;`
*   **`ProjectModel.cpp`:**
    *   Implement the `projectName()` getter and `setProjectName()` setter, emitting `projectNameChanged()` when the name is set.
    *   Initialize `m_projectName` in the constructor (e.g., to "Untitled Project").
*   **`ProjectLoader.h` / `ProjectLoader.cpp`:**
    *   Modify `ProjectLoader` to load and save the project name from/to the project XML file. This will involve adding a new XML tag (e.g., `<ProjectName>`) to the project structure.
*   **`AppController.h` / `AppController.cpp`:**
    *   Add a `Q_PROPERTY` to `AppController` to expose the `ProjectModel` (or just the `projectName` directly) to QML. This will allow `TopBar.qml` to access the project name.
    *   Ensure `AppController` updates its `projectName` property when a new project is loaded.

**QML (`qml/TopBar.qml`):**
*   Add a new `Label` element to display `appController.projectModel.projectName`.
*   Position this label appropriately in the `TopBar` layout.

---

#### **5. Tempo Controller**

**Objective:** Add a UI element to control the project's tempo (BPM).

**C++ (`src/App/TransportController.h`, `src/App/TransportController.cpp`):**
*   **`TransportController.h`:**
    *   Add a new `Q_PROPERTY`: `Q_PROPERTY(double tempoBpm READ tempoBpm WRITE setTempoBpm NOTIFY tempoBpmChanged)`
    *   Add getter/setter declarations: `double tempoBpm() const; void setTempoBpm(double bpm);`
    *   Add signal declaration: `void tempoBpmChanged();`
*   **`TransportController.cpp`:**
    *   Implement `tempoBpm()`: Return `edit->tempoSequence.getTempo().inBeatsPerMinute()`.
    *   Implement `setTempoBpm(double bpm)`: Call `edit->tempoSequence.setTempo(tracktion::Tempo::fromBeatsPerMinute(bpm));` and emit `tempoBpmChanged()`.
    *   Ensure `tempoBpmChanged()` is emitted when the project loads or tempo changes internally.

**QML (`qml/TopBar.qml`):**
*   Add a `SpinBox` control for tempo.
*   Bind its `value` property to `topBar.transportController.tempoBpm`.
*   Set appropriate `minimumValue` and `maximumValue` for the tempo.
*   Add a `Label` to display the current BPM value.

---

#### **6. Always Showing and Click-Draggable Playhead**

**Objective:** Make the playhead always visible and allow users to click and drag it to change the time position.

**C++ (`src/App/TransportController.h`, `src/App/TransportController.cpp`):**
*   **`TransportController.h`:**
    *   Add a new `Q_INVOKABLE` method: `void setPositionInSeconds(double seconds);`
*   **`TransportController.cpp`:**
    *   Implement `setPositionInSeconds(double seconds)`:
        *   Call `edit->getTransport().position = tracktion::TimePosition::fromSeconds(seconds);`
        *   Emit `currentTimeChanged()` and `currentTimeInSecondsChanged()` after setting the position.

**QML (`qml/TrackViewContainer.qml`):**
*   **Always Showing:**
    *   Remove `visible: trackViewContainer.transportController.isPlaying` from the `playhead` Rectangle.
*   **Click-Draggable:**
    *   Add a `MouseArea` to the `playhead` Rectangle.
    *   Implement `onPressed`, `onPositionChanged`, and `onReleased` handlers to calculate new time based on mouse X and call `trackViewContainer.transportController.setPositionInSeconds()`.
*   **Playhead Position Update:** Ensure the `playhead.x` binding correctly reflects the `currentTimeInSeconds` and `timelineFlickable.contentX` for accurate visual positioning during playback and dragging.

---

#### **7. Playhead Drawing Order**

**Objective:** Ensure the playhead is drawn behind the track header but in front of the track timeline.

**QML (`qml/TrackViewContainer.qml`):**
*   Adjust `z` properties:
    *   Set `z` of `trackHeaderFlickable` to `2`.
    *   Set `z` of the `playhead` Rectangle to `1`.
    *   Set `z` of `timelineFlickable` to `0`.

---

#### **8. Track Header Display Track Type**

**Objective:** Display the track type (Audio, Marker, Folder, etc.) in the `TrackHeaderDelegate`.

**C++ (`src/Model/TrackModel.h`, `src/Model/TrackModel.cpp`, `src/IO/ProjectLoader.h`, `src/IO/ProjectLoader.cpp`):**
*   **`TrackModel.h`:**
    *   Define `TrackType` enum and register with `Q_ENUM`.
    *   Add `Q_PROPERTY(TrackType trackType READ trackType WRITE setTrackType NOTIFY trackTypeChanged)`.
*   **`TrackModel.cpp`:**
    *   Implement `trackType()` getter and `setTrackType()` setter.
    *   Initialize `m_trackType` in constructors.
*   **`ProjectLoader.h` / `ProjectLoader.cpp`:**
    *   Modify `ProjectLoader` to load and save the `trackType` from/to the project XML. (Note: Current implementation in `ProjectLoader.cpp` reads the type but doesn't fully integrate it with `TrackModel` creation in `AppController`. This is a known area for future improvement.)

**QML (`qml/TrackHeaderDelegate.qml`):**
*   Add a new `Label` element to display `trackHeaderDelegate.trackModel.trackType`.
*   Use a `switch` statement or `Qt.binding` to convert the enum integer value to a user-friendly string.

---

#### **9. Suggested Further Improvements**

*   **Visual Feedback for Playhead Dragging:** Add a tooltip or temporary display showing the exact time (in seconds or bars) as the playhead is dragged.
*   **Zoom/Scroll Functionality:** Implement horizontal zooming on the timeline to view more or less detail, and horizontal scrolling beyond the initial 32 bars. This would involve dynamically adjusting `pixelsPerBar` and `totalTimelineWidth`.
*   **Project Save/Load UI:** Add explicit "Save Project" and "Load Project" buttons to the `TopBar` or a menu, connecting them to `ProjectLoader` functionality.
*   **Error Handling and User Feedback:** Implement more robust error handling and provide user feedback for operations (e.g., "Project Saved Successfully").
*   **Track Management UI:** Add buttons to add/remove tracks.
*   **Clip Editing:** Implement basic clip editing features like resizing, moving, and splitting clips directly on the timeline.
