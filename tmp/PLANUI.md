# PLANUI.md

## 🎯 Objective

Define a scalable and modular QML-based UI layout for a DAW, inspired by modern DAWs (Ableton Live, Bitwig, Logic Pro). This plan complements the Tracktion Engine backend wrapping and ensures a clean separation of UI zones.

---

## 🧱 High-Level Structure

```
┌────────────────────────────────────────────┐
│                TopPanel                    │
├───────────┬───────────────────┬────────────┤
│           │    CenterPanel    │            │
│ LeftPanel │                   │ RightPanel │
│           │───────────────────│            │
│           │    BottomPanel    │            │
└───────────┴───────────────────┴────────────┘
```

---

## 📁 QML Directory Structure

```
qml/
├── App.qml
├── MainView.qml

├── components/
│   ├── PanelSplitter.qml
│   ├── PanelContainer.qml
│   ├── Tab.qml
│   └── TabView.qml
```

---

## 📐 Panels & Responsibilities

### `TopPanel.qml`

* Hosts `TransportPanel`
* Optionally host global tempo/time signature info

### `ExplorerPanel.qml`

* Left-side dockable
* `TabView`: File Explorer + Plugin List

### `ArrangementPanel.qml`

* Central timeline view
* Interacts with `EditModel` and `TrackModel`

### `InfoPanel.qml`

* Right-side panel
* Shows selection info, clip/device inspector

### `BottomPanel.qml`

* `TabView`: MixerPanel (track volumes) and DevicesPanel (FX chains)

---

## 🧩 Tab System

Use a reusable tab component:

### `Tab.qml`

```qml
Row {
    Repeater {
        model: tabTitles
        delegate: Button { text: modelData }
    }
}
```

### `TabView.qml`

```qml
Column {
    Tab { id: tab }
    Loader { sourceComponent: tabViews[tabBar.currentIndex] }
}
```

---

## ✅ ExplorerPanel Tabs

* **FileBrowserTab.qml**: FilesystemModel + drag to arrangement
* **PluginBrowserTab.qml**: VSTs, Instruments, FX plugins from `PluginListModel`

---

## ✅ BottomPanel Tabs

* **MixerPanel.qml**: Horizontal channel strips per `TrackModel`
* **DevicesPanel.qml**: FX chain per track, grid or list layout

---

## 🛠️ Shell Scaffold Command

```bash
mkdir -p qml/{panels,components,theme}

# Panels
for p in Top Bottom Explorer Arrangement Info Transport Mixer Devices; do
  touch qml/panels/${p}Panel.qml
done

# Components
touch qml/components/{PanelSplitter.qml,PanelContainer.qml,DAWButton.qml,TabBar.qml,TabView.qml}

# Theme
touch qml/theme/{Colors.qml,Sizes.qml}

# Main entry
touch qml/{App.qml,MainView.qml}
```

---

## 🔄 Next Steps

* Generate base QML files using above plan
* Link `Model` C++ backends to panels
* Implement panel resizing (drag handles or `SplitView`)
* Design Tab system and add switching logic
* Style all with `Colors.qml`, `Sizes.qml`
* Hook up interaction (play/stop, drag plugins, etc.)
