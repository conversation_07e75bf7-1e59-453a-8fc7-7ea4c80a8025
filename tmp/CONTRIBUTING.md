# Contributing Guidelines

This document outlines the guidelines and best practices for contributing to this project. Adhering to these principles helps maintain code quality, consistency, and long-term maintainability.

## Architectural Patterns

### Model-Wrapper Pattern

The project utilizes a Model-Wrapper architectural pattern to separate concerns between the UI (QML) and the underlying Tracktion Engine / JUCE library.

*   **Models (QML-facing):** Classes in the `src/QmlModels/` directory are designed to expose data and functionality to the QML user interface.
    *   They typically inherit from `QObject` and use `Q_PROPERTY` and `Q_INVOKABLE` macros to make properties and methods accessible from QML.
    *   They hold `std::shared_ptr` or `std::unique_ptr` to their corresponding `Wrapper` classes, delegating calls to the underlying engine.
    *   **Rule:** If a Model class is always expected to represent a valid underlying object (e.g., a `ClipModel` must always represent a `ClipWrapper`), its default constructor should be explicitly deleted (`= delete`) to prevent the creation of invalid or uninitialized instances. See `ClipModel.h` for an example. This ensures a stronger contract and clearer intent.

*   **Wrappers (Tracktion Engine / JUCE-facing):** Classes in the `src/TracktionWrapper/` directory encapsulate direct interactions with the `tracktion::engine` and JUCE libraries.
    *   They provide a cleaner C++ interface to the underlying engine, abstracting away its complexities.
    *   They typically hold raw pointers or references to `tracktion::engine` or JUCE objects.
    *   Their primary role is to translate calls between the Models and the low-level engine APIs.

## General Code Quality

*   **SOLID Principles:** Strive to adhere to SOLID principles (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion) where applicable, without over-engineering.
*   **Readability:** Write clear, concise, and well-commented code.
*   **Consistency:** Follow existing coding styles and conventions within the project.
*   **Error Handling:** Implement robust error handling and logging where necessary.
