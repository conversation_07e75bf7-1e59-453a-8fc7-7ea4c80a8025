import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ApplicationWindow {
    id: root
    width: 800
    height: 600
    visible: true
    title: "Test View"

    ColumnLayout {
        id: mainLayout
        anchors.fill: parent
        spacing: 10

        Text {
            id: projectPathText
            text: "Project Path: " + (AppInstance && AppInstance.getAppModel() && AppInstance.getAppModel().getProjectManagerModel() ? AppInstance.getAppModel().getProjectManagerModel().projectPath : "N/A")
            color: "white"
            font.pixelSize: 20
        }

        Text {
            id: isPlayingText
            text: "Is Playing: " + (AppInstance && AppInstance.getAppModel() && AppInstance.getAppModel().getTransportModel() ? AppInstance.getAppModel().getTransportModel().isPlaying : "N/A")
            color: "white"
            font.pixelSize: 20
        }

        Button {
            text: "Toggle Play/Pause"
            onClicked: {
                if (AppInstance && AppInstance.getAppModel() && AppInstance.getAppModel().getTransportModel()) {
                    AppInstance.getAppModel().getTransportModel().togglePlayPause();
                }
            }
        }
    }

    Component.onDestruction: {
        // Clear text properties to prevent access to destroyed C++ objects during QML shutdown
        projectPathText.text = "Project Path: Shutting down...";
        isPlayingText.text = "Is Playing: Shutting down...";
    }
}
