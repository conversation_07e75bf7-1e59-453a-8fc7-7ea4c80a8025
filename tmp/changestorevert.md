# Changes to Revert

This document outlines the changes made to the project files during the refactoring of `EditModel` and related classes. These changes were aimed at ensuring `EditModel` relies on `EditWrapper` instead of directly handling `tracktion engine objects`, and addressing subsequent compilation and runtime issues.

## 1. `src/Wrappers/EditWrapper.h`

**Summary of Changes:**
- Added `getName()`, `getProjectLengthInBars()`, `addTrack()`, and `removeTrack()` methods.
- Modified `addTrack()` to return `std::shared_ptr<TrackWrapper>`.
- Modified `removeTrack()` to accept `TrackWrapper*`.
- Re-added `tracktion::engine::Edit& getEdit() const;` to allow `EditViewState` and other direct `tracktion` interfaces to access the underlying `Edit` object.

## 2. `src/Wrappers/EditWrapper.cpp`

**Summary of Changes:**
- Implemented `getName()` to return `mEdit.getName()`.
- Implemented `getProjectLengthInBars()` using `mEdit.tempoSequence.getTimeSigAt()` and `mEdit.tempoSequence.toBeats()`.
- Implemented `addTrack()` to use `mEdit.ensureNumberOfAudioTracks()` and `tracktion::engine::getAudioTracks(mEdit).getLast()`.
- Implemented `removeTrack()` to use `mEdit.deleteTrack(&trackWrapper->getTrack())`.
- Re-implemented `tracktion::engine::Edit& EditWrapper::getEdit() const { return mEdit; }`.

## 3. `src/Models/EditModel.h`

**Summary of Changes:**
- Changed the constructor signature from `explicit EditModel(tracktion::engine::Edit* edit, QObject* parent = nullptr);` to `explicit EditModel(std::unique_ptr<EditWrapper> editWrapper, QObject* parent = nullptr);`.
- Removed the forward declaration `namespace tracktion { inline namespace engine { class Edit; } }`.

## 4. `src/Models/EditModel.cpp`

**Summary of Changes:**
- Updated the constructor to accept `std::unique_ptr<EditWrapper>` and use `std::move` for initialization.
- Updated `getName()`, `getProjectLengthInBars()`, `addTrack()`, and `removeTrack()` to delegate to `mEditWrapper` methods.
- Removed `#include <tracktion_engine/tracktion_engine.h>`.
- Updated `addTrack()` to handle `std::shared_ptr<TrackWrapper>` returned by `EditWrapper::addTrack()`.
- Updated `removeTrack()` to pass `TrackWrapper*` to `EditWrapper::removeTrack()`.

## 5. `src/Models/TrackModel.h`

**Summary of Changes:**
- Added `std::shared_ptr<TrackWrapper> getTrackWrapper() const;` to expose the internal `TrackWrapper`.

## 6. `src/Models/TrackModel.cpp`

**Summary of Changes:**
- Implemented `std::shared_ptr<TrackWrapper> TrackModel::getTrackWrapper() const { return mTrack; }`.

## 7. `src/App.cpp`

**Summary of Changes:**
- Modified the `App` constructor to:
    - Create `std::unique_ptr<EditWrapper> currentEdit` from `mEngineModel->getEngineWrapper()->getEdit()`.
    - Pass `currentEdit->getEdit()` to `EditViewState`'s constructor.
    - Pass `std::move(currentEdit)` to `EditModel`'s constructor.
- Added a `qDebug()` statement to confirm `mAppModel` initialization.

## 8. `src/main.cpp`

**Summary of Changes:**
- Changed `QQmlApplicationEngine applicationEngine;` to `std::unique_ptr<QQmlApplicationEngine> applicationEngine = std::make_unique<QQmlApplicationEngine>();` to manage its lifetime on the heap.
- Updated all uses of `applicationEngine` to `applicationEngine.get()` or `applicationEngine->`.
- Changed `App application(&applicationEngine);` to `App application(applicationEngine.get(), &app);` to parent `App` to `QGuiApplication`.
- Explicitly `QObject::disconnect` the `objectCreated` signal.
- Explicitly cleared the `AppInstance` context property and reset `applicationEngine` (`applicationEngine.reset();`) before `juce::ScopedJuceInitialiser_GUI` is destroyed.
- Moved `juce::ScopedJuceInitialiser_GUI juceInitialiser;` to be the last local variable declared in `main()` to ensure it's destroyed last.

## 9. `qml/TestView.qml`

**Summary of Changes:**
- Added a `Component.onDestruction` handler to explicitly clear the `text` properties of `projectPathText` and `isPlayingText` to prevent QML from attempting to access destroyed C++ objects during shutdown.
