<!DOCTYPE RCC>
<RCC version="1.0">
    <qresource prefix="/qml/">
        <file>MainView.qml</file>
        <file>components/dialogs/NewEditDialog.qml</file>
        <file>components/MiniDawControls.qml</file>
        <file>components/PanelContainer.qml</file>
        <file>components/PanelSplitter.qml</file>
        <file>components/Tab.qml</file>
        <file>components/TabView.qml</file>
        <file>components/bottom/ChannelStrip.qml</file>
        <file>components/bottom/DeviceChain.qml</file>
        <file>components/bottom/DevicePanel.qml</file>
        <file>components/bottom/MixerPanel.qml</file>
        <file>components/bottom/PluginUI.qml</file>
        <file>components/center/ArrangementView.qml</file>
        <file>components/center/AutomationLane.qml</file>
        <file>components/center/ClipItem.qml</file>
        <file>components/center/Timeline.qml</file>
        <file>components/center/TrackBodyView.qml</file>
        <file>components/center/TrackFooterView.qml</file>
        <file>components/center/TrackHeaderView.qml</file>
        <file>components/center/TrackView.qml</file>
        <file>components/left/FavoritesPanel.qml</file>
        <file>components/left/FileBrowser.qml</file>
        <file>components/left/PluginBrowser.qml</file>
        <file>components/left/SearchBar.qml</file>
        <file>components/right/ClipProperties.qml</file>
        <file>components/right/HelpTooltip.qml</file>
        <file>components/right/InfoPanel.qml</file>
        <file>components/right/InspectorPanel.qml</file>
        <file>components/right/PluginChain.qml</file>
        <file>components/right/TrackProperties.qml</file>
        <file>components/top/ClockDisplay.qml</file>
        <file>components/top/MainMenu.qml</file>
        <file>components/top/ProjectInfo.qml</file>
        <file>components/top/TempoEditor.qml</file>
        <file>components/top/TransportControls.qml</file>
        <file>panels/BottomPanel.qml</file>
        <file>panels/CenterPanel.qml</file>
        <file>panels/LeftPanel.qml</file>
        <file>panels/RightPanel.qml</file>
        <file>panels/TopPanel.qml</file>
    </qresource>
</RCC>
