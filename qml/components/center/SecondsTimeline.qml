import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// SecondsTimeline - displays time in seconds at the bottom
Rectangle {
    id: secondsTimeline

    property real barWidth: 100
    property real projectLengthInBars: 32
    property real extraBars: 8
    property var transportModel: null
    property real tempoBpm: 120 // Default tempo

    height: 25
    color: "#2a2a2a"
    border.color: "#555555"
    border.width: 1

    // Timeline content
    Item {
        anchors.fill: parent
        anchors.margins: 2
        clip: true

        // Time markers
        Canvas {
            id: timelineCanvas
            anchors.fill: parent

            onPaint: {
                var ctx = getContext("2d");
                ctx.clearRect(0, 0, width, height);

                var totalBars = secondsTimeline.projectLengthInBars + secondsTimeline.extraBars;
                var totalTimeInSeconds = totalBars * 4 * 60 / secondsTimeline.tempoBpm; // 4 beats per bar
                var secondsPerPixel = totalTimeInSeconds / (totalBars * secondsTimeline.barWidth);

                // Draw second markers
                ctx.font = "8px Arial";
                ctx.textAlign = "center";
                ctx.fillStyle = "white";

                // Draw every second
                for (var i = 0; i <= totalTimeInSeconds; i++) {
                    var x = i / secondsPerPixel;

                    // Major markers every 10 seconds
                    if (i % 10 === 0) {
                        ctx.strokeStyle = "#666666";
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(x, 0);
                        ctx.lineTo(x, height);
                        ctx.stroke();

                        // Draw time label for major markers
                        var minutes = Math.floor(i / 60);
                        var seconds = i % 60;
                        var timeLabel = minutes + ":" + (seconds < 10 ? "0" : "") + seconds;
                        ctx.fillText(timeLabel, x, height - 3);
                    }
                    // Minor markers every 5 seconds (but not on 10-second marks)
                    else if (i % 5 === 0) {
                        ctx.strokeStyle = "#555555";
                        ctx.lineWidth = 1.5;
                        ctx.beginPath();
                        ctx.moveTo(x, height * 0.3);
                        ctx.lineTo(x, height);
                        ctx.stroke();

                        // Draw smaller time label for 5-second marks
                        ctx.font = "7px Arial";
                        ctx.fillStyle = "#CCCCCC";
                        ctx.fillText(i.toString(), x, height - 8);
                        ctx.font = "8px Arial";
                        ctx.fillStyle = "white";
                    }
                    // Every other second
                    else {
                        ctx.strokeStyle = "#444444";
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(x, height * 0.6);
                        ctx.lineTo(x, height);
                        ctx.stroke();
                    }
                }
            }

            // Redraw when properties change
            Connections {
                target: secondsTimeline

                function onBarWidthChanged() {
                    timelineCanvas.requestPaint();
                }

                function onProjectLengthInBarsChanged() {
                    timelineCanvas.requestPaint();
                }

                function onExtraBarsChanged() {
                    timelineCanvas.requestPaint();
                }

                function onTempoBpmChanged() {
                    timelineCanvas.requestPaint();
                }
            }

            // Update tempo from transport model
            Connections {
                target: secondsTimeline.transportModel

                function onTempoBpmChanged() {
                    secondsTimeline.tempoBpm = secondsTimeline.transportModel.tempoBpm;
                }
            }
        }

        // Playhead indicator
        Rectangle {
            id: playheadIndicator
            width: 2
            height: parent.height
            color: "red"
            x: secondsTimeline.transportModel ? (secondsTimeline.transportModel.currentTime * secondsTimeline.barWidth) : 0
            z: 10
            visible: secondsTimeline.transportModel !== null

            // Animate playhead movement
            Behavior on x {
                NumberAnimation {
                    duration: 50
                    easing.type: Easing.Linear
                }
            }
        }

        // Mouse interaction for seeking
        MouseArea {
            anchors.fill: parent
            onClicked: function (mouse) {
                if (secondsTimeline.transportModel) {
                    var totalTimeInSeconds = (secondsTimeline.projectLengthInBars + secondsTimeline.extraBars) * 4 * 60 / secondsTimeline.tempoBpm;
                    var secondsPerPixel = totalTimeInSeconds / ((secondsTimeline.projectLengthInBars + secondsTimeline.extraBars) * secondsTimeline.barWidth);
                    var newTimeInSeconds = mouse.x * secondsPerPixel;
                    secondsTimeline.transportModel.setPositionInSeconds(newTimeInSeconds);
                }
            }
        }
    }

    // Initialize tempo from transport model
    Component.onCompleted: {
        if (secondsTimeline.transportModel) {
            secondsTimeline.tempoBpm = secondsTimeline.transportModel.tempoBpm;
        }
    }
}
