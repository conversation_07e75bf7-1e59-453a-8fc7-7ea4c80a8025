import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// BarsBeatsTimeline - displays bars and beats timeline at the top
Rectangle {
    id: barsBeatsTimeline

    property real barWidth: 100
    property real projectLengthInBars: 32
    property real extraBars: 8
    property int beatsPerBar: 4
    property var transportModel: null

    height: 30
    color: "#333333"
    border.color: "#555555"
    border.width: 1

    // Timeline content
    Item {
        anchors.fill: parent
        anchors.margins: 2
        clip: true

        // Bar and beat markers
        Canvas {
            id: timelineCanvas
            anchors.fill: parent

            onPaint: {
                var ctx = getContext("2d");
                ctx.clearRect(0, 0, width, height);

                var totalBars = barsBeatsTimeline.projectLengthInBars + barsBeatsTimeline.extraBars;
                var beatWidth = barsBeatsTimeline.barWidth / barsBeatsTimeline.beatsPerBar;

                // Draw beat lines and labels
                ctx.font = "10px Arial";
                ctx.textAlign = "center";

                for (var bar = 0; bar <= totalBars; bar++) {
                    var barX = bar * barsBeatsTimeline.barWidth;

                    // Draw bar line
                    ctx.strokeStyle = "#777777";
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(barX, 0);
                    ctx.lineTo(barX, height);
                    ctx.stroke();

                    // Draw bar number (bars start at 1, not 0)
                    if (bar < totalBars) { // Don't draw number for the last line
                        ctx.fillStyle = "white";
                        ctx.fillText((bar + 1).toString(), barX + barsBeatsTimeline.barWidth / 2, height - 5);
                    }

                    // Draw beat lines within the bar
                    for (var beat = 1; beat < barsBeatsTimeline.beatsPerBar; beat++) {
                        var beatX = barX + beat * beatWidth;
                        ctx.strokeStyle = "#555555";
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(beatX, height * 0.3);
                        ctx.lineTo(beatX, height);
                        ctx.stroke();

                        // Draw beat number
                        ctx.fillStyle = "#CCCCCC";
                        ctx.font = "8px Arial";
                        ctx.fillText((beat + 1).toString(), beatX, height - 15);
                        ctx.font = "10px Arial";
                    }
                }
            }

            // Redraw when properties change
            Connections {
                target: barsBeatsTimeline

                function onBarWidthChanged() {
                    timelineCanvas.requestPaint();
                }

                function onProjectLengthInBarsChanged() {
                    timelineCanvas.requestPaint();
                }

                function onExtraBarsChanged() {
                    timelineCanvas.requestPaint();
                }
            }
        }

        // Playhead indicator
        Rectangle {
            id: playheadIndicator
            width: 2
            height: parent.height
            color: "red"
            x: barsBeatsTimeline.transportModel ? (barsBeatsTimeline.transportModel.currentTime * barsBeatsTimeline.barWidth) : 0
            z: 10
            visible: barsBeatsTimeline.transportModel !== null

            // Animate playhead movement
            Behavior on x {
                NumberAnimation {
                    duration: 50
                    easing.type: Easing.Linear
                }
            }
        }

        // Mouse interaction for seeking
        MouseArea {
            anchors.fill: parent
            onClicked: function (mouse) {
                if (barsBeatsTimeline.transportModel) {
                    var newTimeInBars = mouse.x / barsBeatsTimeline.barWidth;
                    // Convert bars to seconds (assuming 4 beats per bar and current tempo)
                    var newTimeInSeconds = newTimeInBars * 4 * 60 / barsBeatsTimeline.transportModel.tempoBpm;
                    barsBeatsTimeline.transportModel.setPositionInSeconds(newTimeInSeconds);
                }
            }
        }
    }
}
