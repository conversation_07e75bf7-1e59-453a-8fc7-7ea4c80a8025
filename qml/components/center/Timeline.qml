import QtQuick 2.15
// import QtQuick.Controls 2.15
// import QtQuick.Layouts 1.15
// import QtQuick.Shapes 1.15

Item {
    id: timeline

    property var transportModel: null
    property var editModel: null

    // Define a property for the width of one bar in pixels
    property real barWidth: 100

    Rectangle {
        anchors.fill: parent
        color: "#282828" // Dark background for timeline
        border.color: "#404040"
        border.width: 1

        // Playhead
        Rectangle {
            id: playhead
            width: 2
            height: parent.height
            color: "red"
            x: timeline.transportModel ? (timeline.transportModel.currentTime * timeline.barWidth) : 0
            z: 10 // Ensure playhead is on top

            // Animate playhead movement
            Behavior on x {
                NumberAnimation {
                    duration: 50; easing.type: Easing.Linear
                }
            }
        }

        // Time markers (bars)
        Repeater {
            model: timeline.editModel ? timeline.editModel.projectLengthInBars : 100 // Display up to project length or 100 bars
            delegate: Item {
                x: index * timeline.barWidth
                width: timeline.barWidth
                height: parent.height

                // Bar line
                Rectangle {
                    width: 1
                    height: parent.height
                    color: "#505050"
                    anchors.left: parent.left
                }

                // Bar number
                Text {
                    text: (index + 1).toString() // Display bar number (1-indexed)
                    color: "white"
                    font.pixelSize: 14
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.leftMargin: 5
                }
            }
        }

        MouseArea {
            anchors.fill: parent
            onClicked: function (mouse) {
                if (timeline.transportModel) {
                    // Calculate new time based on click position
                    var newTimeInBars = mouse.x / timeline.barWidth;
                    // Assuming 4 beats per bar and tempoBpm is available
                    transportModel.setPositionInSeconds(newTimeInBars * 4 * 60 / transportModel.tempoBpm); // Convert bars to seconds
                }
            }
        }
    }
}
