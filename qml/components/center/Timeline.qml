import QtQuick 2.15
// import QtQuick.Controls 2.15
// import QtQuick.Layouts 1.15
// import QtQuick.Shapes 1.15

Item {
    id: timeline

    property var transportModel: null
    property var editModel: null

    // Define a property for the width of one bar in pixels
    property real barWidth: 100

    Rectangle {
        anchors.fill: parent
        color: "#282828" // Dark background for timeline
        border.color: "#404040"
        border.width: 1

        // Playhead is now handled in TrackBodyView components

        // Time markers (bars)
        Repeater {
            model: timeline.editModel ? timeline.editModel.projectLengthInBars : 100 // Display up to project length or 100 bars
            delegate: Item {
                x: index * timeline.barWidth
                width: timeline.barWidth
                height: parent.height

                // Bar line
                Rectangle {
                    width: 1
                    height: parent.height
                    color: "#505050"
                    anchors.left: parent.left
                }

                // Bar number
                Text {
                    text: (index + 1).toString() // Display bar number (1-indexed)
                    color: "white"
                    font.pixelSize: 14
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.leftMargin: 5
                }
            }
        }

        // Timeline click interaction is now handled in TrackBodyView components
    }
}
