import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// TrackView - combines TrackHeaderView, TrackBodyView, and TrackFooterView
Item {
    id: trackview

    // This component expects a TrackModel to be passed to it
    property var trackModel: null
    property var editViewModel: null
    property var transportModel: null

    // Define a property for the width of one bar in pixels (should match Timeline's barWidth)
    property real barWidth: 100 // Assuming 100 pixels per bar for consistency

    // Track height based on track type
    height: getTrackHeight()

    RowLayout {
        anchors.fill: parent
        spacing: 0

        // Track Header - fixed width, anchored to left
        TrackHeaderView {
            id: trackHeader
            trackModel: trackview.trackModel
            Layout.preferredWidth: 200
            Layout.fillHeight: true
        }

        // Track Body - scrollable content area
        TrackBodyView {
            id: trackBody
            trackModel: trackview.trackModel
            editViewModel: trackview.editViewModel
            transportModel: trackview.transportModel
            barWidth: trackview.barWidth
            Layout.fillWidth: true
            Layout.fillHeight: true
        }

        // Track Footer - fixed width, anchored to right
        TrackFooterView {
            id: trackFooter
            trackModel: trackview.trackModel
            Layout.preferredWidth: 120
            Layout.fillHeight: true
        }
    }

    // Helper function to determine track height based on track type
    function getTrackHeight() {
        if (!trackview.trackModel) return 80; // Default height

        var trackType = trackview.trackModel.trackType;
        switch (trackType) {
            case 1: // Arranger
            case 2: // Marker
            case 3: // Tempo
            case 4: // Chord
            case 5: // Master
                return 40; // Small height for global tracks
            case 0: // Audio
            case 6: // Folder
            case 7: // Automation
            default:
                return 80; // Normal height for content tracks
        }
    }
}
