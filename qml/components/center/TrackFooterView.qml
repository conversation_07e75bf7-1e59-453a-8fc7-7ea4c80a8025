import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// TrackFooterView - displays track plugins and controls
Rectangle {
    id: trackfooterview
    
    // This component expects a TrackModel to be passed to it
    property var trackModel: null
    
    // Fixed width for track footer
    width: 120
    color: "#2a2a2a" // Slightly lighter than track body
    border.color: "#404040"
    border.width: 1

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 6
        spacing: 4

        // Volume and Pan section
        RowLayout {
            Layout.fillWidth: true
            spacing: 4

            // Volume control
            Button {
                id: volumeButton
                text: "V"
                Layout.preferredWidth: 25
                Layout.preferredHeight: 25
                enabled: hasVolumeControl()
                
                onClicked: {
                    // TODO: Open volume plugin interface
                    console.log("Volume control clicked for track:", trackfooterview.trackModel ? trackfooterview.trackModel.name : "unknown");
                }

                background: Rectangle {
                    color: "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 10
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Pan control
            Button {
                id: panButton
                text: "P"
                Layout.preferredWidth: 25
                Layout.preferredHeight: 25
                enabled: hasPanControl()
                
                onClicked: {
                    // TODO: Open pan plugin interface
                    console.log("Pan control clicked for track:", trackfooterview.trackModel ? trackfooterview.trackModel.name : "unknown");
                }

                background: Rectangle {
                    color: "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 10
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Level meter indicator
            Rectangle {
                id: levelMeter
                Layout.preferredWidth: 20
                Layout.preferredHeight: 25
                color: "#1a1a1a"
                border.color: "#555555"
                border.width: 1
                radius: 2

                // Simple level meter visualization
                Rectangle {
                    id: levelIndicator
                    anchors.bottom: parent.bottom
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.margins: 2
                    height: parent.height * getLevelMeterValue()
                    color: getLevelMeterColor()
                    radius: 1

                    // Animate level changes
                    Behavior on height {
                        NumberAnimation {
                            duration: 100
                            easing.type: Easing.OutQuad
                        }
                    }
                }

                // Level meter click area
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        // TODO: Open level meter plugin interface
                        console.log("Level meter clicked for track:", trackfooterview.trackModel ? trackfooterview.trackModel.name : "unknown");
                    }
                }
            }
        }

        // Plugin access buttons
        RowLayout {
            Layout.fillWidth: true
            spacing: 2

            // Effects button
            Button {
                id: effectsButton
                text: "FX"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 20
                enabled: hasEffectsSlots()
                
                onClicked: {
                    // TODO: Open effects plugin chain
                    console.log("Effects clicked for track:", trackfooterview.trackModel ? trackfooterview.trackModel.name : "unknown");
                }

                background: Rectangle {
                    color: "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 2
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 8
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Send button
            Button {
                id: sendButton
                text: "SND"
                Layout.preferredWidth: 35
                Layout.preferredHeight: 20
                enabled: hasSendSlots()
                
                onClicked: {
                    // TODO: Open send controls
                    console.log("Send clicked for track:", trackfooterview.trackModel ? trackfooterview.trackModel.name : "unknown");
                }

                background: Rectangle {
                    color: "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 2
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 8
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }

        // Plugin indicator dots (show active plugins)
        Row {
            Layout.fillWidth: true
            spacing: 2

            Repeater {
                model: getPluginCount()
                delegate: Rectangle {
                    width: 6
                    height: 6
                    radius: 3
                    color: "#4CAF50" // Green for active plugins
                    border.color: "#2E7D32"
                    border.width: 1
                }
            }
        }
    }

    // Helper functions
    function hasVolumeControl() {
        if (!trackfooterview.trackModel) return false;
        var trackType = trackfooterview.trackModel.trackType;
        // Volume control available for Audio, Master, and Folder tracks
        return trackType === 0 || trackType === 5 || trackType === 6;
    }

    function hasPanControl() {
        if (!trackfooterview.trackModel) return false;
        var trackType = trackfooterview.trackModel.trackType;
        // Pan control available for Audio and Folder tracks
        return trackType === 0 || trackType === 6;
    }

    function hasEffectsSlots() {
        if (!trackfooterview.trackModel) return false;
        var trackType = trackfooterview.trackModel.trackType;
        // Effects available for Audio, Master, and Folder tracks
        return trackType === 0 || trackType === 5 || trackType === 6;
    }

    function hasSendSlots() {
        if (!trackfooterview.trackModel) return false;
        var trackType = trackfooterview.trackModel.trackType;
        // Sends available for Audio and Folder tracks
        return trackType === 0 || trackType === 6;
    }

    function getLevelMeterValue() {
        // TODO: Get actual level from track model
        // For now, return a simulated value
        return Math.random() * 0.8 + 0.1;
    }

    function getLevelMeterColor() {
        var level = getLevelMeterValue();
        if (level > 0.8) return "#F44336"; // Red for high levels
        if (level > 0.6) return "#FF9800"; // Orange for medium-high levels
        return "#4CAF50"; // Green for normal levels
    }

    function getPluginCount() {
        // TODO: Get actual plugin count from track model
        // For now, return a simulated count based on track type
        if (!trackfooterview.trackModel) return 0;
        var trackType = trackfooterview.trackModel.trackType;
        if (trackType === 0) return 2; // Audio tracks have volume/pan by default
        if (trackType === 5) return 1; // Master track has volume
        return 0;
    }
}
