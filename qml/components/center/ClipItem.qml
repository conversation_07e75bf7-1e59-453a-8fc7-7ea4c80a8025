import QtQuick 2.15
// import QtQuick.Controls 2.15
// import QtQuick.Layouts 1.15

Item {
    id: clipitem
    // This component expects a ClipModel to be passed to it
    property var clipModel: null
    property var editViewModel: null
    property var transportModel: null

    Rectangle {
        anchors.fill: parent
        color: "#4CAF50"
        border.color: "#388E3C"
        border.width: 1
        radius: 3

        Text {
            anchors.centerIn: parent
            text: clipitem.clipModel ? clipitem.clipModel.name : "Unnamed Clip"
            color: "white"
            font.pixelSize: 14
            elide: Text.ElideRight // Elide long names
        }
    }
}
