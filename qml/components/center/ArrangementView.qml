import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15

Item {
    id: arrangementview

    property var editModel: null
    property var editViewModel: null
    property var transportModel: null
    property real barWidth: 100
    property real projectLengthInBars: 32 // Default project length, should be calculated from actual project
    property real extraBars: 8 // Extra bars to show beyond project end

    Layout.fillWidth: true
    Layout.fillHeight: true

    Rectangle {
        anchors.fill: parent
        color: "#1e1e1e" // Dark background for arrangement view

        // Main vertical layout with timelines and track area
        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            // Top timeline - bars and beats
            RowLayout {
                Layout.fillWidth: true
                Layout.preferredHeight: 30
                spacing: 0

                // Empty space above track headers
                Rectangle {
                    Layout.preferredWidth: 200
                    Layout.fillHeight: true
                    color: "#333333"
                    border.color: "#555555"
                    border.width: 1
                }

                // Bars/beats timeline - scrollable
                ScrollView {
                    id: topTimelineScrollView
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                    ScrollBar.vertical.policy: ScrollBar.AlwaysOff

                    contentWidth: (arrangementview.projectLengthInBars + arrangementview.extraBars) * arrangementview.barWidth

                    BarsBeatsTimeline {
                        width: topTimelineScrollView.contentWidth
                        height: 30
                        barWidth: arrangementview.barWidth
                        projectLengthInBars: arrangementview.projectLengthInBars
                        extraBars: arrangementview.extraBars
                        transportModel: arrangementview.transportModel
                    }

                    // Enable mouse wheel scrolling for horizontal
                    WheelHandler {
                        acceptedDevices: PointerDevice.Mouse
                        onWheel: function(event) {
                            var delta = event.angleDelta.y / 120 * 60; // Convert wheel delta to pixels
                            var newPosition = topTimelineScrollView.ScrollBar.horizontal.position - (delta / (topTimelineScrollView.contentWidth - topTimelineScrollView.width));
                            newPosition = Math.max(0, Math.min(1, newPosition));
                            topTimelineScrollView.ScrollBar.horizontal.position = newPosition;
                        }
                    }
                }

                // Empty space above track footers
                Rectangle {
                    Layout.preferredWidth: 120
                    Layout.fillHeight: true
                    color: "#333333"
                    border.color: "#555555"
                    border.width: 1
                }
            }

            // Main track area with headers, bodies, and footers
            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 0

                // Left column - track headers (fixed)
                Rectangle {
                    Layout.preferredWidth: 200
                    Layout.fillHeight: true
                    color: "#2a2a2a"
                    border.color: "#333333"
                    border.width: 1

                    ScrollView {
                        id: headerScrollView
                        anchors.fill: parent
                        clip: true

                        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                        ScrollBar.vertical.policy: ScrollBar.AlwaysOff

                        ListView {
                            id: headerListView
                            width: headerScrollView.width
                            model: arrangementview.editModel ? arrangementview.editModel.tracks : 0
                            orientation: ListView.Vertical
                            spacing: 2
                            interactive: false // Scrolling handled by ScrollView

                            delegate: TrackHeaderView {
                                trackModel: modelData
                                width: 200
                                height: getTrackHeight(modelData)
                            }
                        }

                        // Enable mouse wheel scrolling for vertical
                        WheelHandler {
                            acceptedDevices: PointerDevice.Mouse
                            onWheel: function(event) {
                                var delta = event.angleDelta.y / 120 * 40; // Convert wheel delta to pixels
                                var newPosition = headerScrollView.ScrollBar.vertical.position - (delta / (headerListView.contentHeight - headerScrollView.height));
                                newPosition = Math.max(0, Math.min(1, newPosition));
                                headerScrollView.ScrollBar.vertical.position = newPosition;
                            }
                        }
                    }
                }

                // Center column - scrollable track bodies
                ScrollView {
                    id: trackScrollView
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                    ScrollBar.vertical.policy: ScrollBar.AlwaysOff

                    contentWidth: (arrangementview.projectLengthInBars + arrangementview.extraBars) * arrangementview.barWidth

                    ListView {
                        id: bodyListView
                        width: trackScrollView.contentWidth
                        height: trackScrollView.height
                        model: arrangementview.editModel ? arrangementview.editModel.tracks : 0
                        orientation: ListView.Vertical
                        spacing: 2
                        clip: true
                        interactive: false // Scrolling handled by parent ScrollViews

                        delegate: TrackBodyView {
                            trackModel: modelData
                            editViewModel: arrangementview.editViewModel
                            transportModel: arrangementview.transportModel
                            barWidth: arrangementview.barWidth
                            width: trackScrollView.contentWidth
                            height: getTrackHeight(modelData)
                        }
                    }

                    // Enable mouse wheel scrolling for both horizontal and vertical
                    WheelHandler {
                        acceptedDevices: PointerDevice.Mouse
                        onWheel: function(event) {
                            if (event.modifiers & Qt.ShiftModifier) {
                                // Shift + wheel = horizontal scrolling
                                var deltaX = event.angleDelta.y / 120 * 60; // Convert wheel delta to pixels
                                var newPositionX = trackScrollView.ScrollBar.horizontal.position - (deltaX / (trackScrollView.contentWidth - trackScrollView.width));
                                newPositionX = Math.max(0, Math.min(1, newPositionX));
                                trackScrollView.ScrollBar.horizontal.position = newPositionX;
                            } else {
                                // Normal wheel = vertical scrolling
                                var deltaY = event.angleDelta.y / 120 * 40; // Convert wheel delta to pixels
                                var newPositionY = trackScrollView.ScrollBar.vertical.position - (deltaY / (bodyListView.contentHeight - trackScrollView.height));
                                newPositionY = Math.max(0, Math.min(1, newPositionY));
                                trackScrollView.ScrollBar.vertical.position = newPositionY;
                            }
                        }
                    }
                }

                // Right column - track footers (fixed)
                Rectangle {
                    Layout.preferredWidth: 120
                    Layout.fillHeight: true
                    color: "#2a2a2a"
                    border.color: "#333333"
                    border.width: 1

                    ScrollView {
                        id: footerScrollView
                        anchors.fill: parent
                        clip: true

                        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                        ScrollBar.vertical.policy: ScrollBar.AlwaysOff // Synchronized with headers

                        ListView {
                            id: footerListView
                            width: footerScrollView.width
                            model: arrangementview.editModel ? arrangementview.editModel.tracks : 0
                            orientation: ListView.Vertical
                            spacing: 2
                            interactive: false // Scrolling handled by synchronized ScrollView

                            delegate: TrackFooterView {
                                trackModel: modelData
                                width: 120
                                height: getTrackHeight(modelData)
                            }
                        }

                        // Enable mouse wheel scrolling for vertical
                        WheelHandler {
                            acceptedDevices: PointerDevice.Mouse
                            onWheel: function(event) {
                                var delta = event.angleDelta.y / 120 * 40; // Convert wheel delta to pixels
                                var newPosition = footerScrollView.ScrollBar.vertical.position - (delta / (footerListView.contentHeight - footerScrollView.height));
                                newPosition = Math.max(0, Math.min(1, newPosition));
                                footerScrollView.ScrollBar.vertical.position = newPosition;
                            }
                        }
                    }
                }
            }

            // Bottom timeline - seconds
            RowLayout {
                Layout.fillWidth: true
                Layout.preferredHeight: 25
                spacing: 0

                // Empty space below track headers
                Rectangle {
                    Layout.preferredWidth: 200
                    Layout.fillHeight: true
                    color: "#333333"
                    border.color: "#555555"
                    border.width: 1
                }

                // Seconds timeline - scrollable
                ScrollView {
                    id: bottomTimelineScrollView
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                    ScrollBar.vertical.policy: ScrollBar.AlwaysOff

                    contentWidth: (arrangementview.projectLengthInBars + arrangementview.extraBars) * arrangementview.barWidth

                    SecondsTimeline {
                        width: bottomTimelineScrollView.contentWidth
                        height: 25
                        barWidth: arrangementview.barWidth
                        projectLengthInBars: arrangementview.projectLengthInBars
                        extraBars: arrangementview.extraBars
                        transportModel: arrangementview.transportModel
                    }

                    // Enable mouse wheel scrolling for horizontal
                    WheelHandler {
                        acceptedDevices: PointerDevice.Mouse
                        onWheel: function(event) {
                            var delta = event.angleDelta.y / 120 * 60; // Convert wheel delta to pixels
                            var newPosition = bottomTimelineScrollView.ScrollBar.horizontal.position - (delta / (bottomTimelineScrollView.contentWidth - bottomTimelineScrollView.width));
                            newPosition = Math.max(0, Math.min(1, newPosition));
                            bottomTimelineScrollView.ScrollBar.horizontal.position = newPosition;
                        }
                    }
                }

                // Empty space below track footers
                Rectangle {
                    Layout.preferredWidth: 120
                    Layout.fillHeight: true
                    color: "#333333"
                    border.color: "#555555"
                    border.width: 1
                }
            }
        }

        // Synchronize scrolling between all horizontal scroll views
        Connections {
            target: trackScrollView.ScrollBar.horizontal

            function onPositionChanged() {
                if (topTimelineScrollView.ScrollBar.horizontal.position !== trackScrollView.ScrollBar.horizontal.position) {
                    topTimelineScrollView.ScrollBar.horizontal.position = trackScrollView.ScrollBar.horizontal.position;
                }
                if (bottomTimelineScrollView.ScrollBar.horizontal.position !== trackScrollView.ScrollBar.horizontal.position) {
                    bottomTimelineScrollView.ScrollBar.horizontal.position = trackScrollView.ScrollBar.horizontal.position;
                }
            }
        }

        // Synchronize from top timeline to others
        Connections {
            target: topTimelineScrollView.ScrollBar.horizontal

            function onPositionChanged() {
                if (trackScrollView.ScrollBar.horizontal.position !== topTimelineScrollView.ScrollBar.horizontal.position) {
                    trackScrollView.ScrollBar.horizontal.position = topTimelineScrollView.ScrollBar.horizontal.position;
                }
                if (bottomTimelineScrollView.ScrollBar.horizontal.position !== topTimelineScrollView.ScrollBar.horizontal.position) {
                    bottomTimelineScrollView.ScrollBar.horizontal.position = topTimelineScrollView.ScrollBar.horizontal.position;
                }
            }
        }

        // Synchronize from bottom timeline to others
        Connections {
            target: bottomTimelineScrollView.ScrollBar.horizontal

            function onPositionChanged() {
                if (trackScrollView.ScrollBar.horizontal.position !== bottomTimelineScrollView.ScrollBar.horizontal.position) {
                    trackScrollView.ScrollBar.horizontal.position = bottomTimelineScrollView.ScrollBar.horizontal.position;
                }
                if (topTimelineScrollView.ScrollBar.horizontal.position !== bottomTimelineScrollView.ScrollBar.horizontal.position) {
                    topTimelineScrollView.ScrollBar.horizontal.position = bottomTimelineScrollView.ScrollBar.horizontal.position;
                }
            }
        }

        // Synchronize vertical scrolling between headers, bodies, and footers
        Connections {
            target: headerScrollView.ScrollBar.vertical

            function onPositionChanged() {
                if (footerScrollView.ScrollBar.vertical.position !== headerScrollView.ScrollBar.vertical.position) {
                    footerScrollView.ScrollBar.vertical.position = headerScrollView.ScrollBar.vertical.position;
                }
                if (trackScrollView.ScrollBar.vertical.position !== headerScrollView.ScrollBar.vertical.position) {
                    trackScrollView.ScrollBar.vertical.position = headerScrollView.ScrollBar.vertical.position;
                }
            }
        }

        // Synchronize from track bodies to headers and footers
        Connections {
            target: trackScrollView.ScrollBar.vertical

            function onPositionChanged() {
                if (headerScrollView.ScrollBar.vertical.position !== trackScrollView.ScrollBar.vertical.position) {
                    headerScrollView.ScrollBar.vertical.position = trackScrollView.ScrollBar.vertical.position;
                }
                if (footerScrollView.ScrollBar.vertical.position !== trackScrollView.ScrollBar.vertical.position) {
                    footerScrollView.ScrollBar.vertical.position = trackScrollView.ScrollBar.vertical.position;
                }
            }
        }

        // Synchronize from footers to headers and bodies
        Connections {
            target: footerScrollView.ScrollBar.vertical

            function onPositionChanged() {
                if (headerScrollView.ScrollBar.vertical.position !== footerScrollView.ScrollBar.vertical.position) {
                    headerScrollView.ScrollBar.vertical.position = footerScrollView.ScrollBar.vertical.position;
                }
                if (trackScrollView.ScrollBar.vertical.position !== footerScrollView.ScrollBar.vertical.position) {
                    trackScrollView.ScrollBar.vertical.position = footerScrollView.ScrollBar.vertical.position;
                }
            }
        }
    }

    // Helper function to determine track height based on track type
    function getTrackHeight(trackModel) {
        if (!trackModel) return 80; // Default height

        var trackType = trackModel.trackType;
        switch (trackType) {
            case 1: // Arranger
            case 2: // Marker
            case 3: // Tempo
            case 4: // Chord
            case 5: // Master
                return 40; // Small height for global tracks
            case 0: // Audio
            case 6: // Folder
            case 7: // Automation
            default:
                return 80; // Normal height for content tracks
        }
    }
}
