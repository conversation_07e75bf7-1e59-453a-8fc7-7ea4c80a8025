import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15

Item {
    id: arrangementview

    property var editModel: null
    property var editViewModel: null
    property var transportModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    Rectangle {
        anchors.fill: parent
        color: "#1e1e1e" // Dark background for arrangement view

        ListView {
            id: trackListView
            anchors.fill: parent
            model: arrangementview.editModel ? arrangementview.editModel.tracks : 0
            orientation: ListView.Vertical
            spacing: 2
            clip: true

            delegate: TrackView {
                // Pass the current TrackModel to the TrackView delegate
                trackModel: modelData
                editViewModel: arrangementview.editViewModel // Pass editViewModel
                transportModel: arrangementview.transportModel // Pass transportModel
                width: trackListView.width // TrackView takes full width of ListView
                height: 80 // Fixed height for each track for now
            }

            ScrollBar.vertical: ScrollBar {
                id: verticalScrollBar
                width: 10
                policy: ScrollBar.AsNeeded
            }
        }
    }
}
