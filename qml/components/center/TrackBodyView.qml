import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// TrackBodyView - handles the scrollable track content area with clips and playhead
Item {
    id: trackbodyview
    
    // This component expects a TrackModel to be passed to it
    property var trackModel: null
    property var editViewModel: null
    property var transportModel: null

    // Define a property for the width of one bar in pixels (should match Timeline's barWidth)
    property real barWidth: 100 // Assuming 100 pixels per bar for consistency

    Rectangle {
        anchors.fill: parent
        color: "#222222" // Darker background for tracks
        border.color: "#333333"
        border.width: 1

        // Clip display area - now takes full width since header/footer are separate
        Item {
            id: clipArea
            anchors.fill: parent
            anchors.margins: 2
            clip: true // Ensure clips don't draw outside this area

            // Playhead - moved from Timeline to TrackBodyView
            Rectangle {
                id: playhead
                width: 2
                height: parent.height
                color: "red"
                x: trackbodyview.transportModel ? (trackbodyview.transportModel.currentTime * trackbodyview.barWidth) : 0
                z: 10 // Ensure playhead is on top of clips
                visible: trackbodyview.transportModel !== null

                // Animate playhead movement
                Behavior on x {
                    NumberAnimation {
                        duration: 50; easing.type: Easing.Linear
                    }
                }
            }

            Repeater {
                model: trackbodyview.trackModel ? trackbodyview.trackModel.clips : [] // Bind to the list of clips from TrackModel
                delegate: ClipItem {
                    // Pass the current ClipModel to the ClipItem delegate
                    clipModel: modelData
                    editViewModel: trackbodyview.editViewModel // Pass editViewModel
                    transportModel: trackbodyview.transportModel // Pass transportModel
                    // Position clips based on their start time and length
                    x: clipModel ? clipModel.start * trackbodyview.barWidth : 0 // Convert start time (in bars) to pixels
                    width: clipModel ? clipModel.length * trackbodyview.barWidth : 0 // Convert length (in bars) to pixels
                    height: parent.height - 4 // Slightly less than track height for padding
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            // Mouse area for timeline interaction (clicking to set playhead position)
            MouseArea {
                anchors.fill: parent
                onClicked: function (mouse) {
                    if (trackbodyview.transportModel) {
                        // Calculate new time based on click position
                        var newTimeInBars = mouse.x / trackbodyview.barWidth;
                        // Assuming 4 beats per bar and tempoBpm is available
                        trackbodyview.transportModel.setPositionInSeconds(newTimeInBars * 4 * 60 / trackbodyview.transportModel.tempoBpm); // Convert bars to seconds
                    }
                }
            }
        }
    }
}
