import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// TrackBodyView - handles the scrollable track content area with clips and playhead
Item {
    id: trackbodyview

    // This component expects a TrackModel to be passed to it
    property var trackModel: null
    property var editViewModel: null
    property var transportModel: null

    // Define a property for the width of one bar in pixels (should match Timeline's barWidth)
    property real barWidth: 100 // Assuming 100 pixels per bar for consistency

    Rectangle {
        anchors.fill: parent
        color: "#222222" // Darker background for tracks
        border.color: "#333333"
        border.width: 1

        // Clip display area - now takes full width since header/footer are separate
        Item {
            id: clipArea
            anchors.fill: parent
            anchors.margins: 2
            clip: true // Ensure clips don't draw outside this area

            // Beat grid background
            Canvas {
                id: beatGrid
                anchors.fill: parent
                z: 1 // Behind clips but above background

                onPaint: {
                    var ctx = getContext("2d");
                    ctx.clearRect(0, 0, width, height);

                    // Grid settings
                    var beatsPerBar = 4;
                    var beatWidth = trackbodyview.barWidth / beatsPerBar;

                    // Draw vertical lines for beats
                    ctx.strokeStyle = "#333333";
                    ctx.lineWidth = 1;

                    // Beat lines (lighter)
                    for (var beat = 0; beat < width / beatWidth; beat++) {
                        var x = beat * beatWidth;
                        if (beat % beatsPerBar !== 0) { // Don't draw on bar lines
                            ctx.beginPath();
                            ctx.moveTo(x, 0);
                            ctx.lineTo(x, height);
                            ctx.stroke();
                        }
                    }

                    // Bar lines (stronger)
                    ctx.strokeStyle = "#555555";
                    ctx.lineWidth = 2;
                    for (var bar = 0; bar < width / trackbodyview.barWidth; bar++) {
                        var barX = bar * trackbodyview.barWidth;
                        ctx.beginPath();
                        ctx.moveTo(barX, 0);
                        ctx.lineTo(barX, height);
                        ctx.stroke();
                    }
                }

                // Redraw when barWidth changes
                Connections {
                    target: trackbodyview
                    function onBarWidthChanged() {
                        beatGrid.requestPaint();
                    }
                }
            }

            // Playhead - moved from Timeline to TrackBodyView
            Rectangle {
                id: playhead
                width: 2
                height: parent.height
                color: "red"
                x: trackbodyview.transportModel ? (trackbodyview.transportModel.currentTime * trackbodyview.barWidth) : 0
                z: 20 // Ensure playhead is on top of everything
                visible: trackbodyview.transportModel !== null

                // Animate playhead movement
                Behavior on x {
                    NumberAnimation {
                        duration: 50; easing.type: Easing.Linear
                    }
                }
            }

            Repeater {
                model: trackbodyview.trackModel ? trackbodyview.trackModel.clips : [] // Bind to the list of clips from TrackModel
                delegate: ClipItem {
                    // Pass the current ClipModel to the ClipItem delegate
                    clipModel: modelData
                    editViewModel: trackbodyview.editViewModel // Pass editViewModel
                    transportModel: trackbodyview.transportModel // Pass transportModel
                    // Position clips based on their start time and length
                    x: clipModel ? clipModel.start * trackbodyview.barWidth : 0 // Convert start time (in bars) to pixels
                    width: clipModel ? clipModel.length * trackbodyview.barWidth : 0 // Convert length (in bars) to pixels
                    height: parent.height - 4 // Slightly less than track height for padding
                    anchors.verticalCenter: parent.verticalCenter
                    z: 10 // Above grid, below playhead
                }
            }

            // Mouse area for timeline interaction (clicking to set playhead position)
            MouseArea {
                anchors.fill: parent
                onClicked: function (mouse) {
                    if (trackbodyview.transportModel) {
                        // Calculate new time based on click position
                        var newTimeInBars = mouse.x / trackbodyview.barWidth;
                        // Assuming 4 beats per bar and tempoBpm is available
                        trackbodyview.transportModel.setPositionInSeconds(newTimeInBars * 4 * 60 / trackbodyview.transportModel.tempoBpm); // Convert bars to seconds
                    }
                }
            }
        }
    }
}
