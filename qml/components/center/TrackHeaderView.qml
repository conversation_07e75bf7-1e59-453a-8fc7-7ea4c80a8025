import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// TrackHeaderView - displays track name, type, and control buttons
Rectangle {
    id: trackheaderview

    // This component expects a TrackModel to be passed to it
    property var trackModel: null

    // Fixed width for track header
    width: 200
    color: "#2a2a2a" // Slightly lighter than track body
    border.color: "#404040"
    border.width: 1

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4

        // Track name and type row
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            // Track name
            Text {
                text: trackheaderview.trackModel ? trackheaderview.trackModel.name : "Unnamed Track"
                color: "white"
                font.pixelSize: 14
                font.bold: true
                Layout.fillWidth: true
                elide: Text.ElideRight
            }

            // Track type indicator
            Rectangle {
                Layout.preferredWidth: 60
                Layout.preferredHeight: 20
                color: getTrackTypeColor()
                border.color: "#555555"
                border.width: 1
                radius: 3

                Text {
                    anchors.centerIn: parent
                    text: getTrackTypeText()
                    color: "white"
                    font.pixelSize: 10
                    font.bold: true
                }
            }
        }

        // Control buttons row - only show for content tracks
        RowLayout {
            Layout.fillWidth: true
            spacing: 4
            visible: isContentTrack()

            // Input monitoring button
            Button {
                id: inputMonitoringButton
                text: "I"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 25
                enabled: isAudioOrMidiTrack()
                checkable: true
                checked: trackheaderview.trackModel ? trackheaderview.trackModel.inputMonitoring : false

                onClicked: {
                    if (trackheaderview.trackModel) {
                        trackheaderview.trackModel.inputMonitoring = checked;
                    }
                }

                background: Rectangle {
                    color: parent.checked ? "#4CAF50" : "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 12
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Record arming button
            Button {
                id: recordArmButton
                text: "A"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 25
                enabled: isAudioOrMidiTrack()
                checkable: true
                checked: trackheaderview.trackModel ? trackheaderview.trackModel.recordArmed : false

                onClicked: {
                    if (trackheaderview.trackModel) {
                        trackheaderview.trackModel.recordArmed = checked;
                    }
                }

                background: Rectangle {
                    color: parent.checked ? "#F44336" : "#333333"
                    border.color: parent.enabled ? "#555555" : "#222222"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: parent.enabled ? "white" : "#666666"
                    font.pixelSize: 12
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Solo button
            Button {
                id: soloButton
                text: "S"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 25
                checkable: true
                checked: trackheaderview.trackModel ? trackheaderview.trackModel.solo : false

                onClicked: {
                    if (trackheaderview.trackModel) {
                        trackheaderview.trackModel.solo = checked;
                    }
                }

                background: Rectangle {
                    color: parent.checked ? "#FF9800" : "#333333"
                    border.color: "#555555"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 12
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Mute button
            Button {
                id: muteButton
                text: "M"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 25
                checkable: true
                checked: trackheaderview.trackModel ? trackheaderview.trackModel.muted : false

                onClicked: {
                    if (trackheaderview.trackModel) {
                        trackheaderview.trackModel.muted = checked;
                    }
                }

                background: Rectangle {
                    color: parent.checked ? "#9C27B0" : "#333333"
                    border.color: "#555555"
                    border.width: 1
                    radius: 3
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 12
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }

    // Helper functions
    function getTrackTypeText() {
        if (!trackheaderview.trackModel) return "UNKNOWN";

        var trackType = trackheaderview.trackModel.trackType;
        switch (trackType) {
            case 0:
                return "AUDIO";
            case 1:
                return "ARRANGER";
            case 2:
                return "MARKER";
            case 3:
                return "TEMPO";
            case 4:
                return "CHORD";
            case 5:
                return "MASTER";
            case 6:
                return "FOLDER";
            case 7:
                return "AUTO";
            default:
                return "UNKNOWN";
        }
    }

    function getTrackTypeColor() {
        if (!trackheaderview.trackModel) return "#666666";

        var trackType = trackheaderview.trackModel.trackType;
        switch (trackType) {
            case 0:
                return "#4CAF50"; // Audio - Green
            case 1:
                return "#2196F3"; // Arranger - Blue
            case 2:
                return "#FF9800"; // Marker - Orange
            case 3:
                return "#9C27B0"; // Tempo - Purple
            case 4:
                return "#F44336"; // Chord - Red
            case 5:
                return "#607D8B"; // Master - Blue Grey
            case 6:
                return "#795548"; // Folder - Brown
            case 7:
                return "#009688"; // Automation - Teal
            default:
                return "#666666"; // Unknown - Grey
        }
    }

    function isContentTrack() {
        if (!trackheaderview.trackModel) return false;
        var trackType = trackheaderview.trackModel.trackType;
        // Content tracks: Audio, Folder, Automation (tracks that contain clips/content)
        return trackType === 0 || trackType === 6 || trackType === 7;
    }

    function isAudioOrMidiTrack() {
        if (!trackheaderview.trackModel) return false;
        var trackType = trackheaderview.trackModel.trackType;
        return trackType === 0; // Audio track (MIDI would be a separate type if implemented)
    }
}
