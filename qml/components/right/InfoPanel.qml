import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: infopanel

    Layout.fillWidth: true
    Layout.preferredHeight: 50

    property var editModel: null
    property var editViewModel: null

    // property alias infoText: infoTextLabel.text // Removed as text will be dynamically generated

    Rectangle {
        anchors.fill: parent
        color: "#282828" // Dark background
        border.color: "#404040"
        border.width: 1
        radius: 3

        Text {
            id: infoTextLabel
            anchors.fill: parent
            anchors.margins: 5
            text: {
                if (infopanel.editModel) {
                    return "Project: " + infopanel.editModel.name + " | Tracks: " + infopanel.editModel.tracks.count;
                }
                return "Project Info: N/A";
            }
            color: "lightgray"
            font.pixelSize: 12
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
}
