import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: helptooltip

    Layout.fillWidth: true
    Layout.preferredHeight: 50

    property alias helpText: helpTextLabel.text

    Rectangle {
        anchors.fill: parent
        color: "#282828" // Dark background
        border.color: "#404040"
        border.width: 1
        radius: 3

        Text {
            id: helpTextLabel
            anchors.fill: parent
            anchors.margins: 5
            text: "Hover over an element for help..." // Default text
            color: "lightgray"
            font.pixelSize: 12
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
}
