import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Item {
    id: tempoeditor

    property var transportModel: null

    ColumnLayout {
        anchors.fill: parent
        spacing: 5
        Layout.fillWidth: true
        Layout.fillHeight: true

        Label {
            text: "Tempo (BPM)"
            Layout.alignment: Qt.AlignHCenter
            color: "white"
        }

        SpinBox {
            id: tempoSpinBox
            Layout.fillWidth: true
            Layout.fillHeight: true
            value: tempoeditor.transportModel ? tempoeditor.transportModel.tempoBpm : 120
            from: 20
            to: 300
            stepSize: 1
            editable: true
            onValueChanged: {
                if (tempoeditor.transportModel) {
                    tempoeditor.transportModel.setTempoBpm(value);
                }
            }
        }
    }
}
