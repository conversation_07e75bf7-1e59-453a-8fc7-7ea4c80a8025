import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1 // For FileDialog
import "../dialogs"

Item {
    id: mainmenu

    property var engineModel: null
    property var projectManagerModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    MenuBar {
        Menu {
            title: "File"

            MenuItem {
                text: "New Edit..."
                onTriggered: {
                    newEditDialog.open();
                }
            }

            MenuItem {
                text: "Open Edit..."
                onTriggered: openEditDialog.open()
            }

            MenuItem {
                text: "Save"
                enabled: mainmenu.projectManagerModel && mainmenu.projectManagerModel.projectPath !== "" // Enable only if a project is loaded/saved
                onTriggered: {
                    if (mainmenu.projectManagerModel && mainmenu.projectManagerModel.projectPath !== "") {
                        projectManagerModel.saveProject(projectManagerModel.projectPath);
                    } else {
                        saveAsFileDialog.open(); // If no path, prompt for Save As
                    }
                }
            }

            MenuItem {
                text: "Save As..."
                onTriggered: saveAsFileDialog.open()
            }

            MenuSeparator {
            }

            MenuItem {
                text: "Exit"
                onTriggered: Qt.quit()
            }
        }
        // TODO: Add other menus like Edit, View, Help etc.
    }

    // New Edit Dialog
    NewEditDialog {
        id: newEditDialog
        engineModel: mainmenu.engineModel
    }

    // Open Edit Dialog
    FileDialog {
        id: openEditDialog
        title: "Open Edit"
        folder: StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
        nameFilters: ["Tracktion Edit Files (*.tracktionedit)", "All files (*)"]
        onAccepted: {
            if (!mainmenu.engineModel) {
                console.error("MainMenu: No engineModel available for opening edit!")
                return;
            }
            try {
                var selectedFile = openEditDialog.file.toString();
                // Remove file:// prefix if present
                if (selectedFile.startsWith("file://")) {
                    selectedFile = selectedFile.substring(7);
                }
                console.log("MainMenu: Opening edit with path:", selectedFile);
                mainmenu.engineModel.createOrLoadEdit(selectedFile);
            } catch (e) {
                console.error("MainMenu: Error opening edit:", e);
            }
        }
        onRejected: {
            console.log("Open edit dialog cancelled")
        }
    }

    FileDialog {
        id: saveAsFileDialog
        title: "Save Project As"
        folder: StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
        nameFilters: ["XML Project files (*.xml)", "All files (*)"]
        // selectExisting: false // Allow creating new files
        onAccepted: {
            console.log("Selected file to save: " + saveAsFileDialog.file)
            if (mainmenu.projectManagerModel) {
                mainmenu.projectManagerModel.saveProject(saveAsFileDialog.file);
                mainmenu.projectManagerModel.setProjectPath(saveAsFileDialog.file); // Update project path after saving
            }
        }
        onRejected: {
            console.log("Save As dialog cancelled")
        }
    }
}
