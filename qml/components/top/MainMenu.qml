import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1 // For FileDialog

Item {
    id: mainmenu

    property var engineModel: null
    property var projectManagerModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    MenuBar {
        Menu {
            title: "File"

            MenuItem {
                text: "New"
                onTriggered: {
                    // TODO: Implement new project logic in EngineModel
                    console.log("New project triggered");
                }
            }

            MenuItem {
                text: "Open..."
                onTriggered: openFileDialog.open()
            }

            MenuItem {
                text: "Save"
                enabled: mainmenu.projectManagerModel && mainmenu.projectManagerModel.projectPath !== "" // Enable only if a project is loaded/saved
                onTriggered: {
                    if (mainmenu.projectManagerModel && mainmenu.projectManagerModel.projectPath !== "") {
                        projectManagerModel.saveProject(projectManagerModel.projectPath);
                    } else {
                        saveAsFileDialog.open(); // If no path, prompt for Save As
                    }
                }
            }

            MenuItem {
                text: "Save As..."
                onTriggered: saveAsFileDialog.open()
            }

            MenuSeparator {
            }

            MenuItem {
                text: "Exit"
                onTriggered: Qt.quit()
            }
        }
        // TODO: Add other menus like Edit, View, Help etc.
    }

    FileDialog {
        id: openFileDialog
        title: "Open Project"
        folder: StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
        nameFilters: ["XML Project files (*.xml)", "All files (*)"]
        onAccepted: {
            console.log("Selected file to open: " + openFileDialog.file)
            if (mainmenu.projectManagerModel) {
                mainmenu.projectManagerModel.loadProject(openFileDialog.file);
            }
        }
        onRejected: {
            console.log("Open dialog cancelled")
        }
    }

    FileDialog {
        id: saveAsFileDialog
        title: "Save Project As"
        folder: StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
        nameFilters: ["XML Project files (*.xml)", "All files (*)"]
        // selectExisting: false // Allow creating new files
        onAccepted: {
            console.log("Selected file to save: " + saveAsFileDialog.file)
            if (mainmenu.projectManagerModel) {
                mainmenu.projectManagerModel.saveProject(saveAsFileDialog.file);
                mainmenu.projectManagerModel.setProjectPath(saveAsFileDialog.file); // Update project path after saving
            }
        }
        onRejected: {
            console.log("Save As dialog cancelled")
        }
    }
}
