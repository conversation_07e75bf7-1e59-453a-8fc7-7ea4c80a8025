import QtQuick 2.15
import QtQuick.Controls 2.15

import QtQuick.Layouts 1.15

Item {
    id: transportcontrols
    property var transportModel: null

    RowLayout {
        anchors.fill: parent
        spacing: 5
        Layout.fillWidth: true
        Layout.fillHeight: true

        Button {
            text: "<<"
            enabled: transportcontrols.transportModel !== null
            onClicked: {
                if (transportcontrols.transportModel) {
                    transportcontrols.transportModel.rewind();
                }
            }
            Layout.fillWidth: true
            Layout.fillHeight: true
        }

        Button {
            text: ">>"
            enabled: transportcontrols.transportModel !== null
            onClicked: {
                if (transportcontrols.transportModel) {
                    transportcontrols.transportModel.fastForward();
                }
            }
            Layout.fillWidth: true
            Layout.fillHeight: true
        }

        Button {
            text: (transportcontrols.transportModel && transportcontrols.transportModel.isPlaying) ? "Pause" : "Play"
            enabled: transportcontrols.transportModel !== null
            onClicked: {
                if (transportcontrols.transportModel) {
                    transportcontrols.transportModel.togglePlayPause();
                }
            }
            Layout.fillWidth: true
            Layout.fillHeight: true
        }

        Button {
            text: "Stop"
            enabled: transportcontrols.transportModel !== null
            onClicked: {
                if (transportcontrols.transportModel) {
                    transportcontrols.transportModel.stop();
                }
            }
            Layout.fillWidth: true
            Layout.fillHeight: true
        }

        Button {
            text: (transportcontrols.transportModel && transportcontrols.transportModel.isRecording) ? "Abort" : "Record"
            enabled: transportcontrols.transportModel !== null
            onClicked: {
                if (transportcontrols.transportModel) {
                    transportcontrols.transportModel.toggleRecord();
                }
            }
            Layout.fillWidth: true
            Layout.fillHeight: true
        }
    }
}
