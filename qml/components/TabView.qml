// TabView.qml
import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    id: tabView
    property int currentIndex: 0

    Column {
        width: parent.width
        height: parent.height
        spacing: 0

        // Tab Bar
        Row {
            id: tabBar
            height: 36
            width: parent.width
            spacing: 2
            padding: 4

            Repeater {
                model: tabStack.children
                delegate: But<PERSON> {
                    text: modelData.title
                    checkable: true
                    checked: model.index === tabView.currentIndex
                    onClicked: tabView.currentIndex = model.index
                    width: 120
                    height: 28
                    font.bold: checked
                }
            }
        }

        // Tab Content
        Loader {
            id: tabLoader
            sourceComponent: tabStack.children.length > 0 ? tabStack.children[tabView.currentIndex].contentComponent : null
            width: parent.width
            height: parent.height - tabBar.height
        }

        // Hidden Stack to manage Tab components
        Item {
            id: tabStack
            visible: false
        }
    }

    // API to add tabs
    function addTab(tabComponent) {
        tabStack.children.push(tabComponent)
    }
}
