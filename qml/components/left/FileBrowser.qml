import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: filebrowser

    property var projectManagerModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        Text {
            text: "File Browser"
            color: "white"
            font.pixelSize: 16
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            horizontalAlignment: Text.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#282828"
            border.color: "#404040"
            border.width: 1

            // Placeholder for file system view
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 2

                Text {
                    text: "(TODO: Expose QFileSystemModel from C++ and bind to ListView/TreeView)"
                    color: "lightgray"
                    font.pixelSize: 12
                    Layout.fillWidth: true
                    wrapMode: Text.WordWrap
                }

                // Example placeholder folder/file
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    color: "#444444"
                    border.color: "#666666"
                    border.width: 1
                    Text {
                        anchors.centerIn: parent
                        text: "Folder/File Item"
                        color: "white"
                        font.pixelSize: 12
                    }
                }
                // TODO: Implement actual file system browsing
            }
        }
    }
}
