import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: pluginbrowser

    property var engineModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        Text {
            text: "Plugin Browser"
            color: "white"
            font.pixelSize: 16
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            horizontalAlignment: Text.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#282828"
            border.color: "#404040"
            border.width: 1

            // Placeholder for list of available plugins
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 2

                Text {
                    text: "(TODO: Bind to a PluginManagerModel.availablePlugins or similar)"
                    color: "lightgray"
                    font.pixelSize: 12
                    Layout.fillWidth: true
                    wrapMode: Text.WordWrap
                }

                // Example placeholder plugin
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    color: "#444444"
                    border.color: "#666666"
                    border.width: 1
                    Text {
                        anchors.centerIn: parent
                        text: "Available Plugin"
                        color: "white"
                        font.pixelSize: 12
                    }
                }
                // TODO: Use Repeater/ListView for actual plugins
            }
        }
    }
}
