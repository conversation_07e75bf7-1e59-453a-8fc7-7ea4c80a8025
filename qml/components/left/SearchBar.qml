import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15

Item {
    id: searchbar

    Layout.fillWidth: true
    Layout.preferredHeight: 40

    property alias text: searchField.text

    TextField {
        id: searchField
        anchors.fill: parent
        anchors.margins: 5
        placeholderText: "Search..."
        onTextChanged: searchbar.textChanged(text)

        background: Rectangle {
            color: "#333333"
            border.color: "#555555"
            border.width: 1
            radius: 5
        }

        color: "white"
        font.pixelSize: 14
        anchors.rightMargin: clearButton.implicitWidth + 5
    }

    // Clear button
    Button {
        id: clearButton
        implicitWidth: 24
        implicitHeight: 24
        anchors.right: searchField.right
        anchors.verticalCenter: searchField.verticalCenter
        anchors.rightMargin: searchField.anchors.margins // Align with the right margin of the text field
        visible: searchField.text.length > 0
        flat: true
        contentItem: Text {
            text: "X"
            font.pixelSize: 16
            color: "lightgray"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
        onClicked: searchField.clear()
    }
}
