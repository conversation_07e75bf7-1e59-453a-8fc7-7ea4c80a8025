import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ColumnLayout {
    id: miniDawControls
    Layout.fillWidth: true
    Layout.preferredHeight: 60 // Two rows of buttons, 30 height each

    property var engineModel: null
    property var editModel: null
    property var selectionManagerModel: null
    property var editViewModel: null
    property var transportModel: null

    // First row of buttons
    RowLayout {
        Layout.fillWidth: true
        Layout.preferredHeight: 30
        spacing: 2

        Button {
            text: "Open Edit Folder"
            Layout.fillWidth: true
            onClicked: {
                engineModel.showEditFile();
            }
        }

        Button {
            text: "Add Audio Track"
            Layout.fillWidth: true
            onClicked: {
                if (editModel) editModel.addNewAudioTrack();
            }
        }

        Button {
            text: "Clear Tracks"
            Layout.fillWidth: true
            onClicked: {
                if (editModel) editModel.clearAllTracks();
            }
        }

        Button {
            id: deleteButton
            text: "Delete"
            Layout.fillWidth: true
            enabled: editModel ? editModel.isItemSelected : false
            onClicked: {
                if (editModel) editModel.deleteSelectedItem();
            }
        }

        Button {
            text: "Undo"
            Layout.fillWidth: true
            // enabled: editModel ? editModel.canUndo() : false
            onClicked: {
                if (editModel) editModel.undo();
            }
        }

        Button {
            text: "Redo"
            Layout.fillWidth: true
            // enabled: editModel ? editModel.canRedo() : false
            onClicked: {
                if (editModel) editModel.redo();
            }
        }
    }

    // Second row for additional buttons and label
    RowLayout {
        Layout.fillWidth: true
        Layout.preferredHeight: 30
        spacing: 2

        CheckBox {
            id: showWaveformButton
            text: "Show Waveforms"
            Layout.fillWidth: true
            checked: editModel ? editModel.drawWaveforms : false
            onClicked: {
                if (editModel) editModel.setDrawWaveforms(checked);
            }
        }

        Button {
            text: "Move to first note"
            Layout.fillWidth: true
            onClicked: {
                if (editModel) editModel.moveToFirstNote();
            }
        }

        Button {
            text: "Create MIDI Clip"
            Layout.fillWidth: true
            onClicked: {
                if (editModel) editModel.createMidiClip();
            }
        }

        Label {
            id: editNameLabel
            text: engineModel ? engineModel.editName : "No Edit Loaded"
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }


}
