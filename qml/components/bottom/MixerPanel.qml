import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15

Item {
    id: mixerpanel

    property var editModel: null
    property var deviceManagerModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    Rectangle {
        anchors.fill: parent
        color: "#1e1e1e" // Dark background for mixer panel

        ListView {
            id: channelStripListView
            anchors.fill: parent
            model: mixerpanel.editModel ? mixerpanel.editModel.tracks : 0
            orientation: ListView.Horizontal
            spacing: 5
            clip: true

            delegate: ChannelStrip {
                // Pass the current TrackModel to the ChannelStrip delegate
                trackModel: modelData
                deviceManagerModel: mixerpanel.deviceManagerModel
                height: parent.height
                // width is set by ChannelStrip's Layout.preferredWidth
            }

            ScrollBar.horizontal: ScrollBar {
                id: horizontalScrollBar
                height: 10
                policy: ScrollBar.AsNeeded
            }
        }
    }
}
