import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: devicechain
    // This component might receive a TrackModel or DeviceChainModel
    property var trackModel: null

    Layout.fillWidth: true
    Layout.fillHeight: true

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        Text {
            text: "Device Chain"
            color: "white"
            font.pixelSize: 16
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            horizontalAlignment: Text.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#282828" // Dark background
            border.color: "#404040"
            border.width: 1

            // Placeholder for list of devices/plugins
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 2

                Text {
                    text: "(TODO: Bind to trackModel.deviceChain or similar)"
                    color: "lightgray"
                    font.pixelSize: 12
                    Layout.fillWidth: true
                    wrapMode: Text.WordWrap
                }

                // Example placeholder device
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    color: "#444444"
                    border.color: "#666666"
                    border.width: 1
                    Text {
                        anchors.centerIn: parent
                        text: "Device/Plugin Slot"
                        color: "white"
                        font.pixelSize: 12
                    }
                }
                // TODO: Use Repeater/ListView for actual devices
            }
        }
    }
}
