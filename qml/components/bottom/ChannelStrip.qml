import QtQuick 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Controls 2.15

Item {
    id: channelstrip
    // This component expects a TrackModel to be passed to it
    property var trackModel: null
    property var deviceManagerModel: null

    Layout.fillHeight: true
    Layout.preferredWidth: 80

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        // Track Name
        Text {
            text: channelstrip.trackModel ? channelstrip.trackModel.name : "Unnamed"
            color: "white"
            font.pixelSize: 14
            horizontalAlignment: Text.AlignHCenter
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            elide: Text.ElideRight
        }

        // Placeholder for Volume Fader
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#333333"
            border.color: "#555555"
            border.width: 1

            Text {
                anchors.centerIn: parent
                text: "Volume Fader\n(TODO: Add volume to TrackModel)"
                color: "lightgray"
                font.pixelSize: 10
                horizontalAlignment: Text.AlignHCenter
                wrapMode: Text.WordWrap
            }
            // TODO: Replace with a Slider bound to trackModel.volume (once available)
            // Slider {
            //     id: volumeFader
            //     orientation: Qt.Vertical
            //     anchors.fill: parent
            //     from: 0.0
            //     to: 1.0
            //     value: trackModel.volume
            //     onValueChanged: trackModel.setVolume(value)
            // }
        }

        // TODO: Add Mute/Solo buttons, Pan knob, etc.
    }
}
