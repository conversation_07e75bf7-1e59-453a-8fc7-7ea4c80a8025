import QtQuick
import Qt.labs.platform 1.1

FileDialog {
    id: newEditDialog
    property var engineModel

    onEngineModelChanged: {
        console.log("NewEditDialog engineModel changed to:", engineModel)
    }

    Component.onCompleted: {
        console.log("NewEditDialog initialized with engineModel:", engineModel)
    }

    title: "New Edit"
    folder: "file://" + StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
    nameFilters: ["Tracktion Edit Files (*.tracktionedit)"]
    defaultSuffix: "tracktionedit"
    fileMode: FileDialog.SaveFile

    onAccepted: {
        if (!engineModel) {
            console.error("NewEditDialog: No engineModel available in onAccepted!")
            return;
        }
        try {
            var selectedFile = newEditDialog.file.toString();
            // Remove file:// prefix if present
            if (selectedFile.startsWith("file://")) {
                selectedFile = selectedFile.substring(7);
            }

            console.log("NewEditDialog: Creating edit with path:", selectedFile);
            engineModel.createOrLoadEdit(selectedFile);
        } catch (e) {
            console.error("NewEditDialog: Error in onAccepted:", e);
        }
    }
}
