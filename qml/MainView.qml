import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// Importing panel components
import "panels"
import "components"

ApplicationWindow {
    id: root
    width: 1600
    height: 900
    visible: true
    title: "QML DAW Main View"

    Rectangle {
        anchors.fill: parent
        color: "#1e1e1e"

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            TopPanel {
                Layout.fillWidth: true
                Layout.preferredHeight: 140
                transportModel: AppInstance.getAppModel().transportModel
                projectManagerModel: AppInstance.getAppModel().projectManagerModel
                engineModel: AppInstance.getAppModel().engineModel
                editModel: AppInstance.getAppModel().editModel
                selectionManagerModel: AppInstance.getAppModel().selectionManagerModel
                editViewModel: AppInstance.getAppModel().editViewModel
            }

            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 0

                LeftPanel {
                    id: leftPanel
                    Layout.preferredWidth: 250
                    Layout.minimumWidth: 100
                    Layout.fillHeight: true
                    projectManagerModel: AppInstance.getAppModel().projectManagerModel
                    engineModel: AppInstance.getAppModel().engineModel
                }

                PanelSplitter {
                    id: leftCenterSplitter
                    orientation: Qt.Vertical
                    Layout.preferredWidth: 5
                    Layout.fillHeight: true
                    onPositionChanged: function (delta) {
                        let newLeftWidth = leftPanel.Layout.preferredWidth + delta
                        let newCenterColumnWidth = centerBottomColumn.Layout.preferredWidth - delta

                        // Enforce minimums
                        if (newLeftWidth >= leftPanel.Layout.minimumWidth && newCenterColumnWidth >= centerBottomColumn.Layout.minimumWidth) {
                            leftPanel.Layout.preferredWidth = newLeftWidth
                            centerBottomColumn.Layout.preferredWidth = newCenterColumnWidth
                        }
                    }
                }

                ColumnLayout {
                    id: centerBottomColumn
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.minimumWidth: 200
                    spacing: 0

                    CenterPanel {
                        id: centerPanel
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.minimumHeight: 100
                        transportModel: AppInstance.getAppModel().transportModel
                        editModel: AppInstance.getAppModel().editModel
                        editViewModel: AppInstance.getAppModel().editViewModel
                    }

                    PanelSplitter {
                        id: centerBottomSplitter
                        orientation: Qt.Horizontal
                        Layout.preferredHeight: 5
                        Layout.fillWidth: true
                        onPositionChanged: function (delta) {
                            const newCenterHeight = centerPanel.Layout.preferredHeight + delta
                            const newBottomHeight = bottomPanel.Layout.preferredHeight - delta

                            // Enforce minimums
                            if (newCenterHeight >= centerPanel.Layout.minimumHeight && newBottomHeight >= bottomPanel.Layout.minimumHeight) {
                                centerPanel.Layout.preferredHeight = newCenterHeight
                                bottomPanel.Layout.preferredHeight = newBottomHeight
                            }
                        }
                    }

                    BottomPanel {
                        id: bottomPanel
                        Layout.fillWidth: true
                        Layout.preferredHeight: 200
                        Layout.minimumHeight: 100
                        editModel: AppInstance.getAppModel().editModel
                        deviceManagerModel: AppInstance.getAppModel().deviceManagerModel
                    }
                }

                PanelSplitter {
                    id: centerRightSplitter
                    orientation: Qt.Vertical
                    Layout.preferredWidth: 5
                    Layout.fillHeight: true
                    onPositionChanged: function (delta) {
                        let newCenterColumnWidth = centerBottomColumn.Layout.preferredWidth + delta
                        let newRightWidth = rightPanel.Layout.preferredWidth - delta

                        // Enforce minimums
                        if (newCenterColumnWidth >= centerBottomColumn.Layout.minimumWidth && newRightWidth >= rightPanel.Layout.minimumWidth) {
                            centerBottomColumn.Layout.preferredWidth = newCenterColumnWidth
                            rightPanel.Layout.preferredWidth = newRightWidth
                        }
                    }
                }

                RightPanel {
                    id: rightPanel
                    Layout.preferredWidth: 300
                    Layout.minimumWidth: 100
                    Layout.fillHeight: true
                    editModel: AppInstance.getAppModel().editModel
                    editViewModel: AppInstance.getAppModel().editViewModel
                    deviceManagerModel: AppInstance.getAppModel().deviceManagerModel
                }
            }
        }
    }
}
