import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../components/left"

PanelContainer {
    id: leftPanel
    Layout.fillHeight: true
    Layout.preferredWidth: 150

    property var projectManagerModel: null
    property var engineModel: null

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        SearchBar {
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            // Assuming SearchBar might need a model for filtering/searching
            // For now, no model passed explicitly, will check its content later
        }

        TabView {
            Layout.fillWidth: true
            Layout.fillHeight: true

            Component.onCompleted: {
                addTab(favoritesTab);
                addTab(fileBrowserTab);
                addTab(pluginBrowserTab);
            }

            Tab {
                id: favoritesTab
                title: "Favorites"
                property Component contentComponent: Component
                {
                    FavoritesPanel {
                        anchors.fill: parent
                        // Assuming FavoritesPanel might need a model
                    }
                }
            }

            Tab {
                id: fileBrowserTab
                title: "Files"
                property Component contentComponent: Component
                {
                    FileBrowser {
                        anchors.fill: parent
                        projectManagerModel: leftPanel.projectManagerModel
                    }
                }
            }

            Tab {
                id: pluginBrowserTab
                title: "Plugins"
                property Component contentComponent: Component
                {
                    PluginBrowser {
                        anchors.fill: parent
                        engineModel: leftPanel.engineModel
                    }
                }
            }
        }
    }
}
