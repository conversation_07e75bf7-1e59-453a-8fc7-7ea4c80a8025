import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../components"
import "../components/top"
// Removed explicit import for MiniDawControls.qml as it should be covered by "../components"

PanelContainer {
    id: topPanel
    Layout.fillWidth: true
    Layout.preferredHeight: 100

    property var transportModel: null
    property var projectManagerModel: null
    property var engineModel: null
    property var editModel: null
    property var selectionManagerModel: null
    property var editViewModel: null

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        MiniDawControls {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            engineModel: topPanel.engineModel
            editModel: topPanel.editModel
            selectionManagerModel: topPanel.selectionManagerModel
            editViewModel: topPanel.editViewModel
            transportModel: topPanel.transportModel
        }

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            spacing: 5

            ProjectInfo {
                Layout.fillWidth: true; Layout.fillHeight: true
                projectManagerModel: topPanel.projectManagerModel
                engineModel: topPanel.engineModel
            }
            MainMenu {
                Layout.fillWidth: true; Layout.fillHeight: true
                projectManagerModel: topPanel.projectManagerModel
                engineModel: topPanel.engineModel
            }
            ClockDisplay {
                Layout.fillWidth: true; Layout.fillHeight: true
                transportModel: topPanel.transportModel
            }
            TempoEditor {
                Layout.fillWidth: true; Layout.fillHeight: true
                transportModel: topPanel.transportModel
            }
            TransportControls {
                Layout.fillWidth: true; Layout.fillHeight: true
                transportModel: topPanel.transportModel
            }
        }
    }
}
