import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../components/center"

PanelContainer {
    id: centerPanel
    Layout.fillWidth: true
    Layout.fillHeight: true

    property var transportModel: null
    property var editModel: null
    property var editViewModel: null

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        Timeline {
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            transportModel: centerPanel.transportModel
            editModel: centerPanel.editModel
        }

        ArrangementView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            editModel: centerPanel.editModel
            editViewModel: centerPanel.editViewModel
            transportModel: centerPanel.transportModel
        }
        // AutomationLane, ClipItem, and TrackView are not direct children of CenterPanel
        // They will be integrated within ArrangementView or TrackView
    }
}
