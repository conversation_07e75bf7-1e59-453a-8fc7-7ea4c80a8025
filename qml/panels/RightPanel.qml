import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../components/right"

PanelContainer {
    id: rightPanel
    Layout.fillHeight: true
    Layout.preferredWidth: 150

    property var editModel: null
    property var editViewModel: null
    property var deviceManagerModel: null

    ColumnLayout {
        anchors.fill: parent
        spacing: 5

        // InfoPanel and HelpTooltip might be smaller, persistent displays
        InfoPanel {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            editModel: rightPanel.editModel
            editViewModel: rightPanel.editViewModel
        }
        HelpTooltip {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            // HelpTooltip likely doesn't need a model directly, but might need context from editViewModel
            // For now, no model passed explicitly, will check its content later
        }

        // InspectorPanel as the main content area
        InspectorPanel {
            Layout.fillWidth: true
            Layout.fillHeight: true
            editModel: rightPanel.editModel
            editViewModel: rightPanel.editViewModel
            deviceManagerModel: rightPanel.deviceManagerModel
        }
        // ClipProperties, PluginChain, and TrackProperties are likely sub-components
        // of InspectorPanel or managed by it based on selection.
    }
}
