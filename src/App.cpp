#include "App.h"

#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDebug>

App::App(QQmlApplicationEngine* engine, QObject* parent) :
    QObject(parent),
    mQmlEngine(engine)
{
    qDebug() << "App::App - Constructor called.";

    // Instantiate AppModel, which will now manage all other models
    mAppModel = std::make_unique<AppModel>(this);

    // Set the AppModel as a context property for QML
    mQmlEngine->rootContext()->setContextProperty("appModel", mAppModel.get());
}

AppModel* App::getAppModel() const
{
    return mAppModel.get();
}

App::~App()
{
    qDebug() << "App::~App - Destructor called.";
}
