#pragma once

#include <QObject>
#include <memory>
#include "TracktionWrapper/WrapperChangeListener.h"

// Includes for QML and JUCE classes

#include "EngineModel.h"
#include "TransportModel.h"
#include "EditModel.h"
#include "EditViewModel.h"
#include "ProjectManagerModel.h"
#include "DeviceManagerModel.h"
#include "SelectionManagerModel.h"

// Forward declarations for wrapper classes (these are not Q_PROPERTY types, so forward declaration is fine)
class EditWrapper;
class SelectionManagerWrapper;
class TransportWrapper;
class EditViewWrapper;

namespace juce
{
    class ChangeBroadcaster;
}

namespace tracktion
{
    inline namespace engine
    {
        class Engine;
        class Edit;
        class TransportControl;
        class SelectionManager;
    }
}

class AppModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(EngineModel* engineModel READ getEngineModel CONSTANT)
    Q_PROPERTY(TransportModel* transportModel READ getTransportModel NOTIFY transportModelChanged)
    Q_PROPERTY(EditModel* editModel READ getEditModel WRITE setEditModelFromRawPointer NOTIFY editModelChanged)
    Q_PROPERTY(EditViewModel* editViewModel READ getEditViewModel CONSTANT)
    Q_PROPERTY(ProjectManagerModel* projectManagerModel READ getProjectManagerModel CONSTANT)
    Q_PROPERTY(DeviceManagerModel* deviceManagerModel READ getDeviceManagerModel CONSTANT)
    Q_PROPERTY(SelectionManagerModel* selectionManagerModel READ getSelectionManagerModel CONSTANT)

public:
    explicit AppModel(QObject* parent = nullptr);
    ~AppModel() override;

    EngineModel* getEngineModel() const
    {
        // EngineModel should always be available after construction
        return mEngineModel.get();
    }

    TransportModel* getTransportModel() const
    {
        // TransportModel can be null during initialization
        // QML components should check for null before accessing properties
        return mTransportModel.get();
    }

    EditModel* getEditModel() const
    {
        // EditModel can be null during initialization or when no edit is loaded
        // QML components should check for null before accessing properties
        return mEditModel.get();
    }

    EditViewModel* getEditViewModel() const
    {
        // EditViewModel can be null during initialization
        // QML components should check for null before accessing properties
        return mEditViewModel.get();
    }

    ProjectManagerModel* getProjectManagerModel() const
    {
        // ProjectManagerModel can be null during initialization
        // QML components should check for null before accessing properties
        return mProjectManagerModel.get();
    }

    DeviceManagerModel* getDeviceManagerModel() const
    {
        // DeviceManagerModel should always be available after construction
        return mDeviceManagerModel.get();
    }

    SelectionManagerModel* getSelectionManagerModel() const
    {
        // SelectionManagerModel should always be available after construction
        return mSelectionManagerModel.get();
    }

    // Property setters for QML compatibility
    void setEditModelFromRawPointer(EditModel* editModel);

    // Smart pointer setters for internal use
    void setEditModel(std::unique_ptr<EditModel> newEditModel);

signals:
    void editModelChanged();
    void transportModelChanged();

private slots:
    void handleEditLoaded(EditWrapper* newEditWrapper);

private:
    void createEditDependentModels(EditWrapper* editWrapper);
    void initializeEditDependentModelsAsNull();

    // Helper methods
    void initializeCoreModels();
    void cleanupEditDependentModels();

private:
    // Order for proper destruction: Edit-dependent models first, then core models, Engine last
    // Members are destructed in reverse order of declaration
    std::unique_ptr<EngineModel> mEngineModel; // Core engine (destructed last)
    std::unique_ptr<DeviceManagerModel> mDeviceManagerModel; // Device management
    std::unique_ptr<SelectionManagerModel> mSelectionManagerModel; // Selection management
    std::unique_ptr<ProjectManagerModel> mProjectManagerModel; // Project management
    std::unique_ptr<EditViewModel> mEditViewModel; // Edit view state
    std::unique_ptr<TransportModel> mTransportModel; // Transport controls
    std::unique_ptr<EditModel> mEditModel; // Edit model (now owned by AppModel)
};
