#include "DeviceManagerModel.h"
#include <QDebug>

#include "TracktionWrapper/DeviceManagerWrapper.h"
#include "TracktionWrapper/EngineWrapper.h"
#include "TracktionWrapper/TracktionWrapperUtils.h"

DeviceManagerModel::DeviceManagerModel(EngineWrapper* engineWrapper, QObject* parent)
    : QObject(parent),
      mEngineWrapper(engineWrapper)
{
    qDebug() << "DeviceManagerModel::DeviceManagerModel - Constructor called.";

    try {
        // Initialize cached state
        updateCachedState();

        qDebug() << "DeviceManagerModel::DeviceManagerModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "DeviceManagerModel::DeviceManagerModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "DeviceManagerModel::DeviceManagerModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

DeviceManagerModel::~DeviceManagerModel()
{
    qDebug() << "DeviceManagerModel::~DeviceManagerModel - Destructor called.";

    try {
        // No cleanup needed - we don't own any resources
        qDebug() << "DeviceManagerModel::~DeviceManagerModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "DeviceManagerModel::~DeviceManagerModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "DeviceManagerModel::~DeviceManagerModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

QString DeviceManagerModel::getCurrentAudioDevice() const
{
    return mCurrentAudioDevice;
}

bool DeviceManagerModel::hasValidDeviceManager() const
{
    return TracktionWrapperUtils::isEngineWrapperValid(mEngineWrapper) && getDeviceManager() != nullptr;
}

// Helper methods implementation
bool DeviceManagerModel::isEngineWrapperValid() const
{
    return TracktionWrapperUtils::isEngineWrapperValid(mEngineWrapper);
}

tracktion::engine::DeviceManager* DeviceManagerModel::getDeviceManager() const
{
    return TracktionWrapperUtils::getDeviceManagerFromWrapper(mEngineWrapper);
}

void DeviceManagerModel::updateCachedState()
{
    if (hasValidDeviceManager()) {
        mCurrentAudioDevice = "Engine Device Manager";
    } else {
        mCurrentAudioDevice = "Default Audio Device";
    }
}
