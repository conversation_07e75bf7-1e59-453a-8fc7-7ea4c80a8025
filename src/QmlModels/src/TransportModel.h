#pragma once

#include <QObject>
#include <QTimer>
#include <memory>
#include "EditModel.h"
#include "TracktionWrapper/WrapperChangeListener.h"

class TransportWrapper;

class TransportModel : public QObject,
                       public WrapperChangeListener
{
    Q_OBJECT
    Q_PROPERTY(bool isPlaying READ getIsPlaying NOTIFY isPlayingChanged)
    Q_PROPERTY(bool isRecording READ getIsRecording NOTIFY isRecordingChanged)
    Q_PROPERTY(double currentTime READ getCurrentTime NOTIFY currentTimeChanged)
    Q_PROPERTY(double currentTimeInSeconds READ getCurrentTimeInSeconds NOTIFY currentTimeInSecondsChanged)
    Q_PROPERTY(double tempoBpm READ getTempoBpm NOTIFY tempoBpmChanged)

public:
    explicit TransportModel(std::unique_ptr<TransportWrapper> transportWrapper, EditModel* editModel, QObject* parent = nullptr);
    ~TransportModel() override;

    // Property getters
    bool getIsPlaying() const;
    bool getIsRecording() const;
    double getCurrentTime() const;
    double getCurrentTimeInSeconds() const;
    double getTempoBpm() const;
    bool hasValidTransport() const;

    Q_INVOKABLE void togglePlayPause();
    Q_INVOKABLE void toggleRecord();
    Q_INVOKABLE void stop();
    Q_INVOKABLE void rewind();
    Q_INVOKABLE void fastForward();
    Q_INVOKABLE void setTempoBpm(double newTempo);
    Q_INVOKABLE void setPositionInSeconds(double newPosition);

    // WrapperChangeListener override
    void wrapperChanged(void* source) override;

public slots:
    void updateCurrentTime();

signals:
    void isPlayingChanged();
    void isRecordingChanged();
    void currentTimeChanged();
    void currentTimeInSecondsChanged();
    void tempoBpmChanged();

private:
    // Owned transport wrapper
    std::unique_ptr<TransportWrapper> mTransportWrapper;

    // Non-owning reference to EditModel
    EditModel* mEditModel;

    // Timer for continuous updates
    QTimer mUpdateTimer;

    // Cached state
    bool mIsPlaying = false;
    bool mIsRecording = false;

    // Helper methods
    bool isTransportWrapperValid() const;
    void updateCachedState();
};
