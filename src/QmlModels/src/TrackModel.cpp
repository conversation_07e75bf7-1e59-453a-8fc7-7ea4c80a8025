#include "TrackModel.h"
#include <QDebug>

TrackModel::TrackModel(std::shared_ptr<TrackWrapper> trackWrapper,
                       QObject* parent) :
    QObject(parent), mTrack(trackWrapper)
{
    qDebug() << "TrackModel::TrackModel - Constructor called.";

    try {
        // Populate clips if track wrapper is valid
        populateClips();

        qDebug() << "TrackModel::TrackModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "TrackModel::TrackModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "TrackModel::TrackModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

std::shared_ptr<TrackWrapper> TrackModel::getTrackWrapper() const
{
    return mTrack;
}

TrackModel::~TrackModel()
{
    qDebug() << "TrackModel::~TrackModel - Destructor called.";

    try {
        // Clean up clips
        cleanupClips();

        qDebug() << "TrackModel::~TrackModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "TrackModel::~TrackModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "TrackModel::~TrackModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

QString TrackModel::name() const
{
    if (isTrackWrapperValid()) {
        return QString::fromStdString(mTrack->getName());
    }
    return QString();
}

bool TrackModel::hasValidTrack() const
{
    return isTrackWrapperValid();
}

int TrackModel::trackType() const
{
    if (isTrackWrapperValid()) {
        return mTrack->getTrackTypeEnum();
    }
    return 8; // Unknown
}

bool TrackModel::solo() const
{
    if (isTrackWrapperValid()) {
        return mTrack->isSolo();
    }
    return false;
}

bool TrackModel::muted() const
{
    if (isTrackWrapperValid()) {
        return mTrack->isMuted();
    }
    return false;
}

bool TrackModel::inputMonitoring() const
{
    if (isTrackWrapperValid()) {
        return mTrack->isInputMonitoring();
    }
    return false;
}

bool TrackModel::recordArmed() const
{
    if (isTrackWrapperValid()) {
        return mTrack->isRecordArmed();
    }
    return false;
}

void TrackModel::setSolo(bool solo)
{
    if (isTrackWrapperValid()) {
        mTrack->setSolo(solo);
        emit soloChanged();
    }
}

void TrackModel::setMuted(bool muted)
{
    if (isTrackWrapperValid()) {
        mTrack->setMuted(muted);
        emit mutedChanged();
    }
}

void TrackModel::setInputMonitoring(bool monitoring)
{
    if (isTrackWrapperValid()) {
        mTrack->setInputMonitoring(monitoring);
        emit inputMonitoringChanged();
    }
}

void TrackModel::setRecordArmed(bool armed)
{
    if (isTrackWrapperValid()) {
        mTrack->setRecordArmed(armed);
        emit recordArmedChanged();
    }
}

QList<QObject*> TrackModel::getClips() const
{
    QList<QObject*> result;
    for (const auto& clip : mClipModels) {
        result.append(clip.get());
    }
    return result;
}

void TrackModel::addWaveClip(double startTime, double lengthSeconds)
{
    if (isTrackWrapperValid()) {
        if (auto newClipWrapper = mTrack->addWaveClipToEngine(startTime, lengthSeconds)) {
            auto clipModel = std::make_unique<ClipModel>(newClipWrapper, this);
            mClipModels.push_back(std::move(clipModel));
            emit clipsChanged();
        } else {
            qWarning() << "Failed to add wave clip to engine.";
        }
    } else {
        qWarning() << "TrackWrapper not available to add wave clip.";
    }
}

// Helper methods implementation
bool TrackModel::isTrackWrapperValid() const
{
    return mTrack != nullptr;
}

void TrackModel::populateClips()
{
    if (isTrackWrapperValid()) {
        for (int i = 0; i < mTrack->getNumClips(); ++i) {
            if (auto clipWrapper = mTrack->getClip(i)) {
                auto clipModel = std::make_unique<ClipModel>(clipWrapper, this);
                mClipModels.push_back(std::move(clipModel));
            }
        }
    }
}

void TrackModel::cleanupClips()
{
    mClipModels.clear(); // Smart pointers automatically clean up
}
