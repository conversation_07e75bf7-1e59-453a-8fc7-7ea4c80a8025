#include "EngineModel.h"
#include <QDebug>
#include <QStandardPaths> // For QStandardPaths::writableLocation
#include <QDir>
#include <QFileDialog>
#include <QDesktopServices> // For QDesktopServices::openUrl
#include <QUrl>
#include <QFileInfo>

EngineModel::EngineModel(QObject* parent) : QObject(parent)
{
    qDebug() << "EngineModel::EngineModel - Constructor started.";

    try {
        // Initialize core engine infrastructure
        mEngineWrapper = std::make_unique<EngineWrapper>();
        mEngineWrapper->initProject();
        qDebug() << "EngineModel::EngineModel - Engine initialized successfully.";

        // Initialize global track models
        initializeGlobalTrackModels();
        qDebug() << "EngineModel::EngineModel - Global track models initialized.";

        // Set up default edit
        QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/MiniDawDemo";
        QDir().mkpath(tempDir);

        std::string recentEditPath = mEngineWrapper->findRecentEdit(tempDir.toStdString());
        if (!recentEditPath.empty()) {
            createOrLoadEdit(QString::fromStdString(recentEditPath));
        } else {
            createOrLoadEdit(tempDir + "/Test.tracktionedit");
        }

        qDebug() << "EngineModel::EngineModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "EngineModel::EngineModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "EngineModel::EngineModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

EngineModel::~EngineModel()
{
    qDebug() << "EngineModel::~EngineModel - Destructor called.";

    try {
        // Proper destruction order: Edit-dependent objects first, then core engine
        cleanupCurrentEdit();

        // Cleanup global track models (automatic via unique_ptr)
        mMasterTrackModel.reset();
        mChordTrackModel.reset();
        mTempoTrackModel.reset();
        mMarkerTrackModel.reset();
        mArrangerTrackModel.reset();

        // Cleanup engine infrastructure
        if (mEngineWrapper) {
            mEngineWrapper->deleteTemporaryFileManagerDirectoryRecursively();
            mEngineWrapper.reset();
        }

        qDebug() << "EngineModel::~EngineModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "EngineModel::~EngineModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "EngineModel::~EngineModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

QString EngineModel::getCurrentEditFile() const
{
    return mCurrentEditFile;
}

QString EngineModel::getEditName() const
{
    return mEditName;
}

bool EngineModel::hasValidEdit() const
{
    return mCurrentEditWrapper != nullptr;
}

EngineWrapper* EngineModel::getEngineWrapper() const
{
    return mEngineWrapper.get();
}

EditWrapper* EngineModel::getCurrentEditWrapper() const
{
    return mCurrentEditWrapper.get();
}

void EngineModel::setEditName(const QString& name)
{
    if (mEditName != name)
    {
        mEditName = name;
        emit editNameChanged();
    }
}

void EngineModel::createOrLoadEdit(const QString& filePath)
{
    try {
        QString selectedFilePath;
        if (filePath.isEmpty()) {
            // Create QFileDialog manually to ensure proper parent widget and modality
            QFileDialog dialog;
            dialog.setWindowTitle("New Edit");
            dialog.setDirectory(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation));
            dialog.setNameFilter("Tracktion Edit Files (*.tracktionedit)");
            dialog.setFileMode(QFileDialog::AnyFile);
            dialog.setAcceptMode(QFileDialog::AcceptSave);

            if (dialog.exec() == QDialog::Accepted) {
                selectedFilePath = dialog.selectedFiles().first();
            }
        } else {
            selectedFilePath = filePath;
        }

        if (selectedFilePath.isEmpty()) {
            qDebug() << "EngineModel::createOrLoadEdit - No file path selected, operation cancelled.";
            return;
        }

        // Cleanup any existing edit first
        cleanupCurrentEdit();

        // Create new edit
        mCurrentEditWrapper.reset(mEngineWrapper->createAndSetupEdit(selectedFilePath.toStdString()));

        if (!mCurrentEditWrapper) {
            qWarning() << "EngineModel::createOrLoadEdit - Failed to create valid EditWrapper for path:" << selectedFilePath;
            emit editValidityChanged();
            return;
        }

        // Update edit metadata
        mCurrentEditFile = selectedFilePath;
        setEditName(QFileInfo(selectedFilePath).fileName().replace(".tracktionedit", ""));
        emit currentEditFileChanged();
        emit editValidityChanged();

        qDebug() << "EngineModel::createOrLoadEdit - Successfully loaded edit:" << selectedFilePath;

        // Use QTimer::singleShot to ensure the AppModel is fully constructed before sending the signal
        QTimer::singleShot(0, this, [this]() {
            qDebug() << "EngineModel::createOrLoadEdit - Emitting editLoaded signal";
            emit editLoaded(mCurrentEditWrapper.get());
        });

    } catch (const std::exception& e) {
        qCritical() << "EngineModel::createOrLoadEdit - Exception:" << e.what();
        cleanupCurrentEdit();
        emit editValidityChanged();
    } catch (...) {
        qCritical() << "EngineModel::createOrLoadEdit - Unknown exception occurred";
        cleanupCurrentEdit();
        emit editValidityChanged();
    }
}

void EngineModel::closeCurrentEdit()
{
    qDebug() << "EngineModel::closeCurrentEdit - Closing current edit";
    cleanupCurrentEdit();
    emit editClosed();
    emit editValidityChanged();
}

void EngineModel::showEditFile()
{
    if (!mCurrentEditFile.isEmpty()) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(mCurrentEditFile));
    } else {
        qWarning() << "EngineModel::showEditFile - No current edit file to show";
    }
}

int EngineModel::audioBlockSize() const
{
    return mEngineWrapper ? mEngineWrapper->getAudioBlockSize() : 0;
}

double EngineModel::audioSampleRate() const
{
    return mEngineWrapper ? mEngineWrapper->getAudioSampleRate() : 0.0;
}

GlobalTrackModel* EngineModel::getArrangerTrack() const
{
    return mArrangerTrackModel.get();
}

GlobalTrackModel* EngineModel::getMarkerTrack() const
{
    return mMarkerTrackModel.get();
}

GlobalTrackModel* EngineModel::getTempoTrack() const
{
    return mTempoTrackModel.get();
}

GlobalTrackModel* EngineModel::getChordTrack() const
{
    return mChordTrackModel.get();
}

GlobalTrackModel* EngineModel::getMasterTrack() const
{
    return mMasterTrackModel.get();
}

// Helper methods implementation
void EngineModel::initializeGlobalTrackModels()
{
    try {
        mArrangerTrackModel = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Arranger, this);
        mMarkerTrackModel = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Marker, this);
        mTempoTrackModel = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Tempo, this);
        mChordTrackModel = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Chord, this);
        mMasterTrackModel = std::make_unique<GlobalTrackModel>(GlobalTrackModel::Master, this);
    } catch (const std::exception& e) {
        qCritical() << "EngineModel::initializeGlobalTrackModels - Exception:" << e.what();
        throw; // Re-throw to indicate initialization failure
    }
}

void EngineModel::cleanupCurrentEdit()
{
    if (mCurrentEditWrapper) {
        qDebug() << "EngineModel::cleanupCurrentEdit - Cleaning up current edit";
        mCurrentEditWrapper.reset();
        mCurrentEditFile.clear();
        mEditName.clear();
        emit currentEditFileChanged();
        emit editNameChanged();
    }
}
