#pragma once

#include <QObject>
#include <QString>
#include <memory>

// Forward declarations
class EngineModel;
class EditModel;

class ProjectManagerModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString projectPath READ getProjectPath WRITE setProjectPath NOTIFY projectPathChanged)

public:
    explicit ProjectManagerModel(EngineModel* engineModel, EditModel* editModel, QObject* parent = nullptr);

    Q_INVOKABLE QString getProjectPath() const
    {
        return mProjectPath;
    }

    Q_INVOKABLE void createOrLoadNewEdit(const QString& filePath = "");
    Q_INVOKABLE void saveCurrentEdit();
    Q_INVOKABLE void setProjectPath(const QString& path);

signals:
    void projectPathChanged();

private:
    QString mProjectPath;
    EngineModel* mEngineModel; // Not owned, just observed
    EditModel* mEditModel;     // Not owned, just observed
};
