#pragma once

#include <QObject>
#include <memory> // For std::unique_ptr

#include "TracktionWrapper/SelectionManagerWrapper.h"


#include "TracktionWrapper/WrapperChangeListener.h"

class SelectionManagerModel : public QObject,
                              public WrapperChangeListener
{
    Q_OBJECT
    Q_PROPERTY(int numObjectsSelected READ getNumObjectsSelected NOTIFY selectionChanged)
    Q_PROPERTY(bool hasValidSelectionManager READ hasValidSelectionManager NOTIFY selectionManagerValidityChanged)

public:
    explicit SelectionManagerModel(std::unique_ptr<SelectionManagerWrapper> wrapper, QObject* parent = nullptr);
    ~SelectionManagerModel() override;

    // Property getters
    SelectionManagerWrapper* getSelectionManagerWrapper() const;
    int getNumObjectsSelected() const;
    bool hasValidSelectionManager() const;

    Q_INVOKABLE void deleteSelected();

signals:
    void selectionChanged();
    void selectionManagerValidityChanged();

protected:
    // WrapperChangeListener override
    void wrapperChanged(void* source) override;

private:
    // Owned selection manager wrapper
    std::unique_ptr<SelectionManagerWrapper> mSelectionManagerWrapper;

    // Helper methods
    bool isSelectionManagerWrapperValid() const;
};
