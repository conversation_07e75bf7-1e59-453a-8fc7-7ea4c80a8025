#pragma once
#include <QObject>
#include <QString>
#include <QList> // For QList
#include <memory>
#include <vector>
#include "ClipModel.h"

#include "TracktionWrapper/TrackWrapper.h"

class TrackModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ name NOTIFY nameChanged)
    Q_PROPERTY(QList<QObject*> clips READ getClips NOTIFY clipsChanged)
    Q_PROPERTY(bool hasValidTrack READ hasValidTrack NOTIFY trackValidityChanged)
    Q_PROPERTY(int trackType READ trackType NOTIFY trackTypeChanged)
    Q_PROPERTY(bool solo READ solo WRITE setSolo NOTIFY soloChanged)
    Q_PROPERTY(bool muted READ muted WRITE setMuted NOTIFY mutedChanged)
    Q_PROPERTY(bool inputMonitoring READ inputMonitoring WRITE setInputMonitoring NOTIFY inputMonitoringChanged)
    Q_PROPERTY(bool recordArmed READ recordArmed WRITE setRecordArmed NOTIFY recordArmedChanged)

public:
    // Default constructor deleted to ensure <PERSON><PERSON>ode<PERSON> always represents a valid TrackWrapper
    explicit TrackModel(QObject* parent = nullptr) = delete;
    TrackModel(std::shared_ptr<TrackWrapper> trackWrapper,
               QObject* parent = nullptr);
    ~TrackModel() override;

    // Property getters
    QString name() const;
    QList<QObject*> getClips() const;
    bool hasValidTrack() const;
    int trackType() const;
    bool solo() const;
    bool muted() const;
    bool inputMonitoring() const;
    bool recordArmed() const;

    // Property setters
    void setSolo(bool solo);
    void setMuted(bool muted);
    void setInputMonitoring(bool monitoring);
    void setRecordArmed(bool armed);

    Q_INVOKABLE void addWaveClip(double startTime, double lengthSeconds);
    std::shared_ptr<TrackWrapper> getTrackWrapper() const;

signals:
    void nameChanged();
    void clipsChanged();
    void trackValidityChanged();
    void trackTypeChanged();
    void soloChanged();
    void mutedChanged();
    void inputMonitoringChanged();
    void recordArmedChanged();

private:
    // Shared ownership of track wrapper (may be shared with other components)
    std::shared_ptr<TrackWrapper> mTrack;

    // Owned clip models (using std::vector for better move semantics support)
    std::vector<std::unique_ptr<ClipModel>> mClipModels;

    // Helper methods
    bool isTrackWrapperValid() const;
    void populateClips();
    void cleanupClips();
};
