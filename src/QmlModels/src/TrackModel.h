#pragma once
#include <QObject>
#include <QString>
#include <QList> // For QList
#include <memory>
#include <vector>
#include "ClipModel.h"

#include "TracktionWrapper/TrackWrapper.h"

class TrackModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ name NOTIFY nameChanged)
    Q_PROPERTY(QList<QObject*> clips READ getClips NOTIFY clipsChanged)
    Q_PROPERTY(bool hasValidTrack READ hasValidTrack NOTIFY trackValidityChanged)

public:
    // Default constructor deleted to ensure TrackModel always represents a valid TrackWrapper
    explicit TrackModel(QObject* parent = nullptr) = delete;
    TrackModel(std::shared_ptr<TrackWrapper> trackWrapper,
               QObject* parent = nullptr);
    ~TrackModel() override;

    // Property getters
    QString name() const;
    QList<QObject*> getClips() const;
    bool hasValidTrack() const;

    Q_INVOKABLE void addWaveClip(double startTime, double lengthSeconds);
    std::shared_ptr<TrackWrapper> getTrackWrapper() const;

signals:
    void nameChanged();
    void clipsChanged();
    void trackValidityChanged();

private:
    // Shared ownership of track wrapper (may be shared with other components)
    std::shared_ptr<TrackWrapper> mTrack;

    // Owned clip models (using std::vector for better move semantics support)
    std::vector<std::unique_ptr<ClipModel>> mClipModels;

    // Helper methods
    bool isTrackWrapperValid() const;
    void populateClips();
    void cleanupClips();
};
