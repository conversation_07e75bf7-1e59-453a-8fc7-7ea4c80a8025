#pragma once

#include <QObject>
#include <QString>
#include <memory>

// Forward declarations
class EngineWrapper;

namespace tracktion { inline namespace engine { class DeviceManager; }}

class DeviceManagerModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString currentAudioDevice READ getCurrentAudioDevice NOTIFY currentAudioDeviceChanged)
    Q_PROPERTY(bool hasValidDeviceManager READ hasValidDeviceManager NOTIFY deviceManagerValidityChanged)

public:
    explicit DeviceManagerModel(EngineWrapper* engineWrapper, QObject* parent = nullptr);
    ~DeviceManagerModel() override;

    // Property getters
    QString getCurrentAudioDevice() const;
    bool hasValidDeviceManager() const;

    // Q_INVOKABLE void closeAudioDevices();

signals:
    void currentAudioDeviceChanged();
    void deviceManagerValidityChanged();

private:
    // Non-owning reference to EngineWrapper (owned by EngineModel)
    EngineWrapper* mEngineWrapper;

    // Cached state
    QString mCurrentAudioDevice;

    // Helper methods
    bool isEngineWrapperValid() const;
    tracktion::engine::DeviceManager* getDeviceManager() const;
    void updateCachedState();
};
