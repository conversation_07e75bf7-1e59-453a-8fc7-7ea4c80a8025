#pragma once

#include <QObject>
#include <memory> // For std::shared_ptr
#include <QList> // For QList

// Forward declarations
class EditViewWrapper;
class SelectionManagerWrapper;
class TrackModel; // Assuming EditViewModel might expose selected tracks

class EditViewModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QList<QObject*> selectedTracks READ getSelectedTracks NOTIFY selectedTracksChanged)
    Q_PROPERTY(bool showMasterTrack READ getShowMasterTrack NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showGlobalTrack READ getShowGlobalTrack NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showMarkerTrack READ getShowMarkerTrack NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showChordTrack READ getShowChordTrack NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showArrangerTrack READ getShowArrangerTrack NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool drawWaveforms READ getDrawWaveforms WRITE setDrawWaveforms NOTIFY viewPropertiesChanged) // Added WRITE
    Q_PROPERTY(bool showHeaders READ getShowHeaders NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showFooters READ getShowFooters NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showMidiDevices READ getShowMidiDevices NOTIFY viewPropertiesChanged)
    Q_PROPERTY(bool showWaveDevices READ getShowWaveDevices NOTIFY viewPropertiesChanged)
    Q_PROPERTY(double viewX1 READ getViewX1 NOTIFY viewPropertiesChanged)
    Q_PROPERTY(double viewX2 READ getViewX2 NOTIFY viewPropertiesChanged)
    Q_PROPERTY(double viewY READ getViewY NOTIFY viewPropertiesChanged)

public:
    explicit EditViewModel(std::shared_ptr<EditViewWrapper> editViewWrapper, QObject* parent = nullptr);
    ~EditViewModel() override; // Declare destructor

    QList<QObject*> getSelectedTracks() const
    {
        return mSelectedTracks;
    }

    bool getShowMasterTrack() const;
    bool getShowGlobalTrack() const;
    bool getShowMarkerTrack() const;
    bool getShowChordTrack() const;
    bool getShowArrangerTrack() const;
    bool getDrawWaveforms() const;
    Q_INVOKABLE void setDrawWaveforms(bool draw);
    bool getShowHeaders() const;
    bool getShowFooters() const;
    bool getShowMidiDevices() const;
    bool getShowWaveDevices() const;
    double getViewX1() const;
    double getViewX2() const;
    double getViewY() const;

    Q_INVOKABLE int timeToX(double timeInSeconds, int width) const;
    Q_INVOKABLE double xToTime(int x, int width) const;

signals:
    void selectedTracksChanged();
    void viewPropertiesChanged(); // Generic signal for view property changes

private:
    std::shared_ptr<EditViewWrapper> mEditViewWrapper;
    QList<QObject*> mSelectedTracks;
};
