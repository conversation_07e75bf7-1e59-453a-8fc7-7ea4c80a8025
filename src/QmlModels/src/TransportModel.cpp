#include "TransportModel.h"
#include <QDebug>

#include "TracktionWrapper/TransportWrapper.h"
#include "TracktionWrapper/WrapperChangeListener.h"
#include "TracktionWrapper/TracktionWrapperUtils.h"

TransportModel::TransportModel(std::unique_ptr<TransportWrapper> transportWrapper, EditModel* editModel, QObject* parent)
    : QObject(parent)
    , mTransportWrapper(std::move(transportWrapper))
    , mEditModel(editModel)
{
    qDebug() << "TransportModel::TransportModel - Constructor called.";

    try {
        // Setup change listener if transport wrapper is valid
        if (isTransportWrapperValid()) {
            mTransportWrapper->addChangeListener(this);
        }

        // Initialize cached state
        updateCachedState();

        // Setup timer for continuous time updates
        connect(&mUpdateTimer, &QTimer::timeout, this, &TransportModel::updateCurrentTime);
        mUpdateTimer.setInterval(30); // Update every 30ms for smooth playback

        qDebug() << "TransportModel::TransportModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "TransportModel::TransportModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "TransportModel::TransportModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

TransportModel::~TransportModel()
{
    qDebug() << "TransportModel::~TransportModel - Destructor called.";

    try {
        // Stop timer first
        mUpdateTimer.stop();

        // Remove change listener if transport wrapper is valid
        if (isTransportWrapperValid()) {
            mTransportWrapper->removeChangeListener(this);
        }

        qDebug() << "TransportModel::~TransportModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "TransportModel::~TransportModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "TransportModel::~TransportModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

bool TransportModel::getIsPlaying() const
{
    return mIsPlaying;
}

bool TransportModel::getIsRecording() const
{
    return mIsRecording;
}

bool TransportModel::hasValidTransport() const
{
    return isTransportWrapperValid();
}

double TransportModel::getCurrentTime() const
{
    return isTransportWrapperValid() ? mTransportWrapper->getCurrentTimeInBars() : 0.0;
}

double TransportModel::getCurrentTimeInSeconds() const
{
    return isTransportWrapperValid() ? mTransportWrapper->getCurrentTimeInSeconds() : 0.0;
}

double TransportModel::getTempoBpm() const
{
    return isTransportWrapperValid() ? mTransportWrapper->getTempoBpm() : 120.0;
}

void TransportModel::togglePlayPause()
{
    qDebug() << "TransportModel::togglePlayPause() - Called";
    if (isTransportWrapperValid()) {
        qDebug() << "TransportModel::togglePlayPause() - Current isPlaying:" << mIsPlaying;
        mTransportWrapper->togglePlayPause();
        // Update cached state and emit signal
        bool newIsPlaying = mTransportWrapper->isPlaying();
        qDebug() << "TransportModel::togglePlayPause() - New isPlaying:" << newIsPlaying;
        if (mIsPlaying != newIsPlaying)
        {
            mIsPlaying = newIsPlaying;
            emit isPlayingChanged();
            qDebug() << "TransportModel::togglePlayPause() - Emitted isPlayingChanged()";
        }

        if (mIsPlaying) {
            mUpdateTimer.start();
            qDebug() << "TransportModel::togglePlayPause() - Timer started";
        } else {
            mUpdateTimer.stop();
            qDebug() << "TransportModel::togglePlayPause() - Timer stopped";
        }
    } else {
        qDebug() << "TransportModel::togglePlayPause() - No transport wrapper available";
    }
}

void TransportModel::toggleRecord()
{
    if (mEditModel) // Use EditModel to toggle record as it handles saving
    {
        mEditModel->toggleRecord();
    }
}

void TransportModel::stop()
{
    qDebug() << "TransportModel::stop() - Called";
    if (isTransportWrapperValid()) {
        qDebug() << "TransportModel::stop() - Current isPlaying:" << mIsPlaying;
        mTransportWrapper->stop();
        mTransportWrapper->setPositionInSeconds(0.0); // Reset position to 0
        mUpdateTimer.stop(); // Stop the timer when playback stops
        qDebug() << "TransportModel::stop() - Timer stopped";
        
        // Update cached states and emit signals
        bool newIsPlaying = mTransportWrapper->isPlaying();
        qDebug() << "TransportModel::stop() - New isPlaying:" << newIsPlaying;
        if (mIsPlaying != newIsPlaying)
        {
            mIsPlaying = newIsPlaying;
            emit isPlayingChanged();
            qDebug() << "TransportModel::stop() - Emitted isPlayingChanged()";
        }
        bool newIsRecording = mTransportWrapper->isRecording();
        if (mIsRecording != newIsRecording)
        {
            mIsRecording = newIsRecording;
            emit isRecordingChanged();
            qDebug() << "TransportModel::stop() - Emitted isRecordingChanged()";
        }
        emit currentTimeChanged(); // Emit immediately to update UI to 0
        emit currentTimeInSecondsChanged(); // Emit immediately to update UI to 0
        qDebug() << "TransportModel::stop() - Emitted time update signals";
    } else {
        qDebug() << "TransportModel::stop() - No transport wrapper available";
    }
}

void TransportModel::rewind()
{
    if (isTransportWrapperValid()) {
        mTransportWrapper->rewind();
    }
}

void TransportModel::fastForward()
{
    if (isTransportWrapperValid()) {
        mTransportWrapper->fastForward();
    }
}

void TransportModel::setTempoBpm(double newTempo)
{
    if (isTransportWrapperValid()) {
        mTransportWrapper->setTempoBpm(newTempo);
    }
}

void TransportModel::setPositionInSeconds(double newPosition)
{
    if (isTransportWrapperValid()) {
        mTransportWrapper->setPositionInSeconds(newPosition);
    }
}

void TransportModel::wrapperChanged(void* source)
{
    // This callback is triggered by changes in the TransportWrapper.
    // We need to determine what changed and emit the appropriate signal.
    if (source == mTransportWrapper.get())
    {
        bool newIsPlaying = mTransportWrapper->isPlaying();
        if (mIsPlaying != newIsPlaying)
        {
            mIsPlaying = newIsPlaying;
            emit isPlayingChanged();
        }
        bool newIsRecording = mTransportWrapper->isRecording();
        if (mIsRecording != newIsRecording)
        {
            mIsRecording = newIsRecording;
            emit isRecordingChanged();
        }
        emit currentTimeChanged();
        emit currentTimeInSecondsChanged();
        emit tempoBpmChanged();
    }
}

void TransportModel::updateCurrentTime()
{
    // These signals are emitted periodically by the timer
    emit currentTimeChanged();
    emit currentTimeInSecondsChanged();
}

// Helper methods implementation
bool TransportModel::isTransportWrapperValid() const
{
    return TracktionWrapperUtils::isTransportWrapperValid(mTransportWrapper.get());
}

void TransportModel::updateCachedState()
{
    if (isTransportWrapperValid()) {
        mIsPlaying = mTransportWrapper->isPlaying();
        mIsRecording = mTransportWrapper->isRecording();
    } else {
        mIsPlaying = false;
        mIsRecording = false;
    }
}
