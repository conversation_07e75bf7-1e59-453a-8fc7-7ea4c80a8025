#pragma once
#include <QTimer>
#include <QString>
#include <memory>
#include <QtWidgets/QFileDialog>

#include "GlobalTrackModel.h"

#include "TracktionWrapper/EngineWrapper.h"
#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/WrapperChangeListener.h"

// should not be final since QQmlElement requires inheritance
class EngineModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int audioBlockSize READ audioBlockSize NOTIFY audioSettingsChanged)
    Q_PROPERTY(double audioSampleRate READ audioSampleRate NOTIFY audioSettingsChanged)
    Q_PROPERTY(QString currentEditFile READ getCurrentEditFile NOTIFY currentEditFileChanged)
    Q_PROPERTY(QString editName READ getEditName NOTIFY editNameChanged)
    Q_PROPERTY(bool hasValidEdit READ hasValidEdit NOTIFY editValidityChanged)

public:
    explicit EngineModel(QObject* parent = nullptr);
    ~EngineModel() override;

    // Audio settings properties
    int audioBlockSize() const;
    double audioSampleRate() const;

    // Edit properties
    QString getCurrentEditFile() const;
    QString getEditName() const;
    bool hasValidEdit() const;

    // Engine access (non-owning pointer for external use)
    EngineWrapper* getEngineWrapper() const;

    // Edit access (non-owning pointer for external use)
    EditWrapper* getCurrentEditWrapper() const;

    // Global track access
    Q_INVOKABLE GlobalTrackModel* getArrangerTrack() const;
    Q_INVOKABLE GlobalTrackModel* getMarkerTrack() const;
    Q_INVOKABLE GlobalTrackModel* getTempoTrack() const;
    Q_INVOKABLE GlobalTrackModel* getChordTrack() const;
    Q_INVOKABLE GlobalTrackModel* getMasterTrack() const;

    // Edit management
    Q_INVOKABLE void createOrLoadEdit(const QString& filePath = "");
    Q_INVOKABLE void closeCurrentEdit();
    Q_INVOKABLE void showEditFile();
    Q_INVOKABLE void setEditName(const QString& name);

signals:
    void audioSettingsChanged();
    void currentEditFileChanged();
    void editLoaded(EditWrapper* editWrapper);
    void editClosed();
    void editNameChanged();
    void editValidityChanged();

private:
    // Core engine infrastructure (exclusive ownership)
    std::unique_ptr<EngineWrapper> mEngineWrapper;
    std::unique_ptr<EditWrapper> mCurrentEditWrapper;

    // Edit metadata
    QString mCurrentEditFile;
    QString mEditName;

    // Global track models (exclusive ownership)
    std::unique_ptr<GlobalTrackModel> mArrangerTrackModel;
    std::unique_ptr<GlobalTrackModel> mMarkerTrackModel;
    std::unique_ptr<GlobalTrackModel> mTempoTrackModel;
    std::unique_ptr<GlobalTrackModel> mChordTrackModel;
    std::unique_ptr<GlobalTrackModel> mMasterTrackModel;

    // Helper methods
    void initializeGlobalTrackModels();
    void cleanupCurrentEdit();
};
