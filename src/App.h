#pragma once

#include <QObject>
#include <memory>

#include "QmlModels/src/AppModel.h"

class QQmlApplicationEngine;

class App : public QObject
{
    Q_OBJECT
public:
    explicit App(QQmlApplicationEngine* engine, QObject* parent = nullptr);
    ~App() override;

    Q_INVOKABLE AppModel* getAppModel() const;

private:
    QQmlApplicationEngine* mQmlEngine;

    std::unique_ptr<AppModel> mAppModel;
};
