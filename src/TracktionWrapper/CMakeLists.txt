# CMakeLists.txt for Tracktion Engine Wrapper

set(tracktion_DIR "/Users/<USER>/projects/GitHub/tracktion_engine/")
# Add Tracktion Engine project (which includes JUCE modules)
add_subdirectory(${tracktion_DIR} ${CMAKE_CURRENT_BINARY_DIR}/tracktion_engine_build)

# ==== JUCE Modules ====
# Only the essential backend modules (NO GUI)
set(juce_backend_modules
        "juce::juce_core"
        "juce::juce_data_structures"
        "juce::juce_events"
        # "juce::juce_osc"
        # "juce::juce_audio_utils"
        # "juce::juce_dsp"
        # "juce::juce_audio_basics"
        # "juce::juce_audio_devices"
        # "juce::juce_audio_formats"
        # "juce::juce_audio_processors"
)

# GUI features modules
#set(juce_gui_modules
# "juce::juce_graphics"
# "juce::juce_gui_basics"
# "juce::juce_gui_extra"
# "juce::juce_opengl"
# "juce::juce_video"
#)

#set(juce_flags
# "juce::juce_recommended_config_flags"
# "juce::juce_recommended_lto_flags"
#)

set(tracktion_modules
        "tracktion::tracktion_core"
        "tracktion::tracktion_engine"
        "tracktion::tracktion_graph"
)

add_library(TracktionWrapper SHARED
        src/EngineWrapper.cpp
        src/EditWrapper.cpp
        src/TransportWrapper.cpp
        src/TrackWrapper.cpp
        src/ClipWrapper.cpp
        src/ProjectWrapper.cpp
        src/AudioFileWrapper.cpp
        src/DeviceManagerWrapper.cpp
        src/EditViewWrapper.cpp
        src/SelectionManagerWrapper.cpp
        src/JuceInitialiserWrapper.cpp
        src/TracktionWrapperUtils.cpp
)

target_include_directories(TracktionWrapper
        PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
        PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(TracktionWrapper
        PRIVATE
        ${tracktion_modules}
        ${juce_backend_modules}
)

target_compile_definitions(TracktionWrapper PRIVATE
        JUCE_USE_FONTCONFIG=0
        JUCE_USE_FREETYPE=0
        JUCE_USE_CURL=0`
        JUCE_WEB_BROWSER=0
        JUCE_MODAL_LOOPS_PERMITTED=0
        # JUCE_PLUGINHOST_AU=1
        # JUCE_PLUGINHOST_LADSPA=1
        # JUCE_PLUGINHOST_VST3=1
        # JUCE_STRICT_REFCOUNTEDPOINTER=1

        TRACKTION_UNIT_TESTS=0
        TRACKTION_BENCHMARKS=0
        # TRACKTION_LOG_DEVICES=0
        # TRACKTION_ENABLE_TIMESTRETCH_SOUNDTOUCH=1
        # TRACKTION_ENABLE_TIMESTRETCH_RUBBERBAND=${HAS_RUBBERBAND}
        # TRACKTION_BUILD_RUBBERBAND=${HAS_RUBBERBAND}
)

set_target_properties(TracktionWrapper PROPERTIES CXX_STANDARD 20 CXX_STANDARD_REQUIRED YES)
