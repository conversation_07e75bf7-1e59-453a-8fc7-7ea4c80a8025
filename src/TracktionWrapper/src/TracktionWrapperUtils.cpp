#include "TracktionWrapper/TracktionWrapperUtils.h"
#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/TransportWrapper.h"
#include "TracktionWrapper/EditViewWrapper.h"
#include "TracktionWrapper/SelectionManagerWrapper.h"
#include "TracktionWrapper/EngineWrapper.h"

#include <iostream>

std::unique_ptr<TransportWrapper> TracktionWrapperUtils::createTransportWrapper(EditWrapper* editWrapper)
{
    std::cout << "TracktionWrapperUtils::createTransportWrapper - Creating TransportWrapper from EditWrapper: " << editWrapper << std::endl;

    if (!isEditWrapperValid(editWrapper)) {
        std::cout << "TracktionWrapperUtils::createTransportWrapper - EditWrapper is invalid, returning nullptr" << std::endl;
        return nullptr;
    }

    try {
        // Extract the TransportControl and Edit from the EditWrapper
        tracktion::engine::TransportControl* transportControl = getTransportControlFromWrapper(editWrapper);
        tracktion::engine::Edit* edit = getEditFromWrapper(editWrapper);

        if (!transportControl || !edit) {
            std::cerr << "TracktionWrapperUtils::createTransportWrapper - Failed to extract TransportControl or Edit from EditWrapper" << std::endl;
            return nullptr;
        }

        // Create the TransportWrapper using the same pattern as in AppModel::createEditDependentModels
        auto transportWrapper = std::make_unique<TransportWrapper>(*transportControl, *edit);

        std::cout << "TracktionWrapperUtils::createTransportWrapper - Successfully created TransportWrapper" << std::endl;
        return transportWrapper;

    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::createTransportWrapper - Exception occurred: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::createTransportWrapper - Unknown exception occurred" << std::endl;
        return nullptr;
    }
}

bool TracktionWrapperUtils::isEditWrapperValid(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return false;
    }

    try {
        // Check if the EditWrapper has a valid Edit
        tracktion::engine::Edit* edit = getEditFromWrapper(editWrapper);
        return edit != nullptr;
    } catch (...) {
        // If any exception occurs during validation, consider it invalid
        return false;
    }
}

tracktion::engine::Edit* TracktionWrapperUtils::getEditFromWrapper(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return nullptr;
    }

    try {
        // Use the EditWrapper's getEdit method to get a reference to the Edit
        return &editWrapper->getEdit();
    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::getEditFromWrapper - Exception getting Edit: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::getEditFromWrapper - Unknown exception getting Edit" << std::endl;
        return nullptr;
    }
}

tracktion::engine::TransportControl* TracktionWrapperUtils::getTransportControlFromWrapper(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return nullptr;
    }

    try {
        // Use the EditWrapper's getTransportControl method to get a reference to the TransportControl
        return &editWrapper->getTransportControl();
    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::getTransportControlFromWrapper - Exception getting TransportControl: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::getTransportControlFromWrapper - Unknown exception getting TransportControl" << std::endl;
        return nullptr;
    }
}

std::shared_ptr<EditViewWrapper> TracktionWrapperUtils::createEditViewWrapper(EditWrapper* editWrapper,
                                                                              SelectionManagerWrapper* selectionManagerWrapper)
{
    std::cout << "TracktionWrapperUtils::createEditViewWrapper - Creating EditViewWrapper from EditWrapper: "
              << editWrapper << " and SelectionManagerWrapper: " << selectionManagerWrapper << std::endl;

    if (!isEditWrapperValid(editWrapper)) {
        std::cout << "TracktionWrapperUtils::createEditViewWrapper - EditWrapper is invalid, returning nullptr" << std::endl;
        return nullptr;
    }

    if (!isSelectionManagerWrapperValid(selectionManagerWrapper)) {
        std::cout << "TracktionWrapperUtils::createEditViewWrapper - SelectionManagerWrapper is invalid, returning nullptr" << std::endl;
        return nullptr;
    }

    try {
        // Extract the Edit and SelectionManager from their respective wrappers
        tracktion::engine::Edit* edit = getEditFromWrapper(editWrapper);
        tracktion::engine::SelectionManager* selectionManager = getSelectionManagerFromWrapper(selectionManagerWrapper);

        if (!edit || !selectionManager) {
            std::cerr << "TracktionWrapperUtils::createEditViewWrapper - Failed to extract Edit or SelectionManager from wrappers" << std::endl;
            return nullptr;
        }

        // Create the EditViewWrapper using the same pattern as in AppModel::createEditDependentModels
        auto editViewWrapper = std::make_shared<EditViewWrapper>(*edit, *selectionManager);

        std::cout << "TracktionWrapperUtils::createEditViewWrapper - Successfully created EditViewWrapper" << std::endl;
        return editViewWrapper;

    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::createEditViewWrapper - Exception occurred: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::createEditViewWrapper - Unknown exception occurred" << std::endl;
        return nullptr;
    }
}

std::unique_ptr<SelectionManagerWrapper> TracktionWrapperUtils::createSelectionManagerWrapper(EngineWrapper* engineWrapper)
{
    std::cout << "TracktionWrapperUtils::createSelectionManagerWrapper - Creating SelectionManagerWrapper from EngineWrapper: "
              << engineWrapper << std::endl;

    if (!isEngineWrapperValid(engineWrapper)) {
        std::cout << "TracktionWrapperUtils::createSelectionManagerWrapper - EngineWrapper is invalid, returning nullptr" << std::endl;
        return nullptr;
    }

    try {
        // Extract the Engine from the EngineWrapper
        tracktion::engine::Engine* engine = getEngineFromWrapper(engineWrapper);

        if (!engine) {
            std::cerr << "TracktionWrapperUtils::createSelectionManagerWrapper - Failed to extract Engine from EngineWrapper" << std::endl;
            return nullptr;
        }

        // Create the SelectionManagerWrapper using the same pattern as in AppModel initialization
        auto selectionManagerWrapper = std::make_unique<SelectionManagerWrapper>(*engine);

        std::cout << "TracktionWrapperUtils::createSelectionManagerWrapper - Successfully created SelectionManagerWrapper" << std::endl;
        return selectionManagerWrapper;

    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::createSelectionManagerWrapper - Exception occurred: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::createSelectionManagerWrapper - Unknown exception occurred" << std::endl;
        return nullptr;
    }
}

bool TracktionWrapperUtils::isEngineWrapperValid(EngineWrapper* engineWrapper)
{
    if (!engineWrapper) {
        return false;
    }

    try {
        // Check if the EngineWrapper has a valid Engine
        tracktion::engine::Engine* engine = getEngineFromWrapper(engineWrapper);
        return engine != nullptr;
    } catch (...) {
        // If any exception occurs during validation, consider it invalid
        return false;
    }
}

bool TracktionWrapperUtils::isSelectionManagerWrapperValid(SelectionManagerWrapper* selectionManagerWrapper)
{
    if (!selectionManagerWrapper) {
        return false;
    }

    try {
        // Check if the SelectionManagerWrapper has a valid SelectionManager
        tracktion::engine::SelectionManager* selectionManager = getSelectionManagerFromWrapper(selectionManagerWrapper);
        return selectionManager != nullptr;
    } catch (...) {
        // If any exception occurs during validation, consider it invalid
        return false;
    }
}

tracktion::engine::Engine* TracktionWrapperUtils::getEngineFromWrapper(EngineWrapper* engineWrapper)
{
    if (!engineWrapper) {
        return nullptr;
    }

    try {
        // Use the EngineWrapper's getEngine method to get a reference to the Engine
        return &engineWrapper->getEngine();
    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::getEngineFromWrapper - Exception getting Engine: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::getEngineFromWrapper - Unknown exception getting Engine" << std::endl;
        return nullptr;
    }
}

tracktion::engine::SelectionManager* TracktionWrapperUtils::getSelectionManagerFromWrapper(SelectionManagerWrapper* selectionManagerWrapper)
{
    if (!selectionManagerWrapper) {
        return nullptr;
    }

    try {
        // Use the SelectionManagerWrapper's getSelectionManager method to get a reference to the SelectionManager
        return &selectionManagerWrapper->getSelectionManager();
    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::getSelectionManagerFromWrapper - Exception getting SelectionManager: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::getSelectionManagerFromWrapper - Unknown exception getting SelectionManager" << std::endl;
        return nullptr;
    }
}

tracktion::engine::DeviceManager* TracktionWrapperUtils::getDeviceManagerFromWrapper(EngineWrapper* engineWrapper)
{
    if (!engineWrapper) {
        return nullptr;
    }

    try {
        // Use the EngineWrapper's getDeviceManager method to get a reference to the DeviceManager
        return &engineWrapper->getDeviceManager();
    } catch (const std::exception& e) {
        std::cerr << "TracktionWrapperUtils::getDeviceManagerFromWrapper - Exception getting DeviceManager: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "TracktionWrapperUtils::getDeviceManagerFromWrapper - Unknown exception getting DeviceManager" << std::endl;
        return nullptr;
    }
}

bool TracktionWrapperUtils::isTransportWrapperValid(TransportWrapper* transportWrapper)
{
    // Simple null check for TransportWrapper - no need to access internal components
    return transportWrapper != nullptr;
}
