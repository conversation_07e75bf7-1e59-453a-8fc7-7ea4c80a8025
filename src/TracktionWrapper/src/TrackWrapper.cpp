#include "TracktionWrapper/TrackWrapper.h"
#include "TracktionWrapper/ClipWrapper.h"
#include <tracktion_engine/tracktion_engine.h> // For tracktion::engine::ClipTrack, etc.
#include <juce_core/juce_core.h> // For juce::File
#include <algorithm> // For std::remove


TrackWrapper::TrackWrapper(tracktion::engine::Track& t) :
    mTrack(t)
{
}

TrackWrapper::~TrackWrapper()
{
}

std::string TrackWrapper::getName() const
{
    return mTrack.getName().toStdString();
}

std::shared_ptr<ClipWrapper> TrackWrapper::getClip(int index)
{
    if (auto clipTrack = dynamic_cast<tracktion::engine::ClipTrack*>(&mTrack))
    {
        if (index >= 0 && index < clipTrack->getClips().size())
        {
            return std::make_shared<ClipWrapper>(*clipTrack->getClips()[index]);
        }
    }
    return nullptr;
}

int TrackWrapper::getNumClips() const
{
    if (auto clipTrack = dynamic_cast<tracktion::engine::ClipTrack*>(&mTrack))
    {
        return clipTrack->getClips().size();
    }
    return 0;
}

bool TrackWrapper::isSolo() const
{
    return mTrack.isSolo(false); // Pass false for direct solo status
}

void TrackWrapper::setSolo(bool solo)
{
    mTrack.setSolo(solo);
}

bool TrackWrapper::isMuted() const
{
    return mTrack.isMuted(false); // Pass false for direct mute status
}

void TrackWrapper::setMuted(bool muted)
{
    mTrack.setMute(muted); // Corrected method name
}

std::string TrackWrapper::getTrackType() const
{
    if (mTrack.isAudioTrack()) return "Audio";
    if (mTrack.isArrangerTrack()) return "Arranger";
    if (mTrack.isMarkerTrack()) return "Marker";
    if (mTrack.isTempoTrack()) return "Tempo";
    if (mTrack.isChordTrack()) return "Chord";
    if (mTrack.isMasterTrack()) return "Master";
    if (mTrack.isFolderTrack()) return "Folder";
    if (mTrack.isAutomationTrack()) return "Automation";
    return "Unknown";
}

int TrackWrapper::getTrackTypeEnum() const
{
    // Return enum values matching GlobalTrackModel::TrackType
    if (mTrack.isAudioTrack()) return 0;        // Audio
    if (mTrack.isArrangerTrack()) return 1;     // Arranger
    if (mTrack.isMarkerTrack()) return 2;       // Marker
    if (mTrack.isTempoTrack()) return 3;        // Tempo
    if (mTrack.isChordTrack()) return 4;        // Chord
    if (mTrack.isMasterTrack()) return 5;       // Master
    if (mTrack.isFolderTrack()) return 6;       // Folder
    if (mTrack.isAutomationTrack()) return 7;   // Automation
    return 8; // Unknown
}

bool TrackWrapper::isInputMonitoring() const
{
    // Input monitoring is only relevant for audio tracks
    if (auto audioTrack = dynamic_cast<tracktion::engine::AudioTrack*>(&mTrack))
    {
        // Check if the track has live input monitoring enabled
        return audioTrack->hasAnyLiveInputs();
    }
    return false;
}

void TrackWrapper::setInputMonitoring(bool monitoring)
{
    // Input monitoring control would need to be implemented based on
    // tracktion engine's input device management
    // For now, this is a placeholder
    (void)monitoring; // Suppress unused parameter warning
}

bool TrackWrapper::isRecordArmed() const
{
    // Record arming is only relevant for audio tracks
    if (auto audioTrack = dynamic_cast<tracktion::engine::AudioTrack*>(&mTrack))
    {
        // Check if the track is armed for recording
        // This would need to check the track's input devices
        return false; // Placeholder - would need proper implementation
    }
    return false;
}

void TrackWrapper::setRecordArmed(bool armed)
{
    // Record arming control would need to be implemented based on
    // tracktion engine's recording system
    // For now, this is a placeholder
    (void)armed; // Suppress unused parameter warning
}

// Track type checking methods
bool TrackWrapper::isAudioTrack() const
{
    return mTrack.isAudioTrack();
}

bool TrackWrapper::isArrangerTrack() const
{
    return mTrack.isArrangerTrack();
}

bool TrackWrapper::isMarkerTrack() const
{
    return mTrack.isMarkerTrack();
}

bool TrackWrapper::isTempoTrack() const
{
    return mTrack.isTempoTrack();
}

bool TrackWrapper::isChordTrack() const
{
    return mTrack.isChordTrack();
}

bool TrackWrapper::isMasterTrack() const
{
    return mTrack.isMasterTrack();
}

bool TrackWrapper::isFolderTrack() const
{
    return mTrack.isFolderTrack();
}

bool TrackWrapper::isAutomationTrack() const
{
    return mTrack.isAutomationTrack();
}

std::shared_ptr<ClipWrapper> TrackWrapper::addWaveClipToEngine(double startTime, double lengthSeconds)
{
    if (auto clipTrack = dynamic_cast<tracktion::engine::ClipTrack*>(&mTrack))
    {
        tracktion::core::TimeRange timeRange(tracktion::core::TimePosition::fromSeconds(startTime),
                                             tracktion::core::TimeDuration::fromSeconds(lengthSeconds));

        // Use a default audio file for now
        juce::File audioFile = juce::File::getCurrentWorkingDirectory().getChildFile("resources").getChildFile(
                "Hit 10.wav");
        juce::String clipName = "New Wave Clip";
        tracktion::ClipPosition clipPosition;
        clipPosition.time = timeRange;
        clipPosition.offset = tracktion::core::TimeDuration(); // Initialize offset with a default TimeDuration

        if (auto newClip = clipTrack->insertWaveClip(clipName, audioFile, clipPosition, false))
        // Use insertWaveClip with full arguments
        {
            return std::make_shared<ClipWrapper>(*newClip);
        }
    }
    return nullptr;
}

tracktion::engine::Track& TrackWrapper::getTrack() const
{
    return mTrack;
}
