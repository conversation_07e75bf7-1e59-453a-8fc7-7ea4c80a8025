#ifndef TRACKHELPERS_H
#define TRACKHELPERS_H

#include <tracktion_engine/tracktion_engine.h>
#include <juce_core/juce_core.h>

namespace te = tracktion;

namespace TrackHelpers
{
    inline void removeAllClips (te::AudioTrack& track)
    {
        auto clips = track.getClips();

        for (int i = clips.size(); --i >= 0;)
            clips.getUnchecked (i)->removeFromParent();
    }

    inline te::AudioTrack* getOrInsertAudioTrackAt (te::Edit& edit, int index)
    {
        edit.ensureNumberOfAudioTracks (index + 1);
        return te::getAudioTracks (edit)[index];
    }

    inline te::WaveAudioClip::Ptr loadAudioFileAsClip (te::Edit& edit, const juce::File& file)
    {
        // Find the first track and delete all clips from it
        if (auto track = getOrInsertAudioTrackAt (edit, 0))
        {
            removeAllClips (*track);

            // Add a new clip to this track
            te::AudioFile audioFile (edit.engine, file);

            if (audioFile.isValid())
                if (auto newClip = track->insertWaveClip (file.getFileNameWithoutExtension(), file,
                                                          { { {}, te::TimeDuration::fromSeconds (audioFile.getLength()) }, {} }, false))
                    return newClip;
        }

        return {};
    }

    inline void armTrack (te::AudioTrack& t, bool arm, int position = 0)
    {
        auto& edit = t.edit;
        for (auto instance : edit.getAllInputDevices())
            if (te::isOnTargetTrack (*instance, t, position))
                instance->setRecordingEnabled (t.itemID, arm);
    }

    inline bool isTrackArmed (te::AudioTrack& t, int position = 0)
    {
        auto& edit = t.edit;
        for (auto instance : edit.getAllInputDevices())
            if (te::isOnTargetTrack (*instance, t, position))
                return instance->isRecordingEnabled (t.itemID);

        return false;
    }

    inline bool isInputMonitoringEnabled (te::AudioTrack& t, int position = 0)
    {
        for (auto instance : t.edit.getAllInputDevices())
            if (te::isOnTargetTrack (*instance, t, position))
                return instance->isLivePlayEnabled (t);

        return false;
    }

    inline void enableInputMonitoring (te::AudioTrack& t, bool im, int position = 0)
    {
        if (isInputMonitoringEnabled (t, position) != im)
        {
            for (auto instance : t.edit.getAllInputDevices())
            {
                if (te::isOnTargetTrack (*instance, t, position))
                {
                    if (auto mode = instance->getInputDevice().getMonitorMode();
                        mode == te::InputDevice::MonitorMode::on ||  mode == te::InputDevice::MonitorMode::off)
                    {
                        instance->getInputDevice().setMonitorMode (mode == te::InputDevice::MonitorMode::on
                                                                    ? te::InputDevice::MonitorMode::off
                                                                    : te::InputDevice::MonitorMode::on);
                    }
                }
            }
        }
    }

    inline bool trackHasInput (te::AudioTrack& t, int position = 0)
    {
        auto& edit = t.edit;
        for (auto instance : edit.getAllInputDevices())
            if (te::isOnTargetTrack (*instance, t, position))
                return true;

        return false;
    }
}

#endif //TRACKHELPERS_H
