#ifndef AUDIODEVICETOOLS_H
#define AUDIODEVICETOOLS_H

#include <tracktion_engine/tracktion_engine.h>

namespace te = tracktion;

namespace AudioDeviceHelpers
{
    inline void showAudioDeviceSettings(te::Engine& engine)
    // TODO: this is UI interaction: create QML version in QmlModels
    {
        juce::DialogWindow::LaunchOptions o;
        o.dialogTitle = TRANS("Audio Settings");
        o.dialogBackgroundColour = juce::LookAndFeel::getDefaultLookAndFeel().findColour(
                juce::ResizableWindow::backgroundColourId);
        o.content.setOwned(new juce::AudioDeviceSelectorComponent(engine.getDeviceManager().deviceManager,
                                                                  0, 512, 1, 512, false, false, true, true));
        o.content->setSize(400, 600);
        o.launchAsync();
    }
}

#endif //AUDIODEVICETOOLS_H
