#ifndef TRANSPORTHELPERS_H
#define TRANSPORTHELPERS_H

#include <tracktion_engine/tracktion_engine.h>

namespace te = tracktion;
using namespace std::literals;

namespace TransportHelpers
{
    template<typename ClipType>
    typename ClipType::Ptr loopAroundClip (ClipType& clip)
    {
        auto& transport = clip.edit.getTransport();
        transport.setLoopRange (clip.getEditTimeRange());
        transport.looping = true;
        transport.setPosition (0s);
        transport.play (false);

        return clip;
    }

    enum class ReturnToStart { no, yes };

    inline void togglePlay (te::Edit& edit, ReturnToStart rts = ReturnToStart::no)
    {
        auto& transport = edit.getTransport();

        if (transport.isPlaying())
            transport.stop (false, false);
        else
        {
            if (rts == ReturnToStart::yes)
                transport.playFromStart (true);
            else
                transport.play (false);
        }
    }

    inline void toggleRecord (te::Edit& edit)
    {
        auto& transport = edit.getTransport();

        if (transport.isRecording())
            transport.stop (true, false);
        else
            transport.record (false);
    }
}

#endif //TRANSPORTHELPERS_H
