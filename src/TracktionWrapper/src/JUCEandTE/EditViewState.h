//
// Created by <PERSON> on 3/6/25.
//

#ifndef EDITVIEWSTATE_H
#define EDITVIEWSTATE_H

#include "IDs.h"
#include <tracktion_engine/tracktion_engine.h>

using namespace std::literals;
namespace te = tracktion;

//==============================================================================
class EditViewState
{
public:
    EditViewState(te::Edit& e, te::SelectionManager& s) :
        edit(e), selectionManager(s)
    {
        state = edit.state.getOrCreateChildWithName(IDs::EDITVIEWSTATE, nullptr);

        auto um = &edit.getUndoManager();

        showMasterTrack.referTo(state, IDs::showMasterTrack, um, false);
        showGlobalTrack.referTo(state, IDs::showGlobalTrack, um, false);
        showMarkerTrack.referTo(state, IDs::showMarkerTrack, um, false);
        showChordTrack.referTo(state, IDs::showChordTrack, um, false);
        showArrangerTrack.referTo(state, IDs::showArranger, um, false);
        drawWaveforms.referTo(state, IDs::drawWaveforms, um, true);
        showHeaders.referTo(state, IDs::showHeaders, um, true);
        showFooters.referTo(state, IDs::showFooters, um, false);
        showMidiDevices.referTo(state, IDs::showMidiDevices, um, false);
        showWaveDevices.referTo(state, IDs::showWaveDevices, um, true);

        viewX1.referTo(state, IDs::viewX1, um, beatToTime(tracktion::BeatPosition::fromBeats(0)));
        viewX2.referTo(state, IDs::viewX2, um, beatToTime(tracktion::BeatPosition::fromBeats(64)));
        viewY.referTo(state, IDs::viewY, um, 0);
    }

    int timeToX(te::TimePosition time, int width) const
    {
        return juce::roundToInt(((time - viewX1) * width) / (viewX2 - viewX1));
    }

    te::TimePosition xToTime(int x, int width) const
    {
        return toPosition((viewX2 - viewX1) * (double(x) / width)) + toDuration(viewX1.get());
    }

    te::TimePosition beatToTime(te::BeatPosition b) const
    {
        auto& ts = edit.tempoSequence;
        return ts.toTime(b);
    }

    te::Edit& edit;
    te::SelectionManager& selectionManager;

    juce::CachedValue<bool> showMasterTrack, showGlobalTrack, showMarkerTrack, showChordTrack, showArrangerTrack,
                            drawWaveforms, showHeaders, showFooters, showMidiDevices, showWaveDevices;

    juce::CachedValue<te::TimePosition> viewX1, viewX2;
    juce::CachedValue<double> viewY;

    juce::ValueTree state;
};

#endif //EDITVIEWSTATE_H
