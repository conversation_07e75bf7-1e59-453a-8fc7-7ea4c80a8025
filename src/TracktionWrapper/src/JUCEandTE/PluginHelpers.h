#ifndef PLUGINHELPERS_H
#define PLUGINHELPERS_H

#include <memory>
#include <tracktion_engine/tracktion_engine.h>
#include <juce_audio_processors/juce_audio_processors.h> // For juce::KnownPluginList

namespace te = tracktion;

namespace PluginHelpers
{
    inline std::unique_ptr<juce::KnownPluginList::PluginTree> createPluginTree (te::Engine& engine)
    {
        auto& list = engine.getPluginManager().knownPluginList;

        if (auto tree = list.createTree (list.getTypes(), juce::KnownPluginList::sortByManufacturer))
            return tree;

        return {};
    }

    inline void removeFXPlugins (te::AudioTrack& track)
    {
        for (auto p : track.pluginList.getPlugins())
            if (p != track.getVolumePlugin() && p != track.getLevelMeterPlugin())
                p->removeFromParent();
    }
}

#endif //PLUGINHELPERS_H
