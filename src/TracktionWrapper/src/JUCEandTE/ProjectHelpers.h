#ifndef PROJECTHELPERS_H
#define PROJECTHELPERS_H

#include <tracktion_engine/tracktion_engine.h>

namespace te = tracktion;

namespace ProjectHelpers
{
    inline te::Project::Ptr createTempProject (te::Engine& engine)
    {
        auto file = engine.getTemporaryFileManager().getTempDirectory().getChildFile ("temp_project").withFileExtension (te::projectFileSuffix);
        te::ProjectManager::TempProject tempProject (engine.getProjectManager(), file, true);
        return tempProject.project;
    }
}

#endif //PROJECTHELPERS_H
