#ifndef FILEBROWSERHELPERS_H
#define FILEBROWSERHELPERS_H

#include <tracktion_engine/tracktion_engine.h>
#include <functional> // For std::function
#include <juce_core/juce_core.h> // For juce::File

namespace te = tracktion;

namespace FileBrowserHelpers
{
    inline void browseForAudioFile(te::Engine& engine, std::function<void (const juce::File&)> fileChosenCallback)
    // TODO: this is UI interaction: create QML version in QmlModels
    {
        auto fc = std::make_shared<juce::FileChooser>("Please select an audio file to load...",
                                                      engine.getPropertyStorage().getDefaultLoadSaveDirectory(
                                                              "pitchAndTimeExample"),
                                                      engine.getAudioFileFormatManager().readFormatManager.
                                                             getWildcardForAllFormats());

        fc->launchAsync(juce::FileBrowserComponent::openMode + juce::FileBrowserComponent::canSelectFiles,
                        [fc, &engine, callback = std::move(fileChosenCallback)](const juce::FileChooser&)
                        {
                            const auto f = fc->getResult();

                            if (f.existsAsFile())
                                engine.getPropertyStorage().setDefaultLoadSaveDirectory(
                                        "pitchAndTimeExample", f.getParentDirectory());

                            callback(f);
                        });
    }
}

#endif //FILEBROWSERHELPERS_H
