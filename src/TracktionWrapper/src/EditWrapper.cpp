#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/TrackWrapper.h"
#include <tracktion_engine/tracktion_engine.h>
#include <juce_audio_formats/juce_audio_formats.h> // For juce::MidiFile, juce::MemoryInputStream
#include "JUCEandTE/TrackHelpers.h"
#include "JUCEandTE/TransportHelpers.h"
#include "JUCEandTE/IDs.h"

// Base64 decoding utility (from MiniDawDemo_inspiration.h)
static const int B64index[256] =
{
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 63, 62, 62, 63,
        52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 0, 0, 0, 0, 0, 0,
        0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14,
        15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 0, 0, 0, 0, 63,
        0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51
};

const std::string b64decode(const void* data, const size_t& len)
{
    if (len == 0)
        return "";

    unsigned char *p = (unsigned char*) data;
    size_t j = 0,
           pad1 = len % 4 || p[len - 1] == '=',
           pad2 = pad1 && (len % 4 > 2 || p[len - 2] != '=');
    const size_t last = (len - pad1) / 4 << 2;
    std::string result(last / 4 * 3 + pad1 + pad2, '\0');
    unsigned char* str = (unsigned char*) &result[0];

    for (size_t i = 0; i < last; i += 4)
    {
        int n = B64index[p[i]] << 18 | B64index[p[i + 1]] << 12 | B64index[p[i + 2]] << 6 | B64index[p[i + 3]];
        str[j++] = n >> 16;
        str[j++] = n >> 8 & 0xFF;
        str[j++] = n & 0xFF;
    }
    if (pad1)
    {
        int n = B64index[p[last]] << 18 | B64index[p[last + 1]] << 12;
        str[j++] = n >> 16;
        if (pad2)
        {
            n |= B64index[p[last + 2]] << 6;
            str[j++] = n >> 8 & 0xFF;
        }
    }
    return result;
}

std::string b64decode(const std::string& str64)
{
    return b64decode(str64.c_str(), str64.size());
}

EditWrapper::EditWrapper(std::unique_ptr<tracktion::engine::Edit> edit) :
    mEdit(std::move(edit))
{
    std::cout << "EditWrapper::EditWrapper - Constructor called. Edit valid: " << (isEditValid() ? "true" : "false") << std::endl;
}

EditWrapper::~EditWrapper()
{
    std::cout << "EditWrapper::~EditWrapper - Destructor called. Edit valid: " << (isEditValid() ? "true" : "false") << std::endl;

    if (isEditValid())
    {
        try
        {
            // Safely flush any pending transactions if UndoManager is still valid
            auto& undoManager = mEdit->getUndoManager();
            undoManager.beginNewTransaction();
        }
        catch (...)
        {
            // Ignore any exceptions during cleanup - UndoManager might already be destroyed
            std::cout << "EditWrapper::~EditWrapper - Exception caught during UndoManager cleanup" << std::endl;
        }

        try
        {
            // Let TransportControl clean up its context first
            mEdit->getTransport().freePlaybackContext();
        }
        catch (...)
        {
            // Ignore any exceptions during transport cleanup
            std::cout << "EditWrapper::~EditWrapper - Exception caught during Transport cleanup" << std::endl;
        }

        // Smart pointer automatically handles deletion
        mEdit.reset();
    }
}

std::shared_ptr<TrackWrapper> EditWrapper::getTrack(const int index) const
{
    auto tracks = tracktion::engine::getAllTracks(*mEdit); // Dereference mEdit
    if (index >= 0 && index < tracks.size())
    {
        return std::make_shared<TrackWrapper>(static_cast<tracktion::engine::Track&>(*tracks[index]));
    }
    return nullptr;
}

int EditWrapper::getNumTracks() const
{
    return tracktion::engine::getAllTracks(*mEdit).size(); // Dereference mEdit
}

juce::ChangeBroadcaster& EditWrapper::getTransportControlBroadcaster() const
{
    return mEdit->getTransport(); // Use arrow operator
}

std::string EditWrapper::getName() const
{
    return mEdit->getName().toStdString(); // Use arrow operator
}

int EditWrapper::getProjectLengthInBars() const
{
    // Corrected conversion using provided API for TempoSequence and TimePosition
    const auto& timeSigSetting = mEdit->tempoSequence.getTimeSigAt(tracktion::core::TimePosition::fromSeconds(0.0));
    int beatsPerBar = timeSigSetting.numerator;

    tracktion::core::TimePosition timePos = tracktion::core::TimePosition::fromSeconds(mEdit->getLength().inSeconds());
    tracktion::core::BeatPosition beatPos = mEdit->tempoSequence.toBeats(timePos); // Use arrow operator
    return static_cast<int>(beatPos.inBeats() / beatsPerBar);
}

std::shared_ptr<TrackWrapper> EditWrapper::addTrack()
{
    mEdit->ensureNumberOfAudioTracks(tracktion::engine::getAllTracks(*mEdit).size() + 1); // Dereference mEdit
    if (auto newTrack = tracktion::engine::getAudioTracks(*mEdit).getLast()) // Dereference mEdit
    {
        return std::make_shared<TrackWrapper>(*newTrack);
    }
    return nullptr;
}

void EditWrapper::removeTrack(TrackWrapper* trackWrapper)
{
    if (trackWrapper)
    {
        mEdit->deleteTrack(&trackWrapper->getTrack()); // Use arrow operator
    }
}

tracktion::engine::Edit& EditWrapper::getEdit() const
{
    if (!mEdit)
        throw std::runtime_error("EditWrapper: Attempt to access invalid Edit");
    return *mEdit;
}

tracktion::engine::TransportControl& EditWrapper::getTransportControl() const
{
    return mEdit->getTransport();
}

void EditWrapper::saveEdit(bool isForAutosave, bool useBinaryFormat, bool createBackup)
{
    if (isEditValid())
    {
        te::EditFileOperations(*mEdit).save(isForAutosave, useBinaryFormat, createBackup);
    }
}

void EditWrapper::toggleRecord()
{
    if (isEditValid())
    {
        bool wasRecording = mEdit->getTransport().isRecording();
        TransportHelpers::toggleRecord(*mEdit);
        if (wasRecording)
            saveEdit(true, true, false);
    }
}

void EditWrapper::addNewAudioTrack()
{
    if (isEditValid())
    {
        mEdit->ensureNumberOfAudioTracks(te::getAudioTracks(*mEdit).size() + 1);
    }
}

void EditWrapper::clearAllTracks()
{
    if (isEditValid())
    {
        for (auto t : te::getAudioTracks(*mEdit))
            mEdit->deleteTrack(t);
    }
}

void EditWrapper::undo()
{
    if (isEditValid())
    {
        mEdit->getUndoManager().undo();
    }
}

void EditWrapper::redo()
{
    if (isEditValid())
    {
        mEdit->getUndoManager().redo();
    }
}

void EditWrapper::moveToFirstNote()
{
    if (isEditValid())
    {
        auto& transport = mEdit->getTransport();
        transport.position = tracktion::TimePosition::fromSeconds(7.7359);
    }
}

void EditWrapper::createMidiClip()
{
    if (isEditValid())
    {
        auto track = TrackHelpers::getOrInsertAudioTrackAt(*mEdit, 0);

        auto data = b64decode(
                "TVRoZAAAAAYAAQACBABNVHJrAAAAKAD/WQL+AAD/WQL+AAD/WAQGAyQIAP9RAwehILAA/1gEBgMMCAD/LwBNVHJrAAAAapUwwDUAsAdkALAnKgCwCkAAsCoAAJAuZIJlgC4AAJAvZIJlgC8AAJAyWoMAgDIAAJA1boYAgDUAAJA6X4MBgDoAAJA5ZIJ4gDkAAJA4ZIJ3gDgAAJA5ZoMEgDkAAJA1aYQKgDUAhg7/LwA=");
        auto stream = juce::MemoryInputStream(&data[0], data.length(), false);
        auto midiFile = juce::MidiFile();
        midiFile.readFrom(stream);
        const juce::MidiMessageSequence* sequence = midiFile.getTrack(1); // Assume notes on track 1
        for (int j = sequence->getNumEvents(); --j >= 0;)
        {
            auto& m = sequence->getEventPointer(j)->message;
            m.setTimeStamp(m.getTimeStamp() * 0.001);
        }

        auto len = sequence->getEndTime();

        auto time = tracktion::TimeRange(tracktion::TimePosition::fromSeconds(5.0),
                                         tracktion::TimeDuration::fromSeconds(len));
        auto valueTree = juce::ValueTree(te::IDs::MIDICLIP);
        te::MidiClip* clip = dynamic_cast<te::MidiClip*>
        (track->insertClipWithState(valueTree, "Clip", te::TrackItem::Type::midi,
                                    {time, tracktion::TimeDuration::fromSeconds(0.0)}, true, false));
        juce::MidiMessageSequence seq = *sequence;

        seq.addTimeToMessages(5.0);

        clip->mergeInMidiSequence(seq, te::MidiList::NoteAutomationType::none);
        DBG(clip->state.toXmlString ({}));

        clip->setMidiChannel(te::MidiChannel(1));
    }
}

bool EditWrapper::isRecording() const
{
    if (isEditValid())
    {
        return mEdit->getTransport().isRecording();
    }
    return false;
}

int EditWrapper::getTimeSignatureNumerator() const
{
    if (isEditValid())
    {
        const auto& timeSigSetting = mEdit->tempoSequence.getTimeSigAt(tracktion::core::TimePosition::fromSeconds(0.0));
        return timeSigSetting.numerator;
    }
    return 0;
}

int EditWrapper::getTimeSignatureDenominator() const
{
    if (isEditValid())
    {
        const auto& timeSigSetting = mEdit->tempoSequence.getTimeSigAt(tracktion::core::TimePosition::fromSeconds(0.0));
        return timeSigSetting.denominator;
    }
    return 0;
}

void EditWrapper::deleteTrack(TrackWrapper* trackWrapper)
{
    if (isEditValid() && trackWrapper)
    {
        mEdit->deleteTrack(&trackWrapper->getTrack());
    }
}

// Helper methods implementation
bool EditWrapper::isEditValid() const
{
    return mEdit != nullptr;
}
