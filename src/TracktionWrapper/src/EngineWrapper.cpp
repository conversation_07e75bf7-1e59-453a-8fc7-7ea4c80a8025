#include "TracktionWrapper/EngineWrapper.h"
#include "TracktionWrapper/DeviceManagerWrapper.h"
#include "TracktionWrapper/EditWrapper.h"

#include <juce_core/juce_core.h>
#include <tracktion_engine/tracktion_engine.h>

#include "JUCEandTE/ProjectHelpers.h"
#include "JUCEandTE/FileBrowserHelpers.h"
#include "JUCEandTE/TrackHelpers.h"
#include "JUCEandTE/Helpers.h"

#include <iostream>

EngineWrapper::EngineWrapper() :
    mEngine(std::make_unique<tracktion::engine::Engine>("TracktionApp")), // Initialize unique_ptr
    mTempProjectHolder(nullptr) // Initialize temp project holder
{
    std::cout << "EngineWrapper::EngineWrapper() - Engine initialized." << std::endl;
}

std::vector<tracktion::engine::Edit*> EngineWrapper::getActiveEdits() const
{
    auto activeEdits = mEngine->getActiveEdits().getEdits();
    return std::vector<tracktion::engine::Edit*>(activeEdits.begin(), activeEdits.end());
}

EngineWrapper::~EngineWrapper()
{
    // Clean up temp project before engine destruction
    if (mTempProjectHolder)
    {
        delete static_cast<tracktion::engine::ProjectManager::TempProject*>(mTempProjectHolder);
        mTempProjectHolder = nullptr;
    }

    // Engine's destructor will handle all cleanup
}

void EngineWrapper::initProject()
{
    std::cout << "EngineWrapper::initProject() - Initializing ProjectManager..." << std::endl;

    // Initialize the ProjectManager - this is essential for proper project/edit management
    mEngine->getProjectManager().initialise();

    std::cout << "EngineWrapper::initProject() - ProjectManager initialized successfully." << std::endl;
}

tracktion::engine::Project* EngineWrapper::getOrCreateTempProject()
{
    if (!mTempProjectHolder)
    {
        std::cout << "EngineWrapper::getOrCreateTempProject() - Creating temporary project..." << std::endl;
        auto file = mEngine->getTemporaryFileManager().getTempDirectory()
                           .getChildFile("temp_project")
                           .withFileExtension(tracktion::engine::projectFileSuffix);

        auto* tempProject = new tracktion::engine::ProjectManager::TempProject(
                mEngine->getProjectManager(), file, true);
        mTempProjectHolder = tempProject;

        if (tempProject->project)
        {
            std::cout << "EngineWrapper::getOrCreateTempProject() - Temporary project created successfully. ID: "
                    << tempProject->project->getProjectID() << std::endl;
        }
        else
        {
            std::cout << "EngineWrapper::getOrCreateTempProject() - Failed to create temporary project!" << std::endl;
        }
    }

    auto* tempProject = static_cast<tracktion::engine::ProjectManager::TempProject*>(mTempProjectHolder);
    return tempProject ? tempProject->project.get() : nullptr;
}

EditWrapper* EngineWrapper::createAndSetupEdit(const std::string& editFilePath)
{
    juce::File editFile(editFilePath);
    std::cout << "EngineWrapper::createAndSetupEdit - Attempting to create/load edit: " << editFile.getFullPathName() <<
            std::endl;

    std::unique_ptr<tracktion::engine::Edit> newTracktionEdit;

    if (editFile.existsAsFile())
    {
        // Load existing edit file using the proper ProjectManager pattern
        std::cout << "EngineWrapper::createAndSetupEdit - Loading existing edit file..." << std::endl;
        newTracktionEdit = tracktion::engine::loadEditFromFile(*mEngine, editFile);
        std::cout << "EngineWrapper::createAndSetupEdit - Loaded edit from file. Valid: " << (
            newTracktionEdit ? "true" : "false") << std::endl;
    }
    else
    {
        // Create new edit through ProjectManager for proper project management
        std::cout << "EngineWrapper::createAndSetupEdit - Creating new edit through ProjectManager..." << std::endl;
        auto* tempProject = getOrCreateTempProject();

        if (tempProject)
        {
            // Create a new edit item in the project
            auto editItem = tempProject->createNewEdit();
            if (editItem)
            {
                // Get the edit file path from the project item
                auto projectEditFile = editItem->getSourceFile();

                // Copy the edit to the desired location if different
                if (editFile != projectEditFile && editFile.getParentDirectory().createDirectory())
                {
                    if (projectEditFile.copyFileTo(editFile))
                    {
                        std::cout << "EngineWrapper::createAndSetupEdit - Edit copied to desired location: "
                                << editFile.getFullPathName() << std::endl;
                    }
                    else
                    {
                        std::cout <<
                                "EngineWrapper::createAndSetupEdit - Failed to copy edit to desired location, using project location"
                                << std::endl;
                        editFile = projectEditFile;
                    }
                }
                else
                {
                    editFile = projectEditFile;
                }

                // Load the edit from the final location
                newTracktionEdit = tracktion::engine::loadEditFromFile(*mEngine, editFile);
                std::cout << "EngineWrapper::createAndSetupEdit - Created edit through ProjectManager. Valid: " << (
                    newTracktionEdit ? "true" : "false") << std::endl;
            }
            else
            {
                std::cout << "EngineWrapper::createAndSetupEdit - Failed to create edit item in project!" << std::endl;
            }
        }
        else
        {
            std::cout <<
                    "EngineWrapper::createAndSetupEdit - Failed to get temp project, falling back to direct creation..."
                    << std::endl;
            newTracktionEdit = tracktion::engine::createEmptyEdit(*mEngine, editFile);
        }
    }

    if (newTracktionEdit)
    {
        std::cout << "EngineWrapper::createAndSetupEdit - Edit is valid, setting up." << std::endl;
        newTracktionEdit->editFileRetriever = [editFile] { return editFile; };
        newTracktionEdit->playInStopEnabled = true;

        // Initial setup for the new edit (from MiniDawDemo's createTracksAndAssignInputs)
        auto& dm = mEngine->getDeviceManager();

        // MIDI Input setup
        for (auto& midiIn : dm.getMidiInDevices())
        {
            midiIn->setMonitorMode (tracktion::engine::InputDevice::MonitorMode::automatic);
            midiIn->setEnabled (true);
        }

        // Wave Input setup
        for (int i = 0; i < dm.getNumWaveInDevices(); i++)
            if (auto wip = dm.getWaveInDevice (i))
                wip->setStereoPair (false);

        for (int i = 0; i < dm.getNumWaveInDevices(); i++)
        {
            if (auto wip = dm.getWaveInDevice (i))
            {
                wip->setMonitorMode (tracktion::engine::InputDevice::MonitorMode::automatic);
                wip->setEnabled (true);
            }
        }

        newTracktionEdit->getTransport().ensureContextAllocated();

        if (tracktion::engine::getAudioTracks (*newTracktionEdit).size() == 0)
        {
            int trackNum = 0;

            for (auto instance : newTracktionEdit->getAllInputDevices())
            {
                if (instance->getInputDevice().getDeviceType() == tracktion::engine::InputDevice::physicalMidiDevice ||
                    instance->getInputDevice().getDeviceType() == tracktion::engine::InputDevice::waveDevice)
                {
                    if (auto t = TrackHelpers::getOrInsertAudioTrackAt (*newTracktionEdit, trackNum))
                    {
                        [[ maybe_unused ]] auto result = instance->setTarget (t->itemID, true, &newTracktionEdit->getUndoManager(), 0);
                        instance->setRecordingEnabled (t->itemID, true);

                        trackNum++;
                    }
                }
            }
        }

        newTracktionEdit->restartPlayback();
        std::cout << "EngineWrapper::createAndSetupEdit - Edit setup complete, creating EditWrapper." << std::endl;
        return new EditWrapper(std::move(newTracktionEdit));
    }
    std::cout << "EngineWrapper::createAndSetupEdit - Edit creation/loading failed, returning nullptr." << std::endl;
    return nullptr;
}

std::string EngineWrapper::findRecentEdit(const std::string& directoryPath)
{
    juce::File f = Helpers::findRecentEdit(juce::File(directoryPath));
    if (f.existsAsFile())
        return f.getFullPathName().toStdString();
    return {};
}

void EngineWrapper::revealEditFileInOS(const std::string& filePath)
{
    juce::File(filePath).revealToUser();
}

std::unique_ptr<tracktion::engine::Edit> EngineWrapper::loadEditFromFile(const std::string& filePath)
{
    juce::File file(filePath);
    return te::loadEditFromFile(*mEngine, file);
}

std::unique_ptr<tracktion::engine::Edit> EngineWrapper::createEmptyEdit(const std::string& filePath)
{
    juce::File file(filePath);
    return te::createEmptyEdit(*mEngine, file);
}

std::string EngineWrapper::getTemporaryFileManagerDirectory() const
{
    return mEngine->getTemporaryFileManager().getTempDirectory().getFullPathName().toStdString();
}

void EngineWrapper::deleteTemporaryFileManagerDirectoryRecursively()
{
    // Clear active edits
    auto activeEdits = mEngine->getActiveEdits().getEdits();
    activeEdits.clear();

    // Then cleanup temp files
    mEngine->getTemporaryFileManager().getTempDirectory().deleteRecursively();
}

tracktion::engine::Engine& EngineWrapper::getEngine() const
{
    return *mEngine;
}

tracktion::engine::DeviceManager& EngineWrapper::getDeviceManager() const
{
    return mEngine->getDeviceManager();
}

int EngineWrapper::getTimeSignatureNumerator() const
{
    // This should ideally be retrieved from the current active edit's transport
    // For now, returning a default or assuming a global tempo sequence if available
    return 4; // Default
}

int EngineWrapper::getTimeSignatureDenominator() const
{
    // This should ideally be retrieved from the current active edit's transport
    // For now, returning a default or assuming a global tempo sequence if available
    return 4; // Default
}

int EngineWrapper::getAudioBlockSize() const
{
    return mEngine->getDeviceManager().getBlockSize();
}

double EngineWrapper::getAudioSampleRate() const
{
    return mEngine->getDeviceManager().getSampleRate();
}

tracktion::engine::ArrangerTrack* EngineWrapper::getArrangerTrack() const
{
    // This should be retrieved from the current active edit
    return nullptr; // Placeholder
}

tracktion::engine::MarkerTrack* EngineWrapper::getMarkerTrack() const
{
    // This should be retrieved from the current active edit
    return nullptr; // Placeholder
}

tracktion::engine::TempoTrack* EngineWrapper::getTempoTrack() const
{
    // This should be retrieved from the current active edit
    return nullptr; // Placeholder
}

tracktion::engine::ChordTrack* EngineWrapper::getChordTrack() const
{
    // This should be retrieved from the current active edit
    return nullptr; // Placeholder
}

tracktion::engine::MasterTrack* EngineWrapper::getMasterTrack() const
{
    // This should be retrieved from the current active edit
    return nullptr; // Placeholder
}
