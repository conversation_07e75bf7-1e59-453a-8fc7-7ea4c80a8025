#include "TracktionWrapper/TransportWrapper.h"
#include <tracktion_engine/tracktion_engine.h> // Required for tracktion::engine::Engine::getMidiTimecodeSource() etc.
#include "TracktionWrapper/WrapperChangeListener.h"
#include <iostream>
#include <algorithm> // For std::remove

// Internal JUCE change listener to forward events
class TransportWrapper::InternalChangeListener : public juce::ChangeListener
{
public:
    InternalChangeListener(TransportWrapper& wrapper) : mWrapper(wrapper) {}

    void changeListenerCallback(juce::ChangeBroadcaster* source) override
    {
        std::cout << "TransportWrapper::InternalChangeListener - Change event received from " << source << std::endl;
        std::cout << "TransportWrapper::InternalChangeListener - Current isPlaying: " << mWrapper.isPlaying() <<
                std::endl;

        // Forward the change notification to the custom listeners
        for (auto listener : mWrapper.mListeners)
        {
            if (listener)
            {
                std::cout << "TransportWrapper::InternalChangeListener - Forwarding to listener: " << listener <<
                        std::endl;
                listener->wrapperChanged(&mWrapper);
            }
        }
    }

private:
    TransportWrapper& mWrapper;
};

TransportWrapper::TransportWrapper(tracktion::engine::TransportControl& tc, tracktion::engine::Edit& edit) :
    mTransportControl(tc),
    mEdit(edit),
    mInternalChangeListener(std::make_unique<InternalChangeListener>(*this)) // Initialize internal listener
{
    // Add the internal listener to the JUCE ChangeBroadcaster
    mTransportControl.addChangeListener(mInternalChangeListener.get());
}

TransportWrapper::~TransportWrapper()
{
    // Remove the internal listener from the JUCE ChangeBroadcaster
    mTransportControl.removeChangeListener(mInternalChangeListener.get());
}

void TransportWrapper::addChangeListener(WrapperChangeListener* listener)
{
    if (listener)
        mListeners.push_back(listener);
}

void TransportWrapper::removeChangeListener(WrapperChangeListener* listener)
{
    mListeners.erase(std::remove(mListeners.begin(), mListeners.end(), listener), mListeners.end());
}

void TransportWrapper::play() const
{
    std::cout << "TransportWrapper::play() - Attempting to start playback" << std::endl;
    mTransportControl.play(false);
    std::cout << "TransportWrapper::play() - After play() call, isPlaying: " << mTransportControl.isPlaying() << std::endl;
}

void TransportWrapper::stop() const
{
    std::cout << "TransportWrapper::stop() - Attempting to stop playback" << std::endl;
    mTransportControl.stop(false, false);
    std::cout << "TransportWrapper::stop() - After stop() call, isPlaying: " << mTransportControl.isPlaying() << std::endl;
}

void TransportWrapper::togglePlayPause() const
{
    std::cout << "TransportWrapper::togglePlayPause() - Current isPlaying state: " << mTransportControl.isPlaying() << std::endl;
    if (mTransportControl.isPlaying())
    {
        std::cout << "TransportWrapper::togglePlayPause() - Attempting to stop" << std::endl;
        mTransportControl.stop(false, false);
    }
    else
    {
        std::cout << "TransportWrapper::togglePlayPause() - Attempting to play" << std::endl;
        mTransportControl.play(false);
    }
    std::cout << "TransportWrapper::togglePlayPause() - New isPlaying state: " << mTransportControl.isPlaying() << std::endl;
}

void TransportWrapper::rewind()
{
    std::cout << "TransportWrapper::rewind() - Moving transport position backward by 4 beats" << std::endl;

    // Get current beat position
    tracktion::BeatPosition currentBeat = mEdit.tempoSequence.toBeats(mTransportControl.position.get());
    std::cout << "TransportWrapper::rewind() - Current beat position: " << currentBeat.inBeats() << std::endl;

    // Move backward by 4 beats, but don't go below 0
    double newBeatValue = std::max(0.0, currentBeat.inBeats() - 4.0);
    tracktion::BeatPosition newBeat = tracktion::BeatPosition::fromBeats(newBeatValue);
    std::cout << "TransportWrapper::rewind() - New beat position: " << newBeat.inBeats() << std::endl;

    // Convert back to time and set position
    tracktion::TimePosition newTime = mEdit.tempoSequence.toTime(newBeat);
    mTransportControl.position = newTime;
    std::cout << "TransportWrapper::rewind() - Set new time position: " << newTime.inSeconds() << " seconds" << std::endl;
}

void TransportWrapper::fastForward()
{
    std::cout << "TransportWrapper::fastForward() - Moving transport position forward by 4 beats" << std::endl;

    // Get current beat position
    tracktion::BeatPosition currentBeat = mEdit.tempoSequence.toBeats(mTransportControl.position.get());
    std::cout << "TransportWrapper::fastForward() - Current beat position: " << currentBeat.inBeats() << std::endl;

    // Move forward by 4 beats
    double newBeatValue = currentBeat.inBeats() + 4.0;
    tracktion::BeatPosition newBeat = tracktion::BeatPosition::fromBeats(newBeatValue);
    std::cout << "TransportWrapper::fastForward() - New beat position: " << newBeat.inBeats() << std::endl;

    // Convert back to time and set position
    tracktion::TimePosition newTime = mEdit.tempoSequence.toTime(newBeat);
    mTransportControl.position = newTime;
    std::cout << "TransportWrapper::fastForward() - Set new time position: " << newTime.inSeconds() << " seconds" << std::endl;
}

bool TransportWrapper::isPlaying() const
{
    return mTransportControl.isPlaying();
}

bool TransportWrapper::isRecording() const
{
    return mTransportControl.isRecording();
}

double TransportWrapper::getCurrentTimeInSeconds() const
{
    return mEdit.getTransport().position.get().inSeconds();
}

double TransportWrapper::getCurrentTimeInBars() const
{
    // Convert TimePosition to BeatPosition, then get value in beats, and convert to bars
    // Assuming 4 beats per bar for simplicity. A more robust solution would use edit->tempoSequence.getTimeSignature().numerator / edit->tempoSequence.getTimeSignature().denominator
    return mEdit.tempoSequence.toBeats(mTransportControl.position.get()).inBeats() / 4.0; // Return time in bars
}

double TransportWrapper::getTempoBpm() const
{
    // Get the tempo setting at the current transport position
    if (mEdit.tempoSequence.getTempo(0)) // Check if tempo setting exists
    {
        return mEdit.tempoSequence.getTempo(0)->bpm;
    }
    return 120.0; // Default tempo
}

void TransportWrapper::setTempoBpm(double bpm)
{
    // tracktion::engine::TransportControl does not directly set tempo.
    // This functionality needs to be implemented at the TransportModel level using Edit context.
    juce::ignoreUnused(bpm);
}

void TransportWrapper::setPositionInSeconds(double seconds) const
{
    mTransportControl.setPosition(tracktion::TimePosition::fromSeconds(seconds)); // Corrected API
}
