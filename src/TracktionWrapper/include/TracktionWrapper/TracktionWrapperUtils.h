#pragma once

#include <memory>

// Forward declarations
class EditWrapper;
class TransportWrapper;
class EditViewWrapper;
class SelectionManagerWrapper;
class EngineWrapper;

namespace tracktion
{
    inline namespace engine
    {
        class Edit;
        class TransportControl;
        class SelectionManager;
        class Engine;
        class DeviceManager;
    }
}

/**
 * @brief Utility class for creating Tracktion wrapper objects from other wrappers
 * 
 * This class provides static utility methods to create wrapper objects from existing
 * wrapper objects, following consistent patterns throughout the codebase.
 */
class TracktionWrapperUtils
{
public:
    /**
     * @brief Create a TransportWrapper from an EditWrapper
     * 
     * This utility method extracts the TransportControl and Edit from an EditWrapper
     * and creates a new TransportWrapper instance. This pattern is commonly used
     * when creating edit-dependent models.
     * 
     * @param editWrapper The EditWrapper to extract transport information from
     * @return std::unique_ptr<TransportWrapper> A new TransportWrapper instance, 
     *         or nullptr if editWrapper is null or invalid
     * 
     * @note The returned TransportWrapper takes references to the Edit and TransportControl
     *       owned by the EditWrapper, so the EditWrapper must remain valid for the
     *       lifetime of the returned TransportWrapper.
     */
    static std::unique_ptr<TransportWrapper> createTransportWrapper(EditWrapper* editWrapper);

    /**
     * @brief Check if an EditWrapper is valid and has the necessary components
     * 
     * @param editWrapper The EditWrapper to validate
     * @return bool True if the EditWrapper is valid and has a valid Edit
     */
    static bool isEditWrapperValid(EditWrapper* editWrapper);

    /**
     * @brief Get the Edit reference from an EditWrapper safely
     * 
     * @param editWrapper The EditWrapper to extract the Edit from
     * @return tracktion::engine::Edit* Pointer to the Edit, or nullptr if invalid
     */
    static tracktion::engine::Edit* getEditFromWrapper(EditWrapper* editWrapper);

    /**
     * @brief Get the TransportControl reference from an EditWrapper safely
     *
     * @param editWrapper The EditWrapper to extract the TransportControl from
     * @return tracktion::engine::TransportControl* Pointer to the TransportControl, or nullptr if invalid
     */
    static tracktion::engine::TransportControl* getTransportControlFromWrapper(EditWrapper* editWrapper);

    /**
     * @brief Create an EditViewWrapper from an EditWrapper and SelectionManagerWrapper
     *
     * This utility method extracts the Edit from EditWrapper and SelectionManager from
     * SelectionManagerWrapper to create a new EditViewWrapper instance. This pattern
     * is used in AppModel::createEditDependentModels.
     *
     * @param editWrapper The EditWrapper to extract the Edit from
     * @param selectionManagerWrapper The SelectionManagerWrapper to extract SelectionManager from
     * @return std::shared_ptr<EditViewWrapper> A new EditViewWrapper instance,
     *         or nullptr if either wrapper is null or invalid
     *
     * @note The returned EditViewWrapper takes references to the Edit and SelectionManager
     *       owned by the respective wrappers, so both wrappers must remain valid for the
     *       lifetime of the returned EditViewWrapper.
     */
    static std::shared_ptr<EditViewWrapper> createEditViewWrapper(EditWrapper* editWrapper,
                                                                  SelectionManagerWrapper* selectionManagerWrapper);

    /**
     * @brief Create a SelectionManagerWrapper from an EngineWrapper
     *
     * This utility method extracts the Engine from EngineWrapper and creates a new
     * SelectionManagerWrapper instance. This pattern is used in AppModel initialization.
     *
     * @param engineWrapper The EngineWrapper to extract the Engine from
     * @return std::unique_ptr<SelectionManagerWrapper> A new SelectionManagerWrapper instance,
     *         or nullptr if engineWrapper is null or invalid
     *
     * @note The returned SelectionManagerWrapper takes a reference to the Engine
     *       owned by the EngineWrapper, so the EngineWrapper must remain valid for the
     *       lifetime of the returned SelectionManagerWrapper.
     */
    static std::unique_ptr<SelectionManagerWrapper> createSelectionManagerWrapper(EngineWrapper* engineWrapper);

    /**
     * @brief Check if an EngineWrapper is valid and has the necessary components
     *
     * @param engineWrapper The EngineWrapper to validate
     * @return bool True if the EngineWrapper is valid and has a valid Engine
     */
    static bool isEngineWrapperValid(EngineWrapper* engineWrapper);

    /**
     * @brief Check if a SelectionManagerWrapper is valid
     *
     * @param selectionManagerWrapper The SelectionManagerWrapper to validate
     * @return bool True if the SelectionManagerWrapper is valid
     */
    static bool isSelectionManagerWrapperValid(SelectionManagerWrapper* selectionManagerWrapper);

    /**
     * @brief Check if a TransportWrapper is valid
     *
     * @param transportWrapper The TransportWrapper to validate
     * @return bool True if the TransportWrapper is valid
     */
    static bool isTransportWrapperValid(TransportWrapper* transportWrapper);

    /**
     * @brief Get the Engine reference from an EngineWrapper safely
     *
     * @param engineWrapper The EngineWrapper to extract the Engine from
     * @return tracktion::engine::Engine* Pointer to the Engine, or nullptr if invalid
     */
    static tracktion::engine::Engine* getEngineFromWrapper(EngineWrapper* engineWrapper);

    /**
     * @brief Get the SelectionManager reference from a SelectionManagerWrapper safely
     *
     * @param selectionManagerWrapper The SelectionManagerWrapper to extract the SelectionManager from
     * @return tracktion::engine::SelectionManager* Pointer to the SelectionManager, or nullptr if invalid
     */
    static tracktion::engine::SelectionManager* getSelectionManagerFromWrapper(
            SelectionManagerWrapper* selectionManagerWrapper);

    /**
     * @brief Get the DeviceManager reference from an EngineWrapper safely
     *
     * @param engineWrapper The EngineWrapper to extract the DeviceManager from
     * @return tracktion::engine::DeviceManager* Pointer to the DeviceManager, or nullptr if invalid
     */
    static tracktion::engine::DeviceManager* getDeviceManagerFromWrapper(EngineWrapper* engineWrapper);

private:
    // Static utility class - no instances allowed
    TracktionWrapperUtils() = delete;
    ~TracktionWrapperUtils() = delete;
    TracktionWrapperUtils(const TracktionWrapperUtils&) = delete;
    TracktionWrapperUtils& operator=(const TracktionWrapperUtils&) = delete;
};
