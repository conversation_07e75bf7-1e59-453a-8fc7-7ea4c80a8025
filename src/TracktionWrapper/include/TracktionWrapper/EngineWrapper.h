#pragma once
#include <memory>
#include <string>
#include <vector>

#include "JuceInitialiserWrapper.h"

class EditWrapper;
class DeviceManagerWrapper;

// Forward declarations for tracktion_engine classes
namespace tracktion
{
    inline namespace engine
    {
        // Use inline namespace for consistency
        class Engine;
        class Edit;
        class ArrangerTrack;
        class MarkerTrack;
        class TempoTrack;
        class ChordTrack;
        class MasterTrack;
        class DeviceManager;
        class TransportControl;
        class Project;
    }
}

// Forward declarations for JUCE classes
namespace juce
{
    template<class ObjectType>
    class ReferenceCountedObjectPtr;
}

class EngineWrapper final
{
public:
    EngineWrapper();
    ~EngineWrapper();

    tracktion::engine::Engine& getEngine() const;
    tracktion::engine::DeviceManager& getDeviceManager() const;

    EditWrapper* createAndSetupEdit(const std::string& editFilePath);
    std::string findRecentEdit(const std::string& directoryPath);
    void revealEditFileInOS(const std::string& filePath);

    std::string getTemporaryFileManagerDirectory() const;
    void deleteTemporaryFileManagerDirectoryRecursively(); // Keep for internal use

    int getTimeSignatureNumerator() const;
    int getTimeSignatureDenominator() const;
    int getAudioBlockSize() const;
    double getAudioSampleRate() const;

    tracktion::engine::ArrangerTrack* getArrangerTrack() const;
    tracktion::engine::MarkerTrack* getMarkerTrack() const;
    tracktion::engine::TempoTrack* getTempoTrack() const;
    tracktion::engine::ChordTrack* getChordTrack() const;
    tracktion::engine::MasterTrack* getMasterTrack() const;

private:
    // Initialize JUCE before engine
    JuceInitialiserWrapper mJuceInitialiser;
    // Use forward declared types as pointers/references
    std::unique_ptr<tracktion::engine::Engine> mEngine;

    // Project management - using proper type with forward declaration
    std::unique_ptr<juce::ReferenceCountedObjectPtr<tracktion::engine::Project>> mTempProject;

    // Internal project management helper
    tracktion::engine::Project* getOrCreateTempProject();

    // Keep for internal use
    std::unique_ptr<tracktion::engine::Edit> loadEditFromFile(const std::string& filePath);
    std::unique_ptr<tracktion::engine::Edit> createEmptyEdit(const std::string& filePath);

    // Get active edits from Engine
    std::vector<tracktion::engine::Edit*> getActiveEdits() const;

};
