#pragma once

#include <memory>

// Forward declarations
class EditViewState; // Forward declare EditViewState

namespace tracktion
{
    inline namespace engine
    {
        class Edit;
        class SelectionManager;
    }
}

class EditViewWrapper
{
public:
    explicit EditViewWrapper(tracktion::engine::Edit& e, tracktion::engine::SelectionManager& s);
    // Constructor takes a reference
    ~EditViewWrapper();

    // Expose EditViewState properties and methods
    bool showMasterTrack() const;
    bool showGlobalTrack() const;
    bool showMarkerTrack() const;
    bool showChordTrack() const;
    bool showArrangerTrack() const;
    bool drawWaveforms() const;
    void setDrawWaveforms(bool draw);
    bool showHeaders() const;
    bool showFooters() const;
    bool showMidiDevices() const;
    bool showWaveDevices() const;
    double viewX1() const;
    double viewX2() const;
    double viewY() const;

    int timeToX(double timeInSeconds, int width) const;
    double xToTime(int x, int width) const;

private:
    std::unique_ptr<EditViewState> mEditViewState; // Store a reference
};
