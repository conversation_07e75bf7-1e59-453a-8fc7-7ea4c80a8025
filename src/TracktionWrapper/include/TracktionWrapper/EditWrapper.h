#pragma once
#include <memory>
#include <vector> // For std::vector
#include "TrackWrapper.h"

// Forward declarations for tracktion_engine classes
namespace tracktion
{
    inline namespace engine
    {
        // Use inline namespace for consistency
        class Edit;
        class TransportControl;
        class SelectionManager;
        class EditViewState;
        class Clip;
        class Track;
        class Plugin;
    }
}

// Forward declarations for JUCE classes
namespace juce
{
    class ChangeBroadcaster;
    class MidiFile;
    class MemoryInputStream;
}

class EditWrapper
{
public:
    explicit EditWrapper(std::unique_ptr<tracktion::engine::Edit> edit);
    ~EditWrapper();

    std::string getName() const;
    int getProjectLengthInBars() const;
    std::shared_ptr<TrackWrapper> addTrack();
    void removeTrack(TrackWrapper* trackWrapper);

    std::shared_ptr<TrackWrapper> getTrack(int index) const;
    int getNumTracks() const;
    juce::ChangeBroadcaster& getTransportControlBroadcaster() const; // Changed return type
    tracktion::engine::Edit& getEdit() const; // Keep this for internal wrapper use
    tracktion::engine::TransportControl& getTransportControl() const;

    void deleteTrack(TrackWrapper* trackWrapper);

    void saveEdit(bool isForAutosave, bool useBinaryFormat, bool createBackup);
    void toggleRecord();
    void addNewAudioTrack();
    void clearAllTracks();
    void undo();
    void redo();
    bool canUndo() const;
    bool canRedo() const;
    void moveToFirstNote();
    void createMidiClip();
    bool isRecording() const;

    int getTimeSignatureNumerator() const;
    int getTimeSignatureDenominator() const;

private:
    // Owned Edit object
    std::unique_ptr<tracktion::engine::Edit> mEdit;

    // Helper methods
    bool isEditValid() const;
};
