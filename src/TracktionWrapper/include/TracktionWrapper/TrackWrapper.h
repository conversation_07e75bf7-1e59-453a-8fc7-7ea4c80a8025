#pragma once
#include <memory>
#include <string>
#include <vector> // For std::vector
#include "ClipWrapper.h"

// Forward declarations for tracktion_engine classes
namespace tracktion
{
    inline namespace engine
    {
        class Track;
        class Clip; // Forward declare Clip
        class SelectionManager; // Forward declare SelectionManager
    }
}

class TrackWrapper final
{
public:
    explicit TrackWrapper(tracktion::engine::Track& t);
    ~TrackWrapper(); // No override needed

    [[nodiscard]] std::string getName() const;
    std::shared_ptr<ClipWrapper> getClip(int index);
    [[nodiscard]] int getNumClips() const;

    // Add solo/mute/type methods as per plan
    [[nodiscard]] bool isSolo() const;
    void setSolo(bool solo);
    [[nodiscard]] bool isMuted() const;
    void setMuted(bool muted);

    // Track type methods
    [[nodiscard]] std::string getTrackType() const;
    [[nodiscard]] int getTrackTypeEnum() const;

    // Input monitoring and record arming (for audio tracks)
    [[nodiscard]] bool isInputMonitoring() const;
    void setInputMonitoring(bool monitoring);
    [[nodiscard]] bool isRecordArmed() const;
    void setRecordArmed(bool armed);

    // Track type checking methods
    [[nodiscard]] bool isAudioTrack() const;
    [[nodiscard]] bool isArrangerTrack() const;
    [[nodiscard]] bool isMarkerTrack() const;
    [[nodiscard]] bool isTempoTrack() const;
    [[nodiscard]] bool isChordTrack() const;
    [[nodiscard]] bool isMasterTrack() const;
    [[nodiscard]] bool isFolderTrack() const;
    [[nodiscard]] bool isAutomationTrack() const;

    // New method to add a clip to the underlying tracktion::engine::Track
    std::shared_ptr<ClipWrapper> addWaveClipToEngine(double startTime, double lengthSeconds);

    tracktion::engine::Track& getTrack() const;

private:
    tracktion::engine::Track& mTrack;
};
