#pragma once
#include <memory>
#include <string>
#include <vector> // For std::vector
#include "ClipWrapper.h"

// Forward declarations for tracktion_engine classes
namespace tracktion
{
    inline namespace engine
    {
        class Track;
        class Clip; // Forward declare Clip
        class SelectionManager; // Forward declare SelectionManager
    }
}

class TrackWrapper final
{
public:
    explicit TrackWrapper(tracktion::engine::Track& t);
    ~TrackWrapper(); // No override needed

    [[nodiscard]] std::string getName() const;
    std::shared_ptr<ClipWrapper> getClip(int index);
    [[nodiscard]] int getNumClips() const;

    // Add solo/mute/type methods as per plan
    [[nodiscard]] bool isSolo() const;
    void setSolo(bool solo);
    [[nodiscard]] bool isMuted() const;
    void setMuted(bool muted);
    // Assuming tracktion_engine::Track has a way to get its type
    // For now, return a string or an int that can be mapped to an enum
    [[nodiscard]] std::string getTrackType() const;
    // void setTrackType(const std::string& type); // If track type is mutable

    // New method to add a clip to the underlying tracktion::engine::Track
    std::shared_ptr<ClipWrapper> addWaveClipToEngine(double startTime, double lengthSeconds);

    tracktion::engine::Track& getTrack() const;

private:
    tracktion::engine::Track& mTrack;
};
