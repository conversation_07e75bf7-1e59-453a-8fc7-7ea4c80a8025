#pragma once
#include <vector> // To manage listeners

class WrapperChangeListener;
// Forward declarations for tracktion_engine classes

namespace tracktion
{
    inline namespace engine
    {
        // Use inline namespace for consistency
        class TransportControl;
        class Edit;
    }
}


class TransportWrapper final
{
public:
    explicit TransportWrapper(tracktion::engine::TransportControl& tc, tracktion::engine::Edit& edit);
    ~TransportWrapper();

    void addChangeListener(WrapperChangeListener* listener);
    void removeChangeListener(WrapperChangeListener* listener);

    void play() const;
    void stop() const;
    void togglePlayPause() const;
    void rewind();
    void fastForward();
    [[nodiscard]] bool isPlaying() const;
    [[nodiscard]] bool isRecording() const;
    [[nodiscard]] double getCurrentTimeInSeconds() const;
    [[nodiscard]] double getCurrentTimeInBars() const;
    [[nodiscard]] double getTempoBpm() const;
    void setTempoBpm(double bpm);
    void setPositionInSeconds(double seconds) const;

private:
    tracktion::engine::TransportControl& mTransportControl;
    tracktion::engine::Edit& mEdit;

    // Custom listener management
    std::vector<WrapperChangeListener*> mListeners;

    // Internal JUCE change listener to forward events
    class InternalChangeListener;
    std::unique_ptr<InternalChangeListener> mInternalChangeListener;
};
