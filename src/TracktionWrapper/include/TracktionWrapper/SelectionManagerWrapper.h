#pragma once
#include <memory>
#include <vector> // To manage listeners

class WrapperChangeListener;

namespace juce
{
    class ChangeBroadcaster;
} // For ChangeListener in TransportModel

// Forward declarations for tracktion_engine classes
namespace tracktion
{
    inline namespace engine
    {
        class Engine;
        class SelectionManager;
    }
}

class SelectionManagerWrapper final
{
public:
    explicit SelectionManagerWrapper(tracktion::engine::Engine& engine);
    ~SelectionManagerWrapper();

    void addChangeListener(WrapperChangeListener* listener);
    void removeChangeListener(WrapperChangeListener* listener);

    tracktion::engine::SelectionManager& getSelectionManager() const;
    int getNumObjectsSelected() const;
    void deleteSelected() const;

private:
    std::unique_ptr<tracktion::engine::SelectionManager> mSelectionManager;

    // Custom listener management
    std::vector<WrapperChangeListener*> mListeners;

    // Internal JUCE change listener to forward events
    class InternalChangeListener;
    std::unique_ptr<InternalChangeListener> mInternalChangeListener;
};
