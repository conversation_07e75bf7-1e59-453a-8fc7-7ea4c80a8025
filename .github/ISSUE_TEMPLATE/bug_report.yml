name: Bug Report
description: File a bug report
title: "[Bug]: "
body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting an issue with Tracktion Engine.
  - type: textarea
    id: repro
    attributes:
      label: Detailed steps on how to reproduce the bug
      description: If possible please use already existing TE code such as the examples or unit tests
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What is the expected behaviour?
    validations:
      required: true
  - type: textarea
    id: unittest
    attributes:
      label: Unit test to reproduce the error?
      description: If possible please use the template below to create a failing unit test. This will enable us to quickly reproduce the issue and validate your use case is fixed.
      value: |
        /*
            Copy this code in to your application, replacing the test cases in the runTest function.
            Then call runTest() from somewhere in your application.
        */
        class TestClassName  : public juce::UnitTest
        {
        public:
            TestClassName()
                : juce::UnitTest ("TestClass", "tracktion_engine")
            {
            }
        
            void runTest() override
            {
                // Group tests in to sections with beginTest
                beginTest ("Test section");
                {
                    expect (1 == 1);        // Test an expression for true
                    expectEquals (1, 1);    // Test two values are equal
                    expectNotEquals (1, 1); // Test two values are not equal: FAILS
                }
            }
        };
        
        // Creates an instance of the test so you can run it
        static TestClassName testClassName;
        
        // Call this from your applications
        inline void runTest()
        {
            juce::UnitTestRunner runner;
            runner.runTests ({ &testClassName });
        }
      render: bash
    validations:
      required: false
  - type: dropdown
    id: os
    attributes:
      label: Operating systems
      description: What operating systems do you see the bug on?
      multiple: true
      options:
        - Windows
        - macOS
        - Linux
        - iOS
        - Android
        - Other
    validations:
      required: true
  - type: textarea
    id: osversion
    attributes:
      label: What versions of the operating systems?
    validations:
      required: true
  - type: dropdown
    id: architecture
    attributes:
      label: Architectures
      description: What types of machine do you see the bug on?
      multiple: true
      options:
        - x86_64
        - ARM
        - Other
    validations:
      required: true
  - type: textarea
    id: stacktrace
    attributes:
      label: Stacktrace
      description: Please copy and paste any relevant stack trace. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: dropdown
    id: pluginformat
    attributes:
      label: Plug-in formats (if applicable)
      multiple: true
      options:
        - VST2
        - VST3
        - AU
        - AUv3
        - AAX
        - LV2
        - Standalone
  - type: textarea
    id: pluginhost
    attributes:
      label: Plug-in host applications (DAWs) (if applicable)
  - type: dropdown
    id: branch
    attributes:
      label: Testing on the `develop` branch
      description: We have often already fixed bugs on our `develop` branch. Please confirm if you have tested with the latest commit.
      options:
        - The bug is present on the `develop` branch
        - I have not tested against the `develop` branch
    validations:
      required: true
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://berlincodeofconduct.org/)
      options:
        - label: I agree to follow the Code of Conduct
          required: true
