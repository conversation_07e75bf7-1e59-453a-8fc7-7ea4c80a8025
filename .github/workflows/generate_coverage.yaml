name: Coverage
on:
  push:
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: linux
            os: ubuntu-latest
          # - name: macos
          #   os: macos-latest
          # - name: windows
          #   os: windows-latest
    steps:
      - name: Install dependencies
        if: runner.os == 'Linux'
        run: |
          sudo add-apt-repository ppa:ubuntu-toolchain-r/test
          sudo apt-get update
          sudo apt-get install -y gcc-11 g++-11 freeglut3-dev g++ libasound2-dev libcurl4-openssl-dev libfreetype6-dev libjack-jackd2-dev libx11-dev libxcomposite-dev libxcursor-dev libxinerama-dev libxrandr-dev mesa-common-dev ladspa-sdk webkit2gtk-4.0 libgtk-3-dev xvfb ninja-build lcov

      # - name: Setup Cpp
      #   if: runner.os == 'Windows'
      #   uses: aminya/setup-cpp@v1
      #   with:
      #     gcovr: true
      #     opencppcoverage: true

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          persist-credentials: false

      - name: Add Rubber Band submodule for testing
        run: |
          git submodule add -f https://github.com/breakfastquay/rubberband.git modules/3rd_party/rubberband
          git submodule update --init

      # - name: Generate coverage report
      #   run: |
      #     tests/coverage/generate_coverage_report

      # - name: Windows coverage
      #   if: runner.os == 'Windows'
      #   working-directory: ./tests/coverage
      #   run: |
      #     cmake -B build -A x64
      #     cmake --build ./build --target coverage --config RelWithDebInfo --parallel
      #     OpenCppCoverage.exe --config_file coverage.txt -- ./build/coverage_artefacts/RelWithDebInfo/coverage.exe
      #   shell: bash

      - name: Linux coverage
        if: runner.os == 'Linux'
        working-directory: ./tests/coverage
        run: |
          cmake -G Ninja -B build -DCMAKE_BUILD_TYPE=RelWithDebInfo
          cmake --build ./build --target coverage --config RelWithDebInfo --parallel
          build/coverage_artefacts/RelWithDebInfo/coverage
          lcov --capture --directory "build" --output-file coverage.info --rc geninfo_unexecuted_blocks=1 --ignore-errors negative --ignore-errors unused \
            --exclude "tests/coverage/*" \
            --exclude "examples/common/*" \
            --exclude "tests/coverage/coverage.h" \
            --exclude "*juce_*" \
            --exclude "*choc_*" \
            --exclude "*3rd_Party*" \
            --exclude "*3rd_party*" \
            --exclude "modules/3rd_party/*" \
            --exclude "/Applications*" \
            --exclude "/usr*" \
            --exclude "*examples*"

      # - name: macOS coverage
      #   if: runner.os == 'macOS'
      #   working-directory: ./tests/coverage
      #   run: |
      #     cmake -G Xcode -B build
      #     cmake --build ./build --target coverage --config RelWithDebInfo --parallel
      #     build/coverage_artefacts/RelWithDebInfo/coverage
      #     # llvm-cov gcov --capture --directory "build" --exclude "tests/coverage/*"  --exclude "examples/common/*" --exclude "tests/coverage/coverage.h" --exclude "*juce_*" --exclude "*3rd_Party*" --exclude "*3rd_party*" --exclude "modules/3rd_party/*" --exclude "/Applications*" --exclude "/usr*" --exclude "*examples*" --output-file coverage.info
      #     gcovr -j ${{env.nproc}} --delete --print-summary --xml-pretty --xml coverage.xml . --gcov-executable 'llvm-cov gcov'

      # - uses: actions/upload-artifact@v4
      #   if: runner.os == 'Windows'
      #   with:
      #     name: coverage.xml
      #     path: ./tests/coverage/coverage.xml

      # - name: Upload Windows coverage to Codecov
      #   if: runner.os == 'Windows'
      #   uses: codecov/codecov-action@v4
      #   with:
      #     files: ./tests/coverage/coverage.xml
      #     token: ${{ secrets.CODECOV_TOKEN_V4 }}

      # - name: Upload macOS coverage to Codecov
      #   if: runner.os == 'macOS'
      #   uses: codecov/codecov-action@v4
      #   with:
      #     files: ./tests/coverage/coverage.xml
      #     token: ${{ secrets.CODECOV_TOKEN_V4 }}

      - name: Upload Linux coverage to Codecov
        if: runner.os == 'Linux'
        uses: codecov/codecov-action@v4
        with:
          files: ./tests/coverage/build/coverage.info
          token: ${{ secrets.CODECOV_TOKEN_V4 }}
