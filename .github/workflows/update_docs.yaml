name: Update Docs
on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Install dependencies
        run: |
          sudo add-apt-repository ppa:ubuntu-toolchain-r/test
          sudo apt-get update
          sudo apt-get install -y gcc-11 g++-11 freeglut3-dev g++ libasound2-dev libcurl4-openssl-dev libfreetype6-dev libjack-jackd2-dev libx11-dev libxcomposite-dev libxcursor-dev libxinerama-dev libxrandr-dev mesa-common-dev ladspa-sdk webkit2gtk-4.0 libgtk-3-dev xvfb

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          persist-credentials: false

      - name: Build Docs
        run: |
          sudo apt-get install doxygen
          sudo apt-get install graphviz
          sudo apt-get install rsync
          cd doxygen
          make

      - name: Copy benchmark html
        run: |
          cp -f docs/benchmarks.html doxygen/doc

      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@v4.3.3
        with:
          ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}
          BRANCH: gh-pages 
          FOLDER: doxygen/doc