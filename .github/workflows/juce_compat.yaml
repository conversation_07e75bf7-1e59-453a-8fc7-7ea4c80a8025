name: juce_compatability
on:
  schedule:
    - cron: '* 0 * * *'
  push:
  workflow_dispatch:

env:
  BUILD_CONFIG: Release
  BUILD_DIR: build

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        name: [ linux, macOS, windows ]
        include:
          - name: linux
            os: ubuntu-latest
            generator: "-G Ninja -DCMAKE_BUILD_TYPE=Release"
          - name: macOS
            os: macos-latest
            generator: "-G Xcode"
          - name: windows
            os: windows-latest

    runs-on: ${{ matrix.os }}
    steps:
      - name: Install dependencies
        if: ${{ matrix.name == 'linux' }}
        run: |
            sudo add-apt-repository ppa:ubuntu-toolchain-r/test
            sudo apt-get update
            sudo apt-get install -y gcc-11 g++-11 freeglut3-dev g++ libasound2-dev libcurl4-openssl-dev libfreetype6-dev libjack-jackd2-dev libx11-dev libxcomposite-dev libxcursor-dev libxinerama-dev libxrandr-dev mesa-common-dev ladspa-sdk webkit2gtk-4.0 libgtk-3-dev xvfb ninja-build

      - name: Set Xcode version
        if: runner.os == 'macOS'
        run: sudo xcode-select -s /Applications/Xcode_15.3.app

      - uses: actions/checkout@v4
      #   with:
      #     submodules: true

      # - name: Update juce to tip
      #   run: |
      #     cd modules/juce
      #     git config pull.rebase false # merge changes
      #     git pull origin develop

      # Generate build files
      - name: "Build all"
        id: generate
        shell: bash
        run: |
          cmake -B ${{ env.BUILD_DIR }} ${{ matrix.generator }} -DJUCE_CPM_DEVELOP=1
          cmake --build ./${{ env.BUILD_DIR }} --config ${{ env.BUILD_CONFIG }} --parallel --target DemoRunner EngineInPluginDemo_common TestRunner Benchmarks
