# Automatically generated CMakeLists, created by the Projucer
# Don't edit this file! Your changes will be overwritten when you re-save the Projucer project!

cmake_minimum_required(VERSION 3.22)

project(juce_jni_project)

set(BINARY_NAME "juce_jni")

set(OBOE_DIR "../../../../../modules/juce_audio_devices/native/oboe")

add_subdirectory (${OBOE_DIR} ./oboe)

add_library("cpufeatures" STATIC "${ANDROID_NDK}/sources/android/cpufeatures/cpu-features.c")
set_source_files_properties("${ANDROID_NDK}/sources/android/cpufeatures/cpu-features.c" PROPERTIES COMPILE_FLAGS "-Wno-sign-conversion -Wno-gnu-statement-expression")

add_definitions([[-DJUCE_ANDROID=1]] [[-DJUCE_ANDROID_API_VERSION=23]] [[-DJUCE_PUSH_NOTIFICATIONS=1]] [[-DJ<PERSON>E_PUSH_NOTIFICATIONS_ACTIVITY="com/rmsl/juce/JuceActivity"]] [[-DJUCE_CONTENT_SHARING=1]] [[-DJUCE_ANDROID_GL_ES_VERSION_3_0=1]] [[-DJUCE_DEMO_RUNNER=1]] [[-DJUCE_UNIT_TESTS=1]] [[-DJUCER_ANDROIDSTUDIO_7F0E4A25=1]] [[-DJUCE_APP_VERSION=8.0.6]] [[-DJUCE_APP_VERSION_HEX=0x80006]])

include_directories( AFTER
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK"
    "../../../JuceLibraryCode"
    "../../../../../modules"
    "${ANDROID_NDK}/sources/android/cpufeatures"
)

enable_language(ASM)

if(JUCE_BUILD_CONFIGURATION MATCHES "DEBUG")
    add_definitions([[-DJUCE_PROJUCER_VERSION=0x80006]] [[-DJUCE_MODULE_AVAILABLE_juce_analytics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_animation=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_basics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_devices=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_formats=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_processors=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_utils=1]] [[-DJUCE_MODULE_AVAILABLE_juce_box2d=1]] [[-DJUCE_MODULE_AVAILABLE_juce_core=1]] [[-DJUCE_MODULE_AVAILABLE_juce_cryptography=1]] [[-DJUCE_MODULE_AVAILABLE_juce_data_structures=1]] [[-DJUCE_MODULE_AVAILABLE_juce_dsp=1]] [[-DJUCE_MODULE_AVAILABLE_juce_events=1]] [[-DJUCE_MODULE_AVAILABLE_juce_graphics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_gui_basics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_gui_extra=1]] [[-DJUCE_MODULE_AVAILABLE_juce_javascript=1]] [[-DJUCE_MODULE_AVAILABLE_juce_opengl=1]] [[-DJUCE_MODULE_AVAILABLE_juce_osc=1]] [[-DJUCE_MODULE_AVAILABLE_juce_product_unlocking=1]] [[-DJUCE_MODULE_AVAILABLE_juce_video=1]] [[-DJUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1]] [[-DJUCE_USE_MP3AUDIOFORMAT=1]] [[-DJUCE_PLUGINHOST_VST3=1]] [[-DJUCE_PLUGINHOST_LV2=1]] [[-DJUCE_ALLOW_STATIC_NULL_VARIABLES=0]] [[-DJUCE_STRICT_REFCOUNTEDPOINTER=1]] [[-DJUCE_USE_CAMERA=1]] [[-DJUCE_STANDALONE_APPLICATION=1]] [[-DJUCE_DEMO_RUNNER=1]] [[-DJUCE_UNIT_TESTS=1]] [[-DJUCE_PUSH_NOTIFICATIONS=1]] [[-DJUCER_ANDROIDSTUDIO_7F0E4A25=1]] [[-DJUCE_APP_VERSION=8.0.6]] [[-DJUCE_APP_VERSION_HEX=0x80006]] [[-DDEBUG=1]] [[-D_DEBUG=1]])
elseif(JUCE_BUILD_CONFIGURATION MATCHES "RELEASE")
    add_definitions([[-DJUCE_PROJUCER_VERSION=0x80006]] [[-DJUCE_MODULE_AVAILABLE_juce_analytics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_animation=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_basics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_devices=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_formats=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_processors=1]] [[-DJUCE_MODULE_AVAILABLE_juce_audio_utils=1]] [[-DJUCE_MODULE_AVAILABLE_juce_box2d=1]] [[-DJUCE_MODULE_AVAILABLE_juce_core=1]] [[-DJUCE_MODULE_AVAILABLE_juce_cryptography=1]] [[-DJUCE_MODULE_AVAILABLE_juce_data_structures=1]] [[-DJUCE_MODULE_AVAILABLE_juce_dsp=1]] [[-DJUCE_MODULE_AVAILABLE_juce_events=1]] [[-DJUCE_MODULE_AVAILABLE_juce_graphics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_gui_basics=1]] [[-DJUCE_MODULE_AVAILABLE_juce_gui_extra=1]] [[-DJUCE_MODULE_AVAILABLE_juce_javascript=1]] [[-DJUCE_MODULE_AVAILABLE_juce_opengl=1]] [[-DJUCE_MODULE_AVAILABLE_juce_osc=1]] [[-DJUCE_MODULE_AVAILABLE_juce_product_unlocking=1]] [[-DJUCE_MODULE_AVAILABLE_juce_video=1]] [[-DJUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1]] [[-DJUCE_USE_MP3AUDIOFORMAT=1]] [[-DJUCE_PLUGINHOST_VST3=1]] [[-DJUCE_PLUGINHOST_LV2=1]] [[-DJUCE_ALLOW_STATIC_NULL_VARIABLES=0]] [[-DJUCE_STRICT_REFCOUNTEDPOINTER=1]] [[-DJUCE_USE_CAMERA=1]] [[-DJUCE_STANDALONE_APPLICATION=1]] [[-DJUCE_DEMO_RUNNER=1]] [[-DJUCE_UNIT_TESTS=1]] [[-DJUCE_PUSH_NOTIFICATIONS=1]] [[-DJUCER_ANDROIDSTUDIO_7F0E4A25=1]] [[-DJUCE_APP_VERSION=8.0.6]] [[-DJUCE_APP_VERSION_HEX=0x80006]] [[-DNDEBUG=1]])
else()
    message( FATAL_ERROR "No matching build-configuration found." )
endif()

add_library( ${BINARY_NAME}

    SHARED

    "../../../Source/Demos/DemoPIPs1.cpp"
    "../../../Source/Demos/DemoPIPs2.cpp"
    "../../../Source/Demos/IntroScreen.h"
    "../../../Source/Demos/JUCEDemos.cpp"
    "../../../Source/Demos/JUCEDemos.h"
    "../../../Source/UI/DemoContentComponent.cpp"
    "../../../Source/UI/DemoContentComponent.h"
    "../../../Source/UI/MainComponent.cpp"
    "../../../Source/UI/MainComponent.h"
    "../../../Source/UI/SettingsContent.h"
    "../../../Source/Main.cpp"
    "../../../Source/JUCEAppIcon.png"
    "../../../../../modules/juce_analytics/analytics/juce_Analytics.cpp"
    "../../../../../modules/juce_analytics/analytics/juce_Analytics.h"
    "../../../../../modules/juce_analytics/analytics/juce_ButtonTracker.cpp"
    "../../../../../modules/juce_analytics/analytics/juce_ButtonTracker.h"
    "../../../../../modules/juce_analytics/destinations/juce_AnalyticsDestination.h"
    "../../../../../modules/juce_analytics/destinations/juce_ThreadedAnalyticsDestination.cpp"
    "../../../../../modules/juce_analytics/destinations/juce_ThreadedAnalyticsDestination.h"
    "../../../../../modules/juce_analytics/juce_analytics.cpp"
    "../../../../../modules/juce_analytics/juce_analytics.h"
    "../../../../../modules/juce_animation/animation/juce_Animator.cpp"
    "../../../../../modules/juce_animation/animation/juce_Animator.h"
    "../../../../../modules/juce_animation/animation/juce_AnimatorSetBuilder.cpp"
    "../../../../../modules/juce_animation/animation/juce_AnimatorSetBuilder.h"
    "../../../../../modules/juce_animation/animation/juce_AnimatorUpdater.cpp"
    "../../../../../modules/juce_animation/animation/juce_AnimatorUpdater.h"
    "../../../../../modules/juce_animation/animation/juce_Easings.cpp"
    "../../../../../modules/juce_animation/animation/juce_Easings.h"
    "../../../../../modules/juce_animation/animation/juce_StaticAnimationLimits.h"
    "../../../../../modules/juce_animation/animation/juce_ValueAnimatorBuilder.cpp"
    "../../../../../modules/juce_animation/animation/juce_ValueAnimatorBuilder.h"
    "../../../../../modules/juce_animation/animation/juce_VBlankAnimatorUpdater.h"
    "../../../../../modules/juce_animation/detail/chromium/cubic_bezier.cc"
    "../../../../../modules/juce_animation/detail/chromium/cubic_bezier.h"
    "../../../../../modules/juce_animation/detail/juce_ArrayAndTupleOps.h"
    "../../../../../modules/juce_animation/juce_animation.cpp"
    "../../../../../modules/juce_animation/juce_animation.h"
    "../../../../../modules/juce_audio_basics/audio_play_head/juce_AudioPlayHead.cpp"
    "../../../../../modules/juce_audio_basics/audio_play_head/juce_AudioPlayHead.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioChannelSet.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioChannelSet.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioDataConverters.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioDataConverters.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioProcessLoadMeasurer.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioProcessLoadMeasurer.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioSampleBuffer.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_FloatVectorOperations.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_FloatVectorOperations.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMP.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMP_test.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPacket.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPackets.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPBytesOnGroup.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPConversion.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPConverters.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPDeviceInfo.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPDispatcher.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPFactory.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPIterator.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPIterator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToBytestreamTranslator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToMidi2DefaultTranslator.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToMidi2DefaultTranslator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPProtocols.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPReceiver.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPSysEx7.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPSysEx7.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPUtils.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPUtils.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPView.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPView.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiBuffer.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiBuffer.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiDataConcatenator.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiFile.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiFile.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiKeyboardState.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiKeyboardState.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessage.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessage.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessageSequence.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessageSequence.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiRPN.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiRPN.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEInstrument.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEInstrument.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEMessages.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEMessages.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPENote.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPENote.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiser.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiser.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserBase.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserBase.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserVoice.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserVoice.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEUtils.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEUtils.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEValue.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEValue.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEZoneLayout.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEZoneLayout.h"
    "../../../../../modules/juce_audio_basics/native/juce_AudioWorkgroup_mac.h"
    "../../../../../modules/juce_audio_basics/native/juce_CoreAudioLayouts_mac.h"
    "../../../../../modules/juce_audio_basics/native/juce_CoreAudioTimeConversions_mac.h"
    "../../../../../modules/juce_audio_basics/sources/juce_AudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_BufferingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_BufferingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ChannelRemappingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ChannelRemappingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_IIRFilterAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_IIRFilterAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_MemoryAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_MemoryAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_MixerAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_MixerAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_PositionableAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_PositionableAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ResamplingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ResamplingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ReverbAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ReverbAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ToneGeneratorAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ToneGeneratorAudioSource.h"
    "../../../../../modules/juce_audio_basics/synthesisers/juce_Synthesiser.cpp"
    "../../../../../modules/juce_audio_basics/synthesisers/juce_Synthesiser.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_ADSR.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_ADSR_test.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_AudioWorkgroup.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_AudioWorkgroup.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_Decibels.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_GenericInterpolator.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_IIRFilter.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_IIRFilter.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_Interpolators.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_Interpolators.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_LagrangeInterpolator.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_Reverb.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_SmoothedValue.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_SmoothedValue.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_WindowedSincInterpolator.cpp"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.cpp"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.mm"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioDeviceManager.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioDeviceManager.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODevice.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODevice.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODeviceType.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODeviceType.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_SampleRateHelpers.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_SystemAudioVolume.h"
    "../../../../../modules/juce_audio_devices/midi_io/ump/juce_UMPBytestreamInputHandler.h"
    "../../../../../modules/juce_audio_devices/midi_io/ump/juce_UMPU32InputHandler.h"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDeviceListConnectionBroadcaster.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDevices.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDevices.h"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiMessageCollector.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiMessageCollector.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamBase.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamBuilder.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamCallback.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Definitions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FifoBuffer.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FifoControllerBase.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FullDuplexStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/LatencyTuner.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Oboe.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/OboeExtensions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/ResultWithValue.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/StabilizedCallback.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Utilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Version.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioExtensions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioLoader.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioLoader.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AudioStreamAAudio.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AudioStreamAAudio.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AdpfWrapper.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AdpfWrapper.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioClock.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioSourceCaller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioSourceCaller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioStream.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioStreamBuilder.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/DataConversionFlowGraph.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/DataConversionFlowGraph.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FilterAudioStream.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FilterAudioStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockAdapter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockAdapter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockReader.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockReader.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockWriter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockWriter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/LatencyTuner.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/MonotonicCounter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/OboeDebug.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/OboeExtensions.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/QuirksManager.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/QuirksManager.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/README.md"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceFloatCaller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceFloatCaller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI16Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI16Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI24Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI24Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI32Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI32Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/StabilizedCallback.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Trace.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Trace.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Utilities.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Version.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoBuffer.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoController.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoController.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerBase.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerIndirect.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerIndirect.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/HyperbolicCosineWindow.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/IntegerRatio.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/IntegerRatio.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/KaiserWindow.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/LinearResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/LinearResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/MultiChannelResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/MultiChannelResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerMono.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerMono.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerStereo.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerStereo.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/README.md"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/ResamplerDefinitions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResamplerStereo.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResamplerStereo.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ChannelCountConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ChannelCountConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ClipToRange.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ClipToRange.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowGraphNode.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowGraphNode.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowgraphUtilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/Limiter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/Limiter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ManyToMultiConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ManyToMultiConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoBlend.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoBlend.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoToMultiConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoToMultiConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToManyConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToManyConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToMonoConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToMonoConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/RampLinear.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/RampLinear.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SampleRateConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SampleRateConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkFloat.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkFloat.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI8_24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI8_24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI16.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI16.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI32.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI32.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceFloat.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceFloat.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI8_24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI8_24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI16.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI16.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI32.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI32.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioInputStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioInputStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioOutputStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioOutputStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamBuffered.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamBuffered.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/EngineOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/EngineOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OpenSLESUtilities.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OpenSLESUtilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OutputMixerOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OutputMixerOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/CMakeLists.txt"
    "../../../../../modules/juce_audio_devices/native/oboe/README.md"
    "../../../../../modules/juce_audio_devices/native/juce_ALSA_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_ASIO_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_ios.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_ios.h"
    "../../../../../modules/juce_audio_devices/native/juce_Bela_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_CoreAudio_mac.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_CoreMidi_mac.mm"
    "../../../../../modules/juce_audio_devices/native/juce_DirectSound_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_HighPerformanceAudioHelpers_android.h"
    "../../../../../modules/juce_audio_devices/native/juce_JackAudio.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Oboe_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_OpenSL_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_WASAPI_windows.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioSourcePlayer.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioSourcePlayer.h"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioTransportSource.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioTransportSource.h"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.cpp"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.mm"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/bitreader_read_rice_signed_block.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/lpc_compute_autocorrelation_intrin.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/lpc_compute_autocorrelation_intrin_neon.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitmath.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitreader.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitwriter.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/cpu.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/crc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/fixed.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/float.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/format.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/lpc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/md5.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/memory.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/stream_encoder_framing.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/window.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/protected/stream_decoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/protected/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitmath.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitreader.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitwriter.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/cpu.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/crc.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/fixed.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/float.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/format.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/lpc_flac.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/lpc_intrin_neon.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/md5.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/memory.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_decoder.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_encoder.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_encoder_framing.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/window_flac.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/all.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/alloc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/assert.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/callback.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/compat.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/endswap.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/export.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/Flac Licence.txt"
    "../../../../../modules/juce_audio_formats/codecs/flac/format.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/JUCE_CHANGES.txt"
    "../../../../../modules/juce_audio_formats/codecs/flac/metadata.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/ordinals.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/private.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/stream_decoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/coupled/res_books_51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/coupled/res_books_stereo.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/floor/floor_books.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/uncoupled/res_books_uncoupled.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/floor_all.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_11.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44p51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44u.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_11.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_22.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_32.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44p51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44u.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_X.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/analysis.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/backends.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/bitrate.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/bitrate.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/block.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codebook.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codebook.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codec_internal.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/envelope.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/envelope.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/floor0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/floor1.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/highlevel.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/info.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup_data.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lpc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lpc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lsp.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lsp.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mapping0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/masking.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mdct.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mdct.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/misc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/misc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/os.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/psy.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/psy.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/registry.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/registry.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/res0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/scales.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/sharedbook.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/smallft.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/smallft.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/synthesis.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/vorbisenc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/vorbisfile.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/window.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/window.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/README.md"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/bitwise.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/codec.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/config_types.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/crctable.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/framing.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/Ogg Vorbis Licence.txt"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/ogg.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/os_types.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/vorbisenc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/vorbisfile.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_AiffAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_AiffAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_CoreAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_CoreAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_FlacAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_FlacAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_LAMEEncoderAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_LAMEEncoderAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_MP3AudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_MP3AudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_OggVorbisAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_OggVorbisAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_WavAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_WavAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_WindowsMediaAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_WindowsMediaAudioFormat.h"
    "../../../../../modules/juce_audio_formats/format/juce_ARAAudioReaders.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_ARAAudioReaders.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormat.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatManager.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatManager.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReaderSource.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReaderSource.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatWriter.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatWriter.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioSubsectionReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioSubsectionReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_BufferingAudioFormatReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_BufferingAudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_MemoryMappedAudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/sampler/juce_Sampler.cpp"
    "../../../../../modules/juce_audio_formats/sampler/juce_Sampler.h"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.cpp"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.mm"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.h"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormatManager.cpp"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormatManager.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/lilv/lilv.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/lilv/lilvmm.hpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/common.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/tree.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/tree.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/collections.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/filesystem.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/filesystem.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/instance.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/lib.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/lilv_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/node.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/plugin.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/pluginclass.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/port.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/query.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/scalepoint.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/state.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/ui.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/util.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/world.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom-test-utils.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom-test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/forge-overflow-test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/forge.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/util.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/buf-size/buf-size.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/attributes.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/lv2.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/lv2_util.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/data-access/data-access.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/dynmanifest/dynmanifest.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/event/event-helpers.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/event/event.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/instance-access/instance-access.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/log/log.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/log/logger.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/midi/midi.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/morph/morph.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/options/options.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/parameters/parameters.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/patch/patch.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/port-groups/port-groups.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/port-props/port-props.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/presets/presets.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/resize-port/resize-port.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/state/state.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/time/time.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/ui/ui.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/units/units.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/uri-map/uri-map.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/urid/urid.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/worker/worker.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/serd/serd.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/attributes.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/base64.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/base64.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_sink.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_source.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_source.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/env.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/n3.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/node.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/node.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/reader.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/reader.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serd_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serd_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serdi.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/stack.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/string.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/string_utils.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/system.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/system.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/uri.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/uri_utils.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/writer.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/sord/sord.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/sord/sordmm.hpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/btree.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/btree.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/common.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/digest.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/digest.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/hash.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/hash.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_validate.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sordi.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sordmm_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/syntax.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom/sratom/sratom.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom/src/sratom.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/juce_lv2_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/README.md"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord_config.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslcontextinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipsleditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslgainreduction.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslhostcommands.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslviewembedding.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslviewscaling.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/pslauextensions.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/pslvst2extensions.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/baseiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/classfactoryhelpers.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fbuffer.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fbuffer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fcommandline.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fdebug.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fdebug.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fobject.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fobject.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstreamer.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstreamer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstring.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstring.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/updatehandler.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/updatehandler.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/thread/include/flock.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/thread/source/flock.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/conststringtable.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/conststringtable.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/coreiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/falignpop.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/falignpush.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fplatform.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fstrdefs.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ftypes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknown.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknown.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknownimpl.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/futils.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fvariant.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ibstream.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/icloneable.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ipersistent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ipluginbase.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/iplugincompatibility.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/istringresult.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/iupdatehandler.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/smartpointer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/typesizecheck.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ustring.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ustring.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/gui/iplugview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/gui/iplugviewcontentscalesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstattributes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstaudioprocessor.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstautomationstate.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstchannelcontextinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstcomponent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstcontextmenu.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstdataexchange.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsteditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstevents.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsthostapplication.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstinterappaudio.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmessage.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmidicontrollers.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmidilearn.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstnoteexpression.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstparameterchanges.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstparameterfunctionname.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstphysicalui.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstpluginterfacesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstplugview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstprefetchablesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstprocesscontext.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstremapparamid.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstrepresentation.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsttestplugprovider.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstunits.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vstpshpack4.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vstspeaker.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vsttypes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/samples/vst-utilities/moduleinfotool/source/main.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/commonstringconvert.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/commonstringconvert.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/memorystream.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/memorystream.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/pluginview.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/pluginview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/readfile.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/readfile.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/hostclasses.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/hostclasses.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_linux.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_mac.mm"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_win32.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/pluginterfacesupport.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/pluginterfacesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/json.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/jsoncxx.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfocreator.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfocreator.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfoparser.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfoparser.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/ReadMe.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/optional.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/stringconvert.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/stringconvert.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/uid.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/vst2persistence.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/vst2persistence.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstbus.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstbus.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponent.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponentbase.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponentbase.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vsteditcontroller.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vsteditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstinitiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstparameters.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstparameters.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstpresetfile.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstpresetfile.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/JUCE_README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/README.md"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARACommon.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARACommon.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARAHosting.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARAHosting.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AU_Shared.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AudioUnitPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AudioUnitPluginFormat.mm"
    "../../../../../modules/juce_audio_processors/format_types/juce_LADSPAPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LADSPAPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LegacyAudioParameter.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2Common.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2Resources.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2SupportLibs.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3Common.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3Headers.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTCommon.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTMidiEventList.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTPluginFormat.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioPluginInstance.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioPluginInstance.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditorHostContext.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorGraph.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorGraph.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorListener.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameter.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameterGroup.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameterGroup.h"
    "../../../../../modules/juce_audio_processors/processors/juce_GenericAudioProcessorEditor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_GenericAudioProcessorEditor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_HostedAudioProcessorParameter.h"
    "../../../../../modules/juce_audio_processors/processors/juce_PluginDescription.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_PluginDescription.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_KnownPluginList.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_KnownPluginList.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginDirectoryScanner.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginDirectoryScanner.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginListComponent.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginListComponent.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARA_utils.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARA_utils.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADebug.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentController.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentController.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentControllerCommon.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAModelObjects.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAModelObjects.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAPlugInInstanceRoles.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAPlugInInstanceRoles.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_AudioProcessor_ARAExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_AudioProcessor_ARAExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AAXClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AAXClientExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterBool.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterBool.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterChoice.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterChoice.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterFloat.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterFloat.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterInt.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterInt.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorParameterWithID.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorParameterWithID.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorValueTreeState.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorValueTreeState.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_ExtensionsVisitor.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_FlagCache.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_ParameterAttachments.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_ParameterAttachments.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_PluginHostType.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_PluginHostType.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_RangedAudioParameter.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_RangedAudioParameter.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST2ClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST2ClientExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST3ClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST3ClientExtensions.h"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.mm"
    "../../../../../modules/juce_audio_processors/juce_audio_processors_ara.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors_lv2_libs.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.h"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDBurner.h"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDReader.cpp"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDReader.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioAppComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioAppComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioDeviceSelectorComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioDeviceSelectorComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnail.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnail.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailBase.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailCache.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailCache.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioVisualiserComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioVisualiserComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_BluetoothMidiDevicePairingDialogue.h"
    "../../../../../modules/juce_audio_utils/gui/juce_KeyboardComponentBase.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_KeyboardComponentBase.h"
    "../../../../../modules/juce_audio_utils/gui/juce_MidiKeyboardComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_MidiKeyboardComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_MPEKeyboardComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_MPEKeyboardComponent.h"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDBurner_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDBurner_windows.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_linux.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_windows.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_android.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_ios.mm"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_linux.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_windows.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_AudioProcessorPlayer.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_AudioProcessorPlayer.h"
    "../../../../../modules/juce_audio_utils/players/juce_SoundPlayer.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_SoundPlayer.h"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.cpp"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.mm"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2ChainShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2ChainShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2CircleShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2CircleShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2EdgeShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2EdgeShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2PolygonShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2PolygonShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2Shape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2BroadPhase.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2BroadPhase.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollideCircle.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollideEdge.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollidePolygon.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Collision.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Collision.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Distance.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Distance.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2DynamicTree.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2DynamicTree.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2TimeOfImpact.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2TimeOfImpact.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2BlockAllocator.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2BlockAllocator.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Draw.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Draw.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2GrowableStack.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Math.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Math.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Settings.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Settings.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2StackAllocator.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2StackAllocator.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Timer.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Timer.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndPolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndPolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2CircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2CircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2Contact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2Contact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ContactSolver.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ContactSolver.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndPolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndPolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2DistanceJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2DistanceJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2FrictionJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2FrictionJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2GearJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2GearJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2Joint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2Joint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2MouseJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2MouseJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PrismaticJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PrismaticJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PulleyJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PulleyJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RevoluteJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RevoluteJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RopeJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RopeJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WeldJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WeldJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WheelJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WheelJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Body.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Body.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2ContactManager.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2ContactManager.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Fixture.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Fixture.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Island.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Island.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2TimeStep.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2World.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2World.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2WorldCallbacks.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2WorldCallbacks.h"
    "../../../../../modules/juce_box2d/box2d/Rope/b2Rope.cpp"
    "../../../../../modules/juce_box2d/box2d/Rope/b2Rope.h"
    "../../../../../modules/juce_box2d/box2d/Box2D.h"
    "../../../../../modules/juce_box2d/box2d/README.txt"
    "../../../../../modules/juce_box2d/utils/juce_Box2DRenderer.cpp"
    "../../../../../modules/juce_box2d/utils/juce_Box2DRenderer.h"
    "../../../../../modules/juce_box2d/juce_box2d.cpp"
    "../../../../../modules/juce_box2d/juce_box2d.h"
    "../../../../../modules/juce_core/containers/juce_AbstractFifo.cpp"
    "../../../../../modules/juce_core/containers/juce_AbstractFifo.h"
    "../../../../../modules/juce_core/containers/juce_Array.h"
    "../../../../../modules/juce_core/containers/juce_ArrayAllocationBase.h"
    "../../../../../modules/juce_core/containers/juce_ArrayBase.cpp"
    "../../../../../modules/juce_core/containers/juce_ArrayBase.h"
    "../../../../../modules/juce_core/containers/juce_DynamicObject.cpp"
    "../../../../../modules/juce_core/containers/juce_DynamicObject.h"
    "../../../../../modules/juce_core/containers/juce_ElementComparator.h"
    "../../../../../modules/juce_core/containers/juce_Enumerate.h"
    "../../../../../modules/juce_core/containers/juce_Enumerate_test.cpp"
    "../../../../../modules/juce_core/containers/juce_FixedSizeFunction.h"
    "../../../../../modules/juce_core/containers/juce_FixedSizeFunction_test.cpp"
    "../../../../../modules/juce_core/containers/juce_HashMap.h"
    "../../../../../modules/juce_core/containers/juce_HashMap_test.cpp"
    "../../../../../modules/juce_core/containers/juce_LinkedListPointer.h"
    "../../../../../modules/juce_core/containers/juce_ListenerList.h"
    "../../../../../modules/juce_core/containers/juce_ListenerList_test.cpp"
    "../../../../../modules/juce_core/containers/juce_NamedValueSet.cpp"
    "../../../../../modules/juce_core/containers/juce_NamedValueSet.h"
    "../../../../../modules/juce_core/containers/juce_Optional.h"
    "../../../../../modules/juce_core/containers/juce_Optional_test.cpp"
    "../../../../../modules/juce_core/containers/juce_OwnedArray.cpp"
    "../../../../../modules/juce_core/containers/juce_OwnedArray.h"
    "../../../../../modules/juce_core/containers/juce_PropertySet.cpp"
    "../../../../../modules/juce_core/containers/juce_PropertySet.h"
    "../../../../../modules/juce_core/containers/juce_ReferenceCountedArray.cpp"
    "../../../../../modules/juce_core/containers/juce_ReferenceCountedArray.h"
    "../../../../../modules/juce_core/containers/juce_ScopedValueSetter.h"
    "../../../../../modules/juce_core/containers/juce_SingleThreadedAbstractFifo.h"
    "../../../../../modules/juce_core/containers/juce_SortedSet.h"
    "../../../../../modules/juce_core/containers/juce_Span.h"
    "../../../../../modules/juce_core/containers/juce_SparseSet.cpp"
    "../../../../../modules/juce_core/containers/juce_SparseSet.h"
    "../../../../../modules/juce_core/containers/juce_Variant.cpp"
    "../../../../../modules/juce_core/containers/juce_Variant.h"
    "../../../../../modules/juce_core/detail/juce_CallbackListenerList.h"
    "../../../../../modules/juce_core/files/juce_AndroidDocument.h"
    "../../../../../modules/juce_core/files/juce_common_MimeTypes.cpp"
    "../../../../../modules/juce_core/files/juce_common_MimeTypes.h"
    "../../../../../modules/juce_core/files/juce_DirectoryIterator.cpp"
    "../../../../../modules/juce_core/files/juce_DirectoryIterator.h"
    "../../../../../modules/juce_core/files/juce_File.cpp"
    "../../../../../modules/juce_core/files/juce_File.h"
    "../../../../../modules/juce_core/files/juce_FileFilter.cpp"
    "../../../../../modules/juce_core/files/juce_FileFilter.h"
    "../../../../../modules/juce_core/files/juce_FileInputStream.cpp"
    "../../../../../modules/juce_core/files/juce_FileInputStream.h"
    "../../../../../modules/juce_core/files/juce_FileOutputStream.cpp"
    "../../../../../modules/juce_core/files/juce_FileOutputStream.h"
    "../../../../../modules/juce_core/files/juce_FileSearchPath.cpp"
    "../../../../../modules/juce_core/files/juce_FileSearchPath.h"
    "../../../../../modules/juce_core/files/juce_MemoryMappedFile.h"
    "../../../../../modules/juce_core/files/juce_RangedDirectoryIterator.cpp"
    "../../../../../modules/juce_core/files/juce_RangedDirectoryIterator.h"
    "../../../../../modules/juce_core/files/juce_TemporaryFile.cpp"
    "../../../../../modules/juce_core/files/juce_TemporaryFile.h"
    "../../../../../modules/juce_core/files/juce_WildcardFileFilter.cpp"
    "../../../../../modules/juce_core/files/juce_WildcardFileFilter.h"
    "../../../../../modules/juce_core/json/juce_JSON.cpp"
    "../../../../../modules/juce_core/json/juce_JSON.h"
    "../../../../../modules/juce_core/json/juce_JSONSerialisation.h"
    "../../../../../modules/juce_core/json/juce_JSONSerialisation_test.cpp"
    "../../../../../modules/juce_core/json/juce_JSONUtils.cpp"
    "../../../../../modules/juce_core/json/juce_JSONUtils.h"
    "../../../../../modules/juce_core/logging/juce_FileLogger.cpp"
    "../../../../../modules/juce_core/logging/juce_FileLogger.h"
    "../../../../../modules/juce_core/logging/juce_Logger.cpp"
    "../../../../../modules/juce_core/logging/juce_Logger.h"
    "../../../../../modules/juce_core/maths/juce_BigInteger.cpp"
    "../../../../../modules/juce_core/maths/juce_BigInteger.h"
    "../../../../../modules/juce_core/maths/juce_Expression.cpp"
    "../../../../../modules/juce_core/maths/juce_Expression.h"
    "../../../../../modules/juce_core/maths/juce_MathsFunctions.h"
    "../../../../../modules/juce_core/maths/juce_MathsFunctions_test.cpp"
    "../../../../../modules/juce_core/maths/juce_NormalisableRange.h"
    "../../../../../modules/juce_core/maths/juce_Random.cpp"
    "../../../../../modules/juce_core/maths/juce_Random.h"
    "../../../../../modules/juce_core/maths/juce_Range.h"
    "../../../../../modules/juce_core/maths/juce_StatisticsAccumulator.h"
    "../../../../../modules/juce_core/memory/juce_AllocationHooks.cpp"
    "../../../../../modules/juce_core/memory/juce_AllocationHooks.h"
    "../../../../../modules/juce_core/memory/juce_Atomic.h"
    "../../../../../modules/juce_core/memory/juce_ByteOrder.h"
    "../../../../../modules/juce_core/memory/juce_ContainerDeletePolicy.h"
    "../../../../../modules/juce_core/memory/juce_CopyableHeapBlock.h"
    "../../../../../modules/juce_core/memory/juce_HeapBlock.h"
    "../../../../../modules/juce_core/memory/juce_HeavyweightLeakedObjectDetector.h"
    "../../../../../modules/juce_core/memory/juce_LeakedObjectDetector.h"
    "../../../../../modules/juce_core/memory/juce_Memory.h"
    "../../../../../modules/juce_core/memory/juce_MemoryBlock.cpp"
    "../../../../../modules/juce_core/memory/juce_MemoryBlock.h"
    "../../../../../modules/juce_core/memory/juce_OptionalScopedPointer.h"
    "../../../../../modules/juce_core/memory/juce_ReferenceCountedObject.h"
    "../../../../../modules/juce_core/memory/juce_Reservoir.h"
    "../../../../../modules/juce_core/memory/juce_ScopedPointer.h"
    "../../../../../modules/juce_core/memory/juce_SharedResourcePointer.h"
    "../../../../../modules/juce_core/memory/juce_SharedResourcePointer_test.cpp"
    "../../../../../modules/juce_core/memory/juce_Singleton.h"
    "../../../../../modules/juce_core/memory/juce_WeakReference.h"
    "../../../../../modules/juce_core/misc/juce_ConsoleApplication.cpp"
    "../../../../../modules/juce_core/misc/juce_ConsoleApplication.h"
    "../../../../../modules/juce_core/misc/juce_EnumHelpers.h"
    "../../../../../modules/juce_core/misc/juce_EnumHelpers_test.cpp"
    "../../../../../modules/juce_core/misc/juce_Functional.h"
    "../../../../../modules/juce_core/misc/juce_OptionsHelpers.h"
    "../../../../../modules/juce_core/misc/juce_Result.cpp"
    "../../../../../modules/juce_core/misc/juce_Result.h"
    "../../../../../modules/juce_core/misc/juce_RuntimePermissions.cpp"
    "../../../../../modules/juce_core/misc/juce_RuntimePermissions.h"
    "../../../../../modules/juce_core/misc/juce_ScopeGuard.cpp"
    "../../../../../modules/juce_core/misc/juce_ScopeGuard.h"
    "../../../../../modules/juce_core/misc/juce_Uuid.cpp"
    "../../../../../modules/juce_core/misc/juce_Uuid.h"
    "../../../../../modules/juce_core/misc/juce_WindowsRegistry.h"
    "../../../../../modules/juce_core/native/java/README.txt"
    "../../../../../modules/juce_core/native/juce_AndroidDocument_android.cpp"
    "../../../../../modules/juce_core/native/juce_BasicNativeHeaders.h"
    "../../../../../modules/juce_core/native/juce_CFHelpers_mac.h"
    "../../../../../modules/juce_core/native/juce_CommonFile_linux.cpp"
    "../../../../../modules/juce_core/native/juce_ComSmartPtr_windows.h"
    "../../../../../modules/juce_core/native/juce_Files_android.cpp"
    "../../../../../modules/juce_core/native/juce_Files_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Files_mac.mm"
    "../../../../../modules/juce_core/native/juce_Files_windows.cpp"
    "../../../../../modules/juce_core/native/juce_IPAddress_posix.h"
    "../../../../../modules/juce_core/native/juce_JNIHelpers_android.cpp"
    "../../../../../modules/juce_core/native/juce_JNIHelpers_android.h"
    "../../../../../modules/juce_core/native/juce_Misc_android.cpp"
    "../../../../../modules/juce_core/native/juce_NamedPipe_posix.cpp"
    "../../../../../modules/juce_core/native/juce_Network_android.cpp"
    "../../../../../modules/juce_core/native/juce_Network_curl.cpp"
    "../../../../../modules/juce_core/native/juce_Network_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Network_mac.mm"
    "../../../../../modules/juce_core/native/juce_Network_windows.cpp"
    "../../../../../modules/juce_core/native/juce_ObjCHelpers_mac.h"
    "../../../../../modules/juce_core/native/juce_ObjCHelpers_mac_test.mm"
    "../../../../../modules/juce_core/native/juce_PlatformTimer_generic.cpp"
    "../../../../../modules/juce_core/native/juce_PlatformTimer_windows.cpp"
    "../../../../../modules/juce_core/native/juce_PlatformTimerListener.h"
    "../../../../../modules/juce_core/native/juce_Process_mac.mm"
    "../../../../../modules/juce_core/native/juce_Registry_windows.cpp"
    "../../../../../modules/juce_core/native/juce_RuntimePermissions_android.cpp"
    "../../../../../modules/juce_core/native/juce_SharedCode_intel.h"
    "../../../../../modules/juce_core/native/juce_SharedCode_posix.h"
    "../../../../../modules/juce_core/native/juce_Strings_mac.mm"
    "../../../../../modules/juce_core/native/juce_SystemStats_android.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_linux.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_mac.mm"
    "../../../../../modules/juce_core/native/juce_SystemStats_wasm.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_windows.cpp"
    "../../../../../modules/juce_core/native/juce_ThreadPriorities_native.h"
    "../../../../../modules/juce_core/native/juce_Threads_android.cpp"
    "../../../../../modules/juce_core/native/juce_Threads_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Threads_mac.mm"
    "../../../../../modules/juce_core/native/juce_Threads_windows.cpp"
    "../../../../../modules/juce_core/network/juce_IPAddress.cpp"
    "../../../../../modules/juce_core/network/juce_IPAddress.h"
    "../../../../../modules/juce_core/network/juce_MACAddress.cpp"
    "../../../../../modules/juce_core/network/juce_MACAddress.h"
    "../../../../../modules/juce_core/network/juce_NamedPipe.cpp"
    "../../../../../modules/juce_core/network/juce_NamedPipe.h"
    "../../../../../modules/juce_core/network/juce_Socket.cpp"
    "../../../../../modules/juce_core/network/juce_Socket.h"
    "../../../../../modules/juce_core/network/juce_URL.cpp"
    "../../../../../modules/juce_core/network/juce_URL.h"
    "../../../../../modules/juce_core/network/juce_WebInputStream.cpp"
    "../../../../../modules/juce_core/network/juce_WebInputStream.h"
    "../../../../../modules/juce_core/serialisation/juce_Serialisation.h"
    "../../../../../modules/juce_core/streams/juce_AndroidDocumentInputSource.h"
    "../../../../../modules/juce_core/streams/juce_BufferedInputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_BufferedInputStream.h"
    "../../../../../modules/juce_core/streams/juce_FileInputSource.cpp"
    "../../../../../modules/juce_core/streams/juce_FileInputSource.h"
    "../../../../../modules/juce_core/streams/juce_InputSource.h"
    "../../../../../modules/juce_core/streams/juce_InputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_InputStream.h"
    "../../../../../modules/juce_core/streams/juce_MemoryInputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_MemoryInputStream.h"
    "../../../../../modules/juce_core/streams/juce_MemoryOutputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_MemoryOutputStream.h"
    "../../../../../modules/juce_core/streams/juce_OutputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_OutputStream.h"
    "../../../../../modules/juce_core/streams/juce_SubregionStream.cpp"
    "../../../../../modules/juce_core/streams/juce_SubregionStream.h"
    "../../../../../modules/juce_core/streams/juce_URLInputSource.cpp"
    "../../../../../modules/juce_core/streams/juce_URLInputSource.h"
    "../../../../../modules/juce_core/system/juce_CompilerSupport.h"
    "../../../../../modules/juce_core/system/juce_CompilerWarnings.h"
    "../../../../../modules/juce_core/system/juce_PlatformDefs.h"
    "../../../../../modules/juce_core/system/juce_StandardHeader.h"
    "../../../../../modules/juce_core/system/juce_SystemStats.cpp"
    "../../../../../modules/juce_core/system/juce_SystemStats.h"
    "../../../../../modules/juce_core/system/juce_TargetPlatform.h"
    "../../../../../modules/juce_core/text/juce_Base64.cpp"
    "../../../../../modules/juce_core/text/juce_Base64.h"
    "../../../../../modules/juce_core/text/juce_CharacterFunctions.cpp"
    "../../../../../modules/juce_core/text/juce_CharacterFunctions.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_ASCII.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF8.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF8_test.cpp"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF16.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF16_test.cpp"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF32.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF32_test.cpp"
    "../../../../../modules/juce_core/text/juce_Identifier.cpp"
    "../../../../../modules/juce_core/text/juce_Identifier.h"
    "../../../../../modules/juce_core/text/juce_LocalisedStrings.cpp"
    "../../../../../modules/juce_core/text/juce_LocalisedStrings.h"
    "../../../../../modules/juce_core/text/juce_NewLine.h"
    "../../../../../modules/juce_core/text/juce_String.cpp"
    "../../../../../modules/juce_core/text/juce_String.h"
    "../../../../../modules/juce_core/text/juce_StringArray.cpp"
    "../../../../../modules/juce_core/text/juce_StringArray.h"
    "../../../../../modules/juce_core/text/juce_StringPairArray.cpp"
    "../../../../../modules/juce_core/text/juce_StringPairArray.h"
    "../../../../../modules/juce_core/text/juce_StringPool.cpp"
    "../../../../../modules/juce_core/text/juce_StringPool.h"
    "../../../../../modules/juce_core/text/juce_StringRef.h"
    "../../../../../modules/juce_core/text/juce_TextDiff.cpp"
    "../../../../../modules/juce_core/text/juce_TextDiff.h"
    "../../../../../modules/juce_core/threads/juce_ChildProcess.cpp"
    "../../../../../modules/juce_core/threads/juce_ChildProcess.h"
    "../../../../../modules/juce_core/threads/juce_CriticalSection.h"
    "../../../../../modules/juce_core/threads/juce_DynamicLibrary.h"
    "../../../../../modules/juce_core/threads/juce_HighResolutionTimer.cpp"
    "../../../../../modules/juce_core/threads/juce_HighResolutionTimer.h"
    "../../../../../modules/juce_core/threads/juce_InterProcessLock.h"
    "../../../../../modules/juce_core/threads/juce_Process.h"
    "../../../../../modules/juce_core/threads/juce_ReadWriteLock.cpp"
    "../../../../../modules/juce_core/threads/juce_ReadWriteLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedReadLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedWriteLock.h"
    "../../../../../modules/juce_core/threads/juce_SpinLock.h"
    "../../../../../modules/juce_core/threads/juce_Thread.cpp"
    "../../../../../modules/juce_core/threads/juce_Thread.h"
    "../../../../../modules/juce_core/threads/juce_ThreadLocalValue.h"
    "../../../../../modules/juce_core/threads/juce_ThreadPool.cpp"
    "../../../../../modules/juce_core/threads/juce_ThreadPool.h"
    "../../../../../modules/juce_core/threads/juce_TimeSliceThread.cpp"
    "../../../../../modules/juce_core/threads/juce_TimeSliceThread.h"
    "../../../../../modules/juce_core/threads/juce_WaitableEvent.cpp"
    "../../../../../modules/juce_core/threads/juce_WaitableEvent.h"
    "../../../../../modules/juce_core/time/juce_PerformanceCounter.cpp"
    "../../../../../modules/juce_core/time/juce_PerformanceCounter.h"
    "../../../../../modules/juce_core/time/juce_RelativeTime.cpp"
    "../../../../../modules/juce_core/time/juce_RelativeTime.h"
    "../../../../../modules/juce_core/time/juce_Time.cpp"
    "../../../../../modules/juce_core/time/juce_Time.h"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTest.cpp"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTest.h"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTestCategories.h"
    "../../../../../modules/juce_core/xml/juce_XmlDocument.cpp"
    "../../../../../modules/juce_core/xml/juce_XmlDocument.h"
    "../../../../../modules/juce_core/xml/juce_XmlElement.cpp"
    "../../../../../modules/juce_core/xml/juce_XmlElement.h"
    "../../../../../modules/juce_core/zip/zlib/adler32.c"
    "../../../../../modules/juce_core/zip/zlib/compress.c"
    "../../../../../modules/juce_core/zip/zlib/crc32.c"
    "../../../../../modules/juce_core/zip/zlib/crc32.h"
    "../../../../../modules/juce_core/zip/zlib/deflate.c"
    "../../../../../modules/juce_core/zip/zlib/deflate.h"
    "../../../../../modules/juce_core/zip/zlib/infback.c"
    "../../../../../modules/juce_core/zip/zlib/inffast.c"
    "../../../../../modules/juce_core/zip/zlib/inffast.h"
    "../../../../../modules/juce_core/zip/zlib/inffixed.h"
    "../../../../../modules/juce_core/zip/zlib/inflate.c"
    "../../../../../modules/juce_core/zip/zlib/inflate.h"
    "../../../../../modules/juce_core/zip/zlib/inftrees.c"
    "../../../../../modules/juce_core/zip/zlib/inftrees.h"
    "../../../../../modules/juce_core/zip/zlib/trees.c"
    "../../../../../modules/juce_core/zip/zlib/trees.h"
    "../../../../../modules/juce_core/zip/zlib/uncompr.c"
    "../../../../../modules/juce_core/zip/zlib/zconf.h"
    "../../../../../modules/juce_core/zip/zlib/zconf.in.h"
    "../../../../../modules/juce_core/zip/zlib/zlib.h"
    "../../../../../modules/juce_core/zip/zlib/zutil.c"
    "../../../../../modules/juce_core/zip/zlib/zutil.h"
    "../../../../../modules/juce_core/zip/juce_GZIPCompressorOutputStream.cpp"
    "../../../../../modules/juce_core/zip/juce_GZIPCompressorOutputStream.h"
    "../../../../../modules/juce_core/zip/juce_GZIPDecompressorInputStream.cpp"
    "../../../../../modules/juce_core/zip/juce_GZIPDecompressorInputStream.h"
    "../../../../../modules/juce_core/zip/juce_ZipFile.cpp"
    "../../../../../modules/juce_core/zip/juce_ZipFile.h"
    "../../../../../modules/juce_core/juce_core.cpp"
    "../../../../../modules/juce_core/juce_core.mm"
    "../../../../../modules/juce_core/juce_core_CompilationTime.cpp"
    "../../../../../modules/juce_core/juce_core.h"
    "../../../../../modules/juce_cryptography/encryption/juce_BlowFish.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_BlowFish.h"
    "../../../../../modules/juce_cryptography/encryption/juce_Primes.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_Primes.h"
    "../../../../../modules/juce_cryptography/encryption/juce_RSAKey.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_RSAKey.h"
    "../../../../../modules/juce_cryptography/hashing/juce_MD5.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_MD5.h"
    "../../../../../modules/juce_cryptography/hashing/juce_SHA256.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_SHA256.h"
    "../../../../../modules/juce_cryptography/hashing/juce_Whirlpool.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_Whirlpool.h"
    "../../../../../modules/juce_cryptography/juce_cryptography.cpp"
    "../../../../../modules/juce_cryptography/juce_cryptography.mm"
    "../../../../../modules/juce_cryptography/juce_cryptography.h"
    "../../../../../modules/juce_data_structures/app_properties/juce_ApplicationProperties.cpp"
    "../../../../../modules/juce_data_structures/app_properties/juce_ApplicationProperties.h"
    "../../../../../modules/juce_data_structures/app_properties/juce_PropertiesFile.cpp"
    "../../../../../modules/juce_data_structures/app_properties/juce_PropertiesFile.h"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoableAction.cpp"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoableAction.h"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoManager.cpp"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoManager.h"
    "../../../../../modules/juce_data_structures/values/juce_CachedValue.cpp"
    "../../../../../modules/juce_data_structures/values/juce_CachedValue.h"
    "../../../../../modules/juce_data_structures/values/juce_Value.cpp"
    "../../../../../modules/juce_data_structures/values/juce_Value.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTree.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTree.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreePropertyWithDefault.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreePropertyWithDefault_test.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreeSynchroniser.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreeSynchroniser.h"
    "../../../../../modules/juce_data_structures/juce_data_structures.cpp"
    "../../../../../modules/juce_data_structures/juce_data_structures.mm"
    "../../../../../modules/juce_data_structures/juce_data_structures.h"
    "../../../../../modules/juce_dsp/containers/juce_AudioBlock.h"
    "../../../../../modules/juce_dsp/containers/juce_AudioBlock_test.cpp"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister.h"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister_Impl.h"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister_test.cpp"
    "../../../../../modules/juce_dsp/filter_design/juce_FilterDesign.cpp"
    "../../../../../modules/juce_dsp/filter_design/juce_FilterDesign.h"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution.h"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution_test.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_FFT.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_FFT.h"
    "../../../../../modules/juce_dsp/frequency/juce_FFT_test.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Windowing.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Windowing.h"
    "../../../../../modules/juce_dsp/maths/juce_FastMathApproximations.h"
    "../../../../../modules/juce_dsp/maths/juce_LogRampedValue.h"
    "../../../../../modules/juce_dsp/maths/juce_LogRampedValue_test.cpp"
    "../../../../../modules/juce_dsp/maths/juce_LookupTable.cpp"
    "../../../../../modules/juce_dsp/maths/juce_LookupTable.h"
    "../../../../../modules/juce_dsp/maths/juce_Matrix.cpp"
    "../../../../../modules/juce_dsp/maths/juce_Matrix.h"
    "../../../../../modules/juce_dsp/maths/juce_Matrix_test.cpp"
    "../../../../../modules/juce_dsp/maths/juce_Phase.h"
    "../../../../../modules/juce_dsp/maths/juce_Polynomial.h"
    "../../../../../modules/juce_dsp/maths/juce_SpecialFunctions.cpp"
    "../../../../../modules/juce_dsp/maths/juce_SpecialFunctions.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_avx.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_avx.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_fallback.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_neon.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_neon.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_sse.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_sse.h"
    "../../../../../modules/juce_dsp/processors/juce_BallisticsFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_BallisticsFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_DelayLine.cpp"
    "../../../../../modules/juce_dsp/processors/juce_DelayLine.h"
    "../../../../../modules/juce_dsp/processors/juce_DryWetMixer.cpp"
    "../../../../../modules/juce_dsp/processors/juce_DryWetMixer.h"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter_test.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FirstOrderTPTFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FirstOrderTPTFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter_Impl.h"
    "../../../../../modules/juce_dsp/processors/juce_LinkwitzRileyFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_LinkwitzRileyFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_Oversampling.cpp"
    "../../../../../modules/juce_dsp/processors/juce_Oversampling.h"
    "../../../../../modules/juce_dsp/processors/juce_Panner.cpp"
    "../../../../../modules/juce_dsp/processors/juce_Panner.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessContext.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorChain.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorChain_test.cpp"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorDuplicator.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorWrapper.h"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableTPTFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableTPTFilter.h"
    "../../../../../modules/juce_dsp/widgets/juce_Bias.h"
    "../../../../../modules/juce_dsp/widgets/juce_Chorus.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Chorus.h"
    "../../../../../modules/juce_dsp/widgets/juce_Compressor.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Compressor.h"
    "../../../../../modules/juce_dsp/widgets/juce_Gain.h"
    "../../../../../modules/juce_dsp/widgets/juce_LadderFilter.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_LadderFilter.h"
    "../../../../../modules/juce_dsp/widgets/juce_Limiter.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Limiter.h"
    "../../../../../modules/juce_dsp/widgets/juce_NoiseGate.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_NoiseGate.h"
    "../../../../../modules/juce_dsp/widgets/juce_Oscillator.h"
    "../../../../../modules/juce_dsp/widgets/juce_Phaser.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Phaser.h"
    "../../../../../modules/juce_dsp/widgets/juce_Reverb.h"
    "../../../../../modules/juce_dsp/widgets/juce_WaveShaper.h"
    "../../../../../modules/juce_dsp/juce_dsp.cpp"
    "../../../../../modules/juce_dsp/juce_dsp.mm"
    "../../../../../modules/juce_dsp/juce_dsp.h"
    "../../../../../modules/juce_events/broadcasters/juce_ActionBroadcaster.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_ActionBroadcaster.h"
    "../../../../../modules/juce_events/broadcasters/juce_ActionListener.h"
    "../../../../../modules/juce_events/broadcasters/juce_AsyncUpdater.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_AsyncUpdater.h"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeBroadcaster.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeBroadcaster.h"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeListener.h"
    "../../../../../modules/juce_events/broadcasters/juce_LockingAsyncUpdater.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_LockingAsyncUpdater.h"
    "../../../../../modules/juce_events/interprocess/juce_ChildProcessManager.cpp"
    "../../../../../modules/juce_events/interprocess/juce_ChildProcessManager.h"
    "../../../../../modules/juce_events/interprocess/juce_ConnectedChildProcess.cpp"
    "../../../../../modules/juce_events/interprocess/juce_ConnectedChildProcess.h"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnection.cpp"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnection.h"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnectionServer.cpp"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnectionServer.h"
    "../../../../../modules/juce_events/interprocess/juce_NetworkServiceDiscovery.cpp"
    "../../../../../modules/juce_events/interprocess/juce_NetworkServiceDiscovery.h"
    "../../../../../modules/juce_events/messages/juce_ApplicationBase.cpp"
    "../../../../../modules/juce_events/messages/juce_ApplicationBase.h"
    "../../../../../modules/juce_events/messages/juce_CallbackMessage.h"
    "../../../../../modules/juce_events/messages/juce_DeletedAtShutdown.cpp"
    "../../../../../modules/juce_events/messages/juce_DeletedAtShutdown.h"
    "../../../../../modules/juce_events/messages/juce_Initialisation.h"
    "../../../../../modules/juce_events/messages/juce_Message.h"
    "../../../../../modules/juce_events/messages/juce_MessageListener.cpp"
    "../../../../../modules/juce_events/messages/juce_MessageListener.h"
    "../../../../../modules/juce_events/messages/juce_MessageManager.cpp"
    "../../../../../modules/juce_events/messages/juce_MessageManager.h"
    "../../../../../modules/juce_events/messages/juce_MountedVolumeListChangeDetector.h"
    "../../../../../modules/juce_events/messages/juce_NotificationType.h"
    "../../../../../modules/juce_events/native/juce_EventLoop_linux.h"
    "../../../../../modules/juce_events/native/juce_EventLoopInternal_linux.h"
    "../../../../../modules/juce_events/native/juce_HiddenMessageWindow_windows.h"
    "../../../../../modules/juce_events/native/juce_MessageManager_ios.mm"
    "../../../../../modules/juce_events/native/juce_MessageManager_mac.mm"
    "../../../../../modules/juce_events/native/juce_MessageQueue_mac.h"
    "../../../../../modules/juce_events/native/juce_Messaging_android.cpp"
    "../../../../../modules/juce_events/native/juce_Messaging_linux.cpp"
    "../../../../../modules/juce_events/native/juce_Messaging_windows.cpp"
    "../../../../../modules/juce_events/native/juce_RunningInUnity.h"
    "../../../../../modules/juce_events/native/juce_ScopedLowPowerModeDisabler.cpp"
    "../../../../../modules/juce_events/native/juce_ScopedLowPowerModeDisabler.h"
    "../../../../../modules/juce_events/native/juce_WinRTWrapper_windows.cpp"
    "../../../../../modules/juce_events/native/juce_WinRTWrapper_windows.h"
    "../../../../../modules/juce_events/timers/juce_MultiTimer.cpp"
    "../../../../../modules/juce_events/timers/juce_MultiTimer.h"
    "../../../../../modules/juce_events/timers/juce_TimedCallback.h"
    "../../../../../modules/juce_events/timers/juce_Timer.cpp"
    "../../../../../modules/juce_events/timers/juce_Timer.h"
    "../../../../../modules/juce_events/juce_events.cpp"
    "../../../../../modules/juce_events/juce_events.mm"
    "../../../../../modules/juce_events/juce_events.h"
    "../../../../../modules/juce_graphics/colour/juce_Colour.cpp"
    "../../../../../modules/juce_graphics/colour/juce_Colour.h"
    "../../../../../modules/juce_graphics/colour/juce_ColourGradient.cpp"
    "../../../../../modules/juce_graphics/colour/juce_ColourGradient.h"
    "../../../../../modules/juce_graphics/colour/juce_Colours.cpp"
    "../../../../../modules/juce_graphics/colour/juce_Colours.h"
    "../../../../../modules/juce_graphics/colour/juce_FillType.cpp"
    "../../../../../modules/juce_graphics/colour/juce_FillType.h"
    "../../../../../modules/juce_graphics/colour/juce_PixelFormats.h"
    "../../../../../modules/juce_graphics/contexts/juce_GraphicsContext.cpp"
    "../../../../../modules/juce_graphics/contexts/juce_GraphicsContext.h"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsContext.h"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsSoftwareRenderer.cpp"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsSoftwareRenderer.h"
    "../../../../../modules/juce_graphics/detail/juce_Ranges.cpp"
    "../../../../../modules/juce_graphics/detail/juce_Ranges.h"
    "../../../../../modules/juce_graphics/effects/juce_DropShadowEffect.cpp"
    "../../../../../modules/juce_graphics/effects/juce_DropShadowEffect.h"
    "../../../../../modules/juce_graphics/effects/juce_GlowEffect.cpp"
    "../../../../../modules/juce_graphics/effects/juce_GlowEffect.h"
    "../../../../../modules/juce_graphics/effects/juce_ImageEffectFilter.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/CBDT/CBDT.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/COLR/COLR.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/COLR/colrv1-closure.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/CPAL/CPAL.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/sbix/sbix.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/svg/svg.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/composite-iter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/CompositeGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/glyf-helpers.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/glyf.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/Glyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/GlyphHeader.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/loca.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/path-builder.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/SimpleGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/SubsetGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/Coverage.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/CoverageFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/CoverageFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/RangeRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GDEF/GDEF.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/Anchor.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat3.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorMatrix.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ChainContextPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/Common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ContextPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/CursivePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/CursivePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ExtensionPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/GPOS.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/LigatureArray.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkArray.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkBasePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkBasePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkLigPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkLigPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkMarkPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkMarkPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPosFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairValueRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PosLookup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PosLookupSubTable.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePosFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ValueFormat.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ChainContextSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ContextSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ExtensionSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/GSUB.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Ligature.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/MultipleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/MultipleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ReverseChainSingleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ReverseChainSingleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Sequence.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubstFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SubstLookup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SubstLookupSubTable.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/types.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/name/name.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/coord-setter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/VARC.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/VARC.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/failing-alloc.c"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/harfbuzz-subset.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/harfbuzz.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-ankr-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-bsln-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-feat-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-just-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-kerx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-morx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-opbd-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-trak-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-ltag-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-algs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-array.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-atomic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bimap.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-page.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-set-invertible.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-set.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-json.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-text-glyphs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-text-unicode.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-serialize.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-verify.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cache.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-cs-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-dict-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff1-interp-cs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff2-interp-cs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-common.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-common.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-config.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cplusplus.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-debug.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-deprecated.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-directwrite.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-directwrite.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-dispatch.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face-builder.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-fallback-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft-colr.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gdi.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gdi.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-geometry.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-glib.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-glib.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject-structs.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject-structs.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-graphite2.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-graphite2.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-icu.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-icu.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-iter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-kern.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-limits.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-machinery.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-meta.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ms-feature-ranges.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-multimap.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-mutex.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-null.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number-parser.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-object.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-open-file.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-open-type.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-std-str.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff2-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff2-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cmap-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-color.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-color.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-deprecated.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face-table-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-font.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-gasp-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-glyf-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hdmx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-head-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hhea-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hmtx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-kern-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-base-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gdef-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gpos-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gsub-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gsubgpos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-jstf-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-maxp-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-language-static.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-language.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-os2-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-os2-unicode-ranges.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-macroman.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-table-v2subset.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-fallback.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-fallback.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-normalize.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-normalize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-fallback.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-joining-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-pua.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-win1256.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-default.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-hangul.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-hebrew.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-khmer-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-khmer.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-myanmar-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-myanmar.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-syllabic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-syllabic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-thai.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-vowel-constraints.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-vowel-constraints.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-stat-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-tag-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-tag.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-avar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-cvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-fvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-gvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-hvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-mvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-varc-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-vorg-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-outline.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-outline.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint-extents.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint-extents.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-pool.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-priority-queue.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-repacker.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-sanitize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-serialize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set-digest.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper-impl.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-static.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-string-array.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-style.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-style.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-accelerator.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff-common.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff1.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff2.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-input.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-input.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-iup.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-iup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-solver.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-solver.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan-member-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-repacker.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-repacker.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ucd-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ucd.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode-emoji-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-uniscribe.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-uniscribe.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-utf.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-vector.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-version.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-blob.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-buffer.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-font.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-shape.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb.hh"
    "../../../../../modules/juce_graphics/fonts/juce_AttributedString.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_AttributedString.h"
    "../../../../../modules/juce_graphics/fonts/juce_Font.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_Font.h"
    "../../../../../modules/juce_graphics/fonts/juce_FontOptions.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_FontOptions.h"
    "../../../../../modules/juce_graphics/fonts/juce_FunctionPointerDestructor.h"
    "../../../../../modules/juce_graphics/fonts/juce_GlyphArrangement.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_GlyphArrangement.h"
    "../../../../../modules/juce_graphics/fonts/juce_JustifiedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_LruCache.h"
    "../../../../../modules/juce_graphics/fonts/juce_ShapedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_SimpleShapedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_TextLayout.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_TextLayout.h"
    "../../../../../modules/juce_graphics/fonts/juce_Typeface.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_Typeface.h"
    "../../../../../modules/juce_graphics/fonts/juce_TypefaceFileCache.h"
    "../../../../../modules/juce_graphics/fonts/juce_TypefaceTestData.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_AffineTransform.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_AffineTransform.h"
    "../../../../../modules/juce_graphics/geometry/juce_BorderSize.h"
    "../../../../../modules/juce_graphics/geometry/juce_EdgeTable.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_EdgeTable.h"
    "../../../../../modules/juce_graphics/geometry/juce_Line.h"
    "../../../../../modules/juce_graphics/geometry/juce_Parallelogram.h"
    "../../../../../modules/juce_graphics/geometry/juce_Parallelogram_test.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_Path.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_Path.h"
    "../../../../../modules/juce_graphics/geometry/juce_PathIterator.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_PathIterator.h"
    "../../../../../modules/juce_graphics/geometry/juce_PathStrokeType.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_PathStrokeType.h"
    "../../../../../modules/juce_graphics/geometry/juce_Point.h"
    "../../../../../modules/juce_graphics/geometry/juce_Rectangle.h"
    "../../../../../modules/juce_graphics/geometry/juce_Rectangle_test.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_RectangleList.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/cderror.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/changes to libjpeg for JUCE.txt"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcapimin.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcapistd.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jccoefct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jccolor.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcdctmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jchuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jchuff.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcinit.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmainct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmarker.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmaster.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcomapi.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jconfig.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcparam.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcphuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcprepct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcsample.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jctrans.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdapimin.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdapistd.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdatasrc.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdcoefct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdcolor.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdct.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jddctmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdhuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdhuff.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdinput.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmainct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmarker.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmaster.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmerge.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdphuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdpostct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdsample.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdtrans.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jerror.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jerror.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctflt.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctfst.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctint.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctflt.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctfst.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctint.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctred.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jinclude.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemnobs.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemsys.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmorecfg.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jpegint.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jpeglib.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jquant1.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jquant2.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jutils.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jversion.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/transupp.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/transupp.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/libpng_readme.txt"
    "../../../../../modules/juce_graphics/image_formats/pnglib/png.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/png.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngconf.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngdebug.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngerror.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngget.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pnginfo.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngmem.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngpread.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngpriv.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngread.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrio.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrtran.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrutil.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngset.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngstruct.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngtrans.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwio.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwrite.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwtran.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwutil.c"
    "../../../../../modules/juce_graphics/image_formats/juce_GIFLoader.cpp"
    "../../../../../modules/juce_graphics/image_formats/juce_JPEGLoader.cpp"
    "../../../../../modules/juce_graphics/image_formats/juce_PNGLoader.cpp"
    "../../../../../modules/juce_graphics/images/juce_Image.cpp"
    "../../../../../modules/juce_graphics/images/juce_Image.h"
    "../../../../../modules/juce_graphics/images/juce_ImageCache.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageCache.h"
    "../../../../../modules/juce_graphics/images/juce_ImageConvolutionKernel.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageConvolutionKernel.h"
    "../../../../../modules/juce_graphics/images/juce_ImageFileFormat.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageFileFormat.h"
    "../../../../../modules/juce_graphics/images/juce_ScaledImage.h"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsContext_mac.h"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsContext_mac.mm"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsHelpers_mac.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DGraphicsContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DGraphicsContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHelpers_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHwndContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHwndContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImage_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImage_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImageContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImageContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DMetrics_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DMetrics_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DPixelDataPage_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DResources_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_DirectWriteTypeface_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_DirectX_windows.h"
    "../../../../../modules/juce_graphics/native/juce_EventTracing.h"
    "../../../../../modules/juce_graphics/native/juce_Fonts_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_freetype.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_linux.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_mac.mm"
    "../../../../../modules/juce_graphics/native/juce_GraphicsContext_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_linux.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_mac.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_RenderingHelpers.h"
    "../../../../../modules/juce_graphics/placement/juce_Justification.h"
    "../../../../../modules/juce_graphics/placement/juce_RectanglePlacement.cpp"
    "../../../../../modules/juce_graphics/placement/juce_RectanglePlacement.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBAlgorithm.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBBase.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBBidiType.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBCodepoint.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBCodepointSequence.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBConfig.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBGeneralCategory.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBLine.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBMirrorLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBParagraph.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBScript.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBScriptLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SheenBidi.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiChain.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiChain.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiTypeLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiTypeLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketQueue.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketQueue.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketType.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/GeneralCategoryLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/GeneralCategoryLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/IsolatingRun.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/IsolatingRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/LevelRun.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/LevelRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/PairingLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/PairingLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunExtrema.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunKind.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunQueue.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunQueue.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAlgorithm.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAlgorithm.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAssert.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBBase.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBBase.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBCodepointSequence.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBCodepointSequence.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLine.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLine.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLog.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLog.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBMirrorLocator.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBMirrorLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBParagraph.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBParagraph.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBScriptLocator.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBScriptLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptStack.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptStack.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SheenBidi.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/StatusStack.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/StatusStack.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/JUCE_CHANGES.txt"
    "../../../../../modules/juce_graphics/unicode/juce_Unicode.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeBidi.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeGenerated.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeLine.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeScript.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeUtils.cpp"
    "../../../../../modules/juce_graphics/juce_graphics.cpp"
    "../../../../../modules/juce_graphics/juce_graphics.mm"
    "../../../../../modules/juce_graphics/juce_graphics_Harfbuzz.cpp"
    "../../../../../modules/juce_graphics/juce_graphics_Sheenbidi.c"
    "../../../../../modules/juce_graphics/juce_graphics.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityActions.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityEvent.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityRole.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityCellInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityTableInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityTextInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityValueInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityHandler.cpp"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityHandler.h"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityState.h"
    "../../../../../modules/juce_gui_basics/application/juce_Application.cpp"
    "../../../../../modules/juce_gui_basics/application/juce_Application.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ArrowButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ArrowButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_Button.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_Button.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_DrawableButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_DrawableButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_HyperlinkButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_HyperlinkButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ImageButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ImageButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ShapeButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ShapeButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_TextButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_TextButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToggleButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToggleButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToolbarButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToolbarButton.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandID.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandInfo.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandInfo.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandManager.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandManager.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandTarget.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandTarget.h"
    "../../../../../modules/juce_gui_basics/commands/juce_KeyPressMappingSet.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_KeyPressMappingSet.h"
    "../../../../../modules/juce_gui_basics/components/juce_CachedComponentImage.h"
    "../../../../../modules/juce_gui_basics/components/juce_Component.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_Component.h"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentListener.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentListener.h"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentTraverser.h"
    "../../../../../modules/juce_gui_basics/components/juce_FocusTraverser.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_FocusTraverser.h"
    "../../../../../modules/juce_gui_basics/components/juce_ModalComponentManager.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_ModalComponentManager.h"
    "../../../../../modules/juce_gui_basics/desktop/juce_Desktop.cpp"
    "../../../../../modules/juce_gui_basics/desktop/juce_Desktop.h"
    "../../../../../modules/juce_gui_basics/desktop/juce_Displays.cpp"
    "../../../../../modules/juce_gui_basics/desktop/juce_Displays.h"
    "../../../../../modules/juce_gui_basics/detail/juce_AccessibilityHelpers.cpp"
    "../../../../../modules/juce_gui_basics/detail/juce_AccessibilityHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_AlertWindowHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ButtonAccessibilityHandler.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ComponentHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_CustomMouseCursorInfo.h"
    "../../../../../modules/juce_gui_basics/detail/juce_FocusHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_FocusRestorer.h"
    "../../../../../modules/juce_gui_basics/detail/juce_LookAndFeelHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_MouseInputSourceImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_MouseInputSourceList.h"
    "../../../../../modules/juce_gui_basics/detail/juce_PointerState.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScalingHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedContentSharerImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedContentSharerInterface.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedMessageBoxImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedMessageBoxInterface.h"
    "../../../../../modules/juce_gui_basics/detail/juce_StandardCachedComponentImage.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ToolbarItemDragAndDropOverlayComponent.h"
    "../../../../../modules/juce_gui_basics/detail/juce_TopLevelWindowManager.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ViewportHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_WindowingHelpers.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_Drawable.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_Drawable.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableComposite.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableComposite.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableImage.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableImage.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawablePath.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawablePath.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableRectangle.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableRectangle.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableShape.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableShape.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableText.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableText.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_SVGParser.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ContentSharer.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ContentSharer.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsDisplayComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsDisplayComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsList.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsList.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserListener.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooser.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooser.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooserDialogBox.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooserDialogBox.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileListComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileListComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilenameComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilenameComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilePreviewComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileSearchPathListComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileSearchPathListComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileTreeComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileTreeComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ImagePreviewComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ImagePreviewComponent.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_CaretComponent.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_CaretComponent.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyboardFocusTraverser.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyboardFocusTraverser.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyListener.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyListener.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyPress.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyPress.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_ModifierKeys.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_ModifierKeys.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_SystemClipboard.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_TextEditorKeyMapper.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_TextInputTarget.h"
    "../../../../../modules/juce_gui_basics/layout/juce_AnimatedPosition.h"
    "../../../../../modules/juce_gui_basics/layout/juce_AnimatedPositionBehaviours.h"
    "../../../../../modules/juce_gui_basics/layout/juce_BorderedComponentBoundsConstrainer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_BorderedComponentBoundsConstrainer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentAnimator.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentAnimator.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBoundsConstrainer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBoundsConstrainer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBuilder.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBuilder.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentMovementWatcher.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentMovementWatcher.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ConcertinaPanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ConcertinaPanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexBox.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexBox.h"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexItem.h"
    "../../../../../modules/juce_gui_basics/layout/juce_Grid.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_Grid.h"
    "../../../../../modules/juce_gui_basics/layout/juce_GridItem.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_GridItem.h"
    "../../../../../modules/juce_gui_basics/layout/juce_GroupComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_GroupComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_MultiDocumentPanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_MultiDocumentPanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableBorderComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableBorderComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableCornerComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableCornerComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableEdgeComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableEdgeComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ScrollBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ScrollBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_SidePanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_SidePanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutManager.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutManager.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutResizerBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutResizerBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableObjectResizer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableObjectResizer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedButtonBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedButtonBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_Viewport.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_Viewport.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V1.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V1.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V2.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V2.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V3.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V3.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V4.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V4.h"
    "../../../../../modules/juce_gui_basics/menus/juce_BurgerMenuComponent.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_BurgerMenuComponent.h"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarComponent.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarComponent.h"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarModel.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarModel.h"
    "../../../../../modules/juce_gui_basics/menus/juce_PopupMenu.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_PopupMenu.h"
    "../../../../../modules/juce_gui_basics/misc/juce_BubbleComponent.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_BubbleComponent.h"
    "../../../../../modules/juce_gui_basics/misc/juce_DropShadower.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_DropShadower.h"
    "../../../../../modules/juce_gui_basics/misc/juce_FocusOutline.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_FocusOutline.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_ComponentDragger.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_ComponentDragger.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropContainer.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropContainer.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_FileDragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_LassoComponent.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseCursor.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseCursor.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseEvent.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseEvent.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInactivityDetector.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInactivityDetector.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInputSource.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInputSource.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseListener.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseListener.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_SelectedItemSet.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_TextDragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_TooltipClient.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_android.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_ios.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_mac.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityElement_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityElement_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilitySharedCode_mac.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityTextHelpers.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityTextHelpers_test.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAExpandCollapseProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAGridItemProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAGridProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAHelpers_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAInvokeProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAProviderBase_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAProviders_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIARangeValueProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIASelectionProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIATextProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAToggleProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIATransformProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAValueProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAWindowProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_WindowsUIAWrapper_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_CGMetalLayerRenderer_mac.h"
    "../../../../../modules/juce_gui_basics/native/juce_ContentSharer_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_ContentSharer_ios.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_DragAndDrop_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_DragAndDrop_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_MainMenu_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_MouseCursor_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_MultiTouchMapper.h"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeModalWrapperComponent_ios.h"
    "../../../../../modules/juce_gui_basics/native/juce_NSViewComponentPeer_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_PerScreenDisplayLinks_mac.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedDPIAwarenessDisabler.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedDPIAwarenessDisabler.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedThreadDPIAwarenessSetter_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedWindowAssociation_linux.h"
    "../../../../../modules/juce_gui_basics/native/juce_UIViewComponentPeer_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_VBlank_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowsHooks_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowsHooks_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XSymbols_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XSymbols_linux.h"
    "../../../../../modules/juce_gui_basics/native/juce_XWindowSystem_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XWindowSystem_linux.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_MarkerList.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_MarkerList.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinate.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinate.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinatePositioner.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinatePositioner.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeParallelogram.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeParallelogram.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePoint.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePoint.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePointPath.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePointPath.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeRectangle.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeRectangle.h"
    "../../../../../modules/juce_gui_basics/properties/juce_BooleanPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_BooleanPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_ButtonPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_ButtonPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_ChoicePropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_ChoicePropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_MultiChoicePropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_MultiChoicePropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyPanel.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyPanel.h"
    "../../../../../modules/juce_gui_basics/properties/juce_SliderPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_SliderPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_TextPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_TextPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ComboBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ComboBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ImageComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ImageComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Label.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Label.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ListBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ListBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ProgressBar.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ProgressBar.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Slider.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Slider.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableHeaderComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableHeaderComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableListBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableListBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TextEditor.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TextEditor.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Toolbar.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Toolbar.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemFactory.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemPalette.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemPalette.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TreeView.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TreeView.h"
    "../../../../../modules/juce_gui_basics/windows/juce_AlertWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_AlertWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_CallOutBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_CallOutBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ComponentPeer.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ComponentPeer.h"
    "../../../../../modules/juce_gui_basics/windows/juce_DialogWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_DialogWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_DocumentWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_DocumentWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_MessageBoxOptions.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_MessageBoxOptions.h"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeMessageBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeMessageBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeScaleFactorNotifier.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeScaleFactorNotifier.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ResizableWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ResizableWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ScopedMessageBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ScopedMessageBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ThreadWithProgressWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ThreadWithProgressWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_TooltipWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_TooltipWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_TopLevelWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_TopLevelWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_VBlankAttachment.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_VBlankAttachment.h"
    "../../../../../modules/juce_gui_basics/windows/juce_WindowUtils.h"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.cpp"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.mm"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeDocument.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeDocument.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeEditorComponent.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeEditorComponent.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniserFunctions.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_LuaCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_LuaCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_XMLCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_XMLCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/detail/juce_WebControlRelayEvents.h"
    "../../../../../modules/juce_gui_extra/documents/juce_FileBasedDocument.cpp"
    "../../../../../modules/juce_gui_extra/documents/juce_FileBasedDocument.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_ActiveXControlComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_AndroidViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_HWNDComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_NSViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_UIViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_XEmbedComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_AnimatedAppComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_AnimatedAppComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_AppleRemote.h"
    "../../../../../modules/juce_gui_extra/misc/juce_BubbleMessageComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_BubbleMessageComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_ColourSelector.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_ColourSelector.h"
    "../../../../../modules/juce_gui_extra/misc/juce_KeyMappingEditorComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_KeyMappingEditorComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_LiveConstantEditor.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_LiveConstantEditor.h"
    "../../../../../modules/juce_gui_extra/misc/juce_PreferencesPanel.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_PreferencesPanel.h"
    "../../../../../modules/juce_gui_extra/misc/juce_PushNotifications.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_PushNotifications.h"
    "../../../../../modules/juce_gui_extra/misc/juce_RecentlyOpenedFilesList.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_RecentlyOpenedFilesList.h"
    "../../../../../modules/juce_gui_extra/misc/juce_SplashScreen.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_SplashScreen.h"
    "../../../../../modules/juce_gui_extra/misc/juce_SystemTrayIconComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_SystemTrayIconComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebBrowserComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_WebBrowserComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlParameterIndexReceiver.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlRelays.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlRelays.h"
    "../../../../../modules/juce_gui_extra/native/juce_ActiveXComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_AndroidViewComponent.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_AppleRemote_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_HWNDComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_NSViewComponent_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_NSViewFrameWatcher_mac.h"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_android.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_ios.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_mac.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_linux.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_mac.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_UIViewComponent_ios.mm"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_android.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_linux.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_XEmbedComponent_linux.cpp"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.cpp"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.mm"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.h"
    "../../../../../modules/juce_javascript/choc/containers/choc_Value.h"
    "../../../../../modules/juce_javascript/choc/javascript/choc_javascript.h"
    "../../../../../modules/juce_javascript/choc/javascript/choc_javascript_QuickJS.h"
    "../../../../../modules/juce_javascript/choc/math/choc_MathHelpers.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_Assert.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_DisableAllWarnings.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_ReenableAllWarnings.h"
    "../../../../../modules/juce_javascript/choc/text/choc_FloatToString.h"
    "../../../../../modules/juce_javascript/choc/text/choc_JSON.h"
    "../../../../../modules/juce_javascript/choc/text/choc_StringUtilities.h"
    "../../../../../modules/juce_javascript/choc/text/choc_UTF8.h"
    "../../../../../modules/juce_javascript/choc/LICENSE.md"
    "../../../../../modules/juce_javascript/detail/juce_QuickJSHelpers.h"
    "../../../../../modules/juce_javascript/javascript/juce_Javascript_test.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JavascriptEngine.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JavascriptEngine.h"
    "../../../../../modules/juce_javascript/javascript/juce_JSCursor.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JSCursor.h"
    "../../../../../modules/juce_javascript/javascript/juce_JSObject.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JSObject.h"
    "../../../../../modules/juce_javascript/juce_javascript.cpp"
    "../../../../../modules/juce_javascript/juce_javascript.h"
    "../../../../../modules/juce_opengl/geometry/juce_Draggable3DOrientation.h"
    "../../../../../modules/juce_opengl/geometry/juce_Matrix3D.h"
    "../../../../../modules/juce_opengl/geometry/juce_Quaternion.h"
    "../../../../../modules/juce_opengl/geometry/juce_Vector3D.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_android.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_ios.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_linux.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_mac.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_windows.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGLExtensions.h"
    "../../../../../modules/juce_opengl/opengl/juce_gl.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_gl.h"
    "../../../../../modules/juce_opengl/opengl/juce_gles2.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_gles2.h"
    "../../../../../modules/juce_opengl/opengl/juce_khrplatform.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLContext.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLContext.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLFrameBuffer.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLFrameBuffer.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLGraphicsContext.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLGraphicsContext.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLHelpers.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLHelpers.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLImage.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLImage.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLPixelFormat.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLPixelFormat.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLRenderer.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLShaderProgram.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLShaderProgram.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLTexture.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLTexture.h"
    "../../../../../modules/juce_opengl/opengl/juce_wgl.h"
    "../../../../../modules/juce_opengl/utils/juce_OpenGLAppComponent.cpp"
    "../../../../../modules/juce_opengl/utils/juce_OpenGLAppComponent.h"
    "../../../../../modules/juce_opengl/juce_opengl.cpp"
    "../../../../../modules/juce_opengl/juce_opengl.mm"
    "../../../../../modules/juce_opengl/juce_opengl.h"
    "../../../../../modules/juce_osc/osc/juce_OSCAddress.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCAddress.h"
    "../../../../../modules/juce_osc/osc/juce_OSCArgument.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCArgument.h"
    "../../../../../modules/juce_osc/osc/juce_OSCBundle.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCBundle.h"
    "../../../../../modules/juce_osc/osc/juce_OSCMessage.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCMessage.h"
    "../../../../../modules/juce_osc/osc/juce_OSCReceiver.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCReceiver.h"
    "../../../../../modules/juce_osc/osc/juce_OSCSender.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCSender.h"
    "../../../../../modules/juce_osc/osc/juce_OSCTimeTag.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCTimeTag.h"
    "../../../../../modules/juce_osc/osc/juce_OSCTypes.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCTypes.h"
    "../../../../../modules/juce_osc/juce_osc.cpp"
    "../../../../../modules/juce_osc/juce_osc.h"
    "../../../../../modules/juce_product_unlocking/in_app_purchases/juce_InAppPurchases.cpp"
    "../../../../../modules/juce_product_unlocking/in_app_purchases/juce_InAppPurchases.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_KeyFileGeneration.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockForm.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockForm.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockStatus.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockStatus.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_TracktionMarketplaceStatus.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_TracktionMarketplaceStatus.h"
    "../../../../../modules/juce_product_unlocking/native/juce_InAppPurchases_android.cpp"
    "../../../../../modules/juce_product_unlocking/native/juce_InAppPurchases_ios.cpp"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.cpp"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.mm"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.h"
    "../../../../../modules/juce_video/capture/juce_CameraDevice.cpp"
    "../../../../../modules/juce_video/capture/juce_CameraDevice.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_android.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_ios.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_mac.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_windows.h"
    "../../../../../modules/juce_video/native/juce_Video_android.h"
    "../../../../../modules/juce_video/native/juce_Video_mac.h"
    "../../../../../modules/juce_video/native/juce_Video_windows.h"
    "../../../../../modules/juce_video/playback/juce_VideoComponent.cpp"
    "../../../../../modules/juce_video/playback/juce_VideoComponent.h"
    "../../../../../modules/juce_video/juce_video.cpp"
    "../../../../../modules/juce_video/juce_video.mm"
    "../../../../../modules/juce_video/juce_video.h"
    "../../../JuceLibraryCode/include_juce_analytics.cpp"
    "../../../JuceLibraryCode/include_juce_animation.cpp"
    "../../../JuceLibraryCode/include_juce_audio_basics.cpp"
    "../../../JuceLibraryCode/include_juce_audio_devices.cpp"
    "../../../JuceLibraryCode/include_juce_audio_formats.cpp"
    "../../../JuceLibraryCode/include_juce_audio_processors.cpp"
    "../../../JuceLibraryCode/include_juce_audio_processors_ara.cpp"
    "../../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp"
    "../../../JuceLibraryCode/include_juce_audio_utils.cpp"
    "../../../JuceLibraryCode/include_juce_box2d.cpp"
    "../../../JuceLibraryCode/include_juce_core.cpp"
    "../../../JuceLibraryCode/include_juce_core_CompilationTime.cpp"
    "../../../JuceLibraryCode/include_juce_cryptography.cpp"
    "../../../JuceLibraryCode/include_juce_data_structures.cpp"
    "../../../JuceLibraryCode/include_juce_dsp.cpp"
    "../../../JuceLibraryCode/include_juce_events.cpp"
    "../../../JuceLibraryCode/include_juce_graphics.cpp"
    "../../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp"
    "../../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c"
    "../../../JuceLibraryCode/include_juce_gui_basics.cpp"
    "../../../JuceLibraryCode/include_juce_gui_extra.cpp"
    "../../../JuceLibraryCode/include_juce_javascript.cpp"
    "../../../JuceLibraryCode/include_juce_opengl.cpp"
    "../../../JuceLibraryCode/include_juce_osc.cpp"
    "../../../JuceLibraryCode/include_juce_product_unlocking.cpp"
    "../../../JuceLibraryCode/include_juce_video.cpp"
    "../../../JuceLibraryCode/JuceHeader.h"
)

set_source_files_properties(
    "../../../Source/Demos/IntroScreen.h"
    "../../../Source/Demos/JUCEDemos.h"
    "../../../Source/UI/DemoContentComponent.h"
    "../../../Source/UI/MainComponent.h"
    "../../../Source/UI/SettingsContent.h"
    "../../../Source/JUCEAppIcon.png"
    "../../../../../modules/juce_analytics/analytics/juce_Analytics.cpp"
    "../../../../../modules/juce_analytics/analytics/juce_Analytics.h"
    "../../../../../modules/juce_analytics/analytics/juce_ButtonTracker.cpp"
    "../../../../../modules/juce_analytics/analytics/juce_ButtonTracker.h"
    "../../../../../modules/juce_analytics/destinations/juce_AnalyticsDestination.h"
    "../../../../../modules/juce_analytics/destinations/juce_ThreadedAnalyticsDestination.cpp"
    "../../../../../modules/juce_analytics/destinations/juce_ThreadedAnalyticsDestination.h"
    "../../../../../modules/juce_analytics/juce_analytics.cpp"
    "../../../../../modules/juce_analytics/juce_analytics.h"
    "../../../../../modules/juce_animation/animation/juce_Animator.cpp"
    "../../../../../modules/juce_animation/animation/juce_Animator.h"
    "../../../../../modules/juce_animation/animation/juce_AnimatorSetBuilder.cpp"
    "../../../../../modules/juce_animation/animation/juce_AnimatorSetBuilder.h"
    "../../../../../modules/juce_animation/animation/juce_AnimatorUpdater.cpp"
    "../../../../../modules/juce_animation/animation/juce_AnimatorUpdater.h"
    "../../../../../modules/juce_animation/animation/juce_Easings.cpp"
    "../../../../../modules/juce_animation/animation/juce_Easings.h"
    "../../../../../modules/juce_animation/animation/juce_StaticAnimationLimits.h"
    "../../../../../modules/juce_animation/animation/juce_ValueAnimatorBuilder.cpp"
    "../../../../../modules/juce_animation/animation/juce_ValueAnimatorBuilder.h"
    "../../../../../modules/juce_animation/animation/juce_VBlankAnimatorUpdater.h"
    "../../../../../modules/juce_animation/detail/chromium/cubic_bezier.cc"
    "../../../../../modules/juce_animation/detail/chromium/cubic_bezier.h"
    "../../../../../modules/juce_animation/detail/juce_ArrayAndTupleOps.h"
    "../../../../../modules/juce_animation/juce_animation.cpp"
    "../../../../../modules/juce_animation/juce_animation.h"
    "../../../../../modules/juce_audio_basics/audio_play_head/juce_AudioPlayHead.cpp"
    "../../../../../modules/juce_audio_basics/audio_play_head/juce_AudioPlayHead.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioChannelSet.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioChannelSet.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioDataConverters.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioDataConverters.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioProcessLoadMeasurer.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioProcessLoadMeasurer.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_AudioSampleBuffer.h"
    "../../../../../modules/juce_audio_basics/buffers/juce_FloatVectorOperations.cpp"
    "../../../../../modules/juce_audio_basics/buffers/juce_FloatVectorOperations.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMP.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMP_test.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPacket.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPackets.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPBytesOnGroup.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPConversion.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPConverters.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPDeviceInfo.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPDispatcher.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPFactory.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPIterator.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPIterator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToBytestreamTranslator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToMidi2DefaultTranslator.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPMidi1ToMidi2DefaultTranslator.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPProtocols.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPReceiver.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPSysEx7.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPSysEx7.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPUtils.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPUtils.h"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPView.cpp"
    "../../../../../modules/juce_audio_basics/midi/ump/juce_UMPView.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiBuffer.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiBuffer.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiDataConcatenator.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiFile.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiFile.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiKeyboardState.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiKeyboardState.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessage.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessage.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessageSequence.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiMessageSequence.h"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiRPN.cpp"
    "../../../../../modules/juce_audio_basics/midi/juce_MidiRPN.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEInstrument.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEInstrument.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEMessages.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEMessages.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPENote.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPENote.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiser.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiser.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserBase.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserBase.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserVoice.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPESynthesiserVoice.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEUtils.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEUtils.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEValue.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEValue.h"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEZoneLayout.cpp"
    "../../../../../modules/juce_audio_basics/mpe/juce_MPEZoneLayout.h"
    "../../../../../modules/juce_audio_basics/native/juce_AudioWorkgroup_mac.h"
    "../../../../../modules/juce_audio_basics/native/juce_CoreAudioLayouts_mac.h"
    "../../../../../modules/juce_audio_basics/native/juce_CoreAudioTimeConversions_mac.h"
    "../../../../../modules/juce_audio_basics/sources/juce_AudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_BufferingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_BufferingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ChannelRemappingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ChannelRemappingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_IIRFilterAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_IIRFilterAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_MemoryAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_MemoryAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_MixerAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_MixerAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_PositionableAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_PositionableAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ResamplingAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ResamplingAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ReverbAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ReverbAudioSource.h"
    "../../../../../modules/juce_audio_basics/sources/juce_ToneGeneratorAudioSource.cpp"
    "../../../../../modules/juce_audio_basics/sources/juce_ToneGeneratorAudioSource.h"
    "../../../../../modules/juce_audio_basics/synthesisers/juce_Synthesiser.cpp"
    "../../../../../modules/juce_audio_basics/synthesisers/juce_Synthesiser.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_ADSR.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_ADSR_test.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_AudioWorkgroup.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_AudioWorkgroup.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_Decibels.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_GenericInterpolator.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_IIRFilter.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_IIRFilter.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_Interpolators.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_Interpolators.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_LagrangeInterpolator.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_Reverb.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_SmoothedValue.cpp"
    "../../../../../modules/juce_audio_basics/utilities/juce_SmoothedValue.h"
    "../../../../../modules/juce_audio_basics/utilities/juce_WindowedSincInterpolator.cpp"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.cpp"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.mm"
    "../../../../../modules/juce_audio_basics/juce_audio_basics.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioDeviceManager.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioDeviceManager.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODevice.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODevice.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODeviceType.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_AudioIODeviceType.h"
    "../../../../../modules/juce_audio_devices/audio_io/juce_SampleRateHelpers.cpp"
    "../../../../../modules/juce_audio_devices/audio_io/juce_SystemAudioVolume.h"
    "../../../../../modules/juce_audio_devices/midi_io/ump/juce_UMPBytestreamInputHandler.h"
    "../../../../../modules/juce_audio_devices/midi_io/ump/juce_UMPU32InputHandler.h"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDeviceListConnectionBroadcaster.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDevices.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiDevices.h"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiMessageCollector.cpp"
    "../../../../../modules/juce_audio_devices/midi_io/juce_MidiMessageCollector.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamBase.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamBuilder.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/AudioStreamCallback.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Definitions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FifoBuffer.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FifoControllerBase.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/FullDuplexStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/LatencyTuner.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Oboe.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/OboeExtensions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/ResultWithValue.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/StabilizedCallback.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Utilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/include/oboe/Version.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioExtensions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioLoader.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AAudioLoader.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AudioStreamAAudio.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/aaudio/AudioStreamAAudio.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AdpfWrapper.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AdpfWrapper.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioClock.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioSourceCaller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioSourceCaller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioStream.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/AudioStreamBuilder.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/DataConversionFlowGraph.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/DataConversionFlowGraph.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FilterAudioStream.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FilterAudioStream.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockAdapter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockAdapter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockReader.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockReader.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockWriter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/FixedBlockWriter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/LatencyTuner.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/MonotonicCounter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/OboeDebug.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/OboeExtensions.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/QuirksManager.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/QuirksManager.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/README.md"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceFloatCaller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceFloatCaller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI16Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI16Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI24Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI24Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI32Caller.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/SourceI32Caller.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/StabilizedCallback.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Trace.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Trace.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Utilities.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/common/Version.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoBuffer.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoController.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoController.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerBase.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerIndirect.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/fifo/FifoControllerIndirect.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/HyperbolicCosineWindow.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/IntegerRatio.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/IntegerRatio.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/KaiserWindow.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/LinearResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/LinearResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/MultiChannelResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/MultiChannelResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerMono.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerMono.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerStereo.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/PolyphaseResamplerStereo.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/README.md"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/ResamplerDefinitions.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResampler.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResampler.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResamplerStereo.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/resampler/SincResamplerStereo.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ChannelCountConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ChannelCountConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ClipToRange.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ClipToRange.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowGraphNode.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowGraphNode.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/FlowgraphUtilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/Limiter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/Limiter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ManyToMultiConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/ManyToMultiConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoBlend.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoBlend.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoToMultiConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MonoToMultiConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToManyConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToManyConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToMonoConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/MultiToMonoConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/RampLinear.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/RampLinear.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SampleRateConverter.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SampleRateConverter.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkFloat.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkFloat.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI8_24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI8_24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI16.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI16.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI32.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SinkI32.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceFloat.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceFloat.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI8_24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI8_24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI16.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI16.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI24.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI24.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI32.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/flowgraph/SourceI32.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioInputStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioInputStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioOutputStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioOutputStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamBuffered.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamBuffered.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/AudioStreamOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/EngineOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/EngineOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OpenSLESUtilities.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OpenSLESUtilities.h"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OutputMixerOpenSLES.cpp"
    "../../../../../modules/juce_audio_devices/native/oboe/src/opensles/OutputMixerOpenSLES.h"
    "../../../../../modules/juce_audio_devices/native/oboe/CMakeLists.txt"
    "../../../../../modules/juce_audio_devices/native/oboe/README.md"
    "../../../../../modules/juce_audio_devices/native/juce_ALSA_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_ASIO_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_ios.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Audio_ios.h"
    "../../../../../modules/juce_audio_devices/native/juce_Bela_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_CoreAudio_mac.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_CoreMidi_mac.mm"
    "../../../../../modules/juce_audio_devices/native/juce_DirectSound_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_HighPerformanceAudioHelpers_android.h"
    "../../../../../modules/juce_audio_devices/native/juce_JackAudio.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_linux.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Midi_windows.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_Oboe_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_OpenSL_android.cpp"
    "../../../../../modules/juce_audio_devices/native/juce_WASAPI_windows.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioSourcePlayer.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioSourcePlayer.h"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioTransportSource.cpp"
    "../../../../../modules/juce_audio_devices/sources/juce_AudioTransportSource.h"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.cpp"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.mm"
    "../../../../../modules/juce_audio_devices/juce_audio_devices.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/bitreader_read_rice_signed_block.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/lpc_compute_autocorrelation_intrin.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/deduplication/lpc_compute_autocorrelation_intrin_neon.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitmath.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitreader.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/bitwriter.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/cpu.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/crc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/fixed.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/float.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/format.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/lpc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/md5.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/memory.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/stream_encoder_framing.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/private/window.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/protected/stream_decoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/include/protected/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitmath.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitreader.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/bitwriter.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/cpu.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/crc.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/fixed.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/float.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/format.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/lpc_flac.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/lpc_intrin_neon.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/md5.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/memory.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_decoder.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_encoder.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/stream_encoder_framing.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/libFLAC/window_flac.c"
    "../../../../../modules/juce_audio_formats/codecs/flac/all.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/alloc.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/assert.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/callback.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/compat.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/endswap.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/export.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/Flac Licence.txt"
    "../../../../../modules/juce_audio_formats/codecs/flac/format.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/JUCE_CHANGES.txt"
    "../../../../../modules/juce_audio_formats/codecs/flac/metadata.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/ordinals.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/private.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/stream_decoder.h"
    "../../../../../modules/juce_audio_formats/codecs/flac/stream_encoder.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/coupled/res_books_51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/coupled/res_books_stereo.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/floor/floor_books.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/books/uncoupled/res_books_uncoupled.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/floor_all.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_11.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/psych_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44p51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/residue_44u.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_8.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_11.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_16.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_22.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_32.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44p51.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_44u.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/modes/setup_X.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/analysis.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/backends.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/bitrate.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/bitrate.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/block.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codebook.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codebook.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/codec_internal.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/envelope.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/envelope.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/floor0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/floor1.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/highlevel.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/info.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lookup_data.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lpc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lpc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lsp.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/lsp.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mapping0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/masking.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mdct.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/mdct.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/misc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/misc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/os.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/psy.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/psy.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/registry.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/registry.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/res0.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/scales.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/sharedbook.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/smallft.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/smallft.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/synthesis.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/vorbisenc.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/vorbisfile.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/window.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/lib/window.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/libvorbis-1.3.7/README.md"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/bitwise.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/codec.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/config_types.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/crctable.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/framing.c"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/Ogg Vorbis Licence.txt"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/ogg.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/os_types.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/vorbisenc.h"
    "../../../../../modules/juce_audio_formats/codecs/oggvorbis/vorbisfile.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_AiffAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_AiffAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_CoreAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_CoreAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_FlacAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_FlacAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_LAMEEncoderAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_LAMEEncoderAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_MP3AudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_MP3AudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_OggVorbisAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_OggVorbisAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_WavAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_WavAudioFormat.h"
    "../../../../../modules/juce_audio_formats/codecs/juce_WindowsMediaAudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/codecs/juce_WindowsMediaAudioFormat.h"
    "../../../../../modules/juce_audio_formats/format/juce_ARAAudioReaders.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_ARAAudioReaders.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormat.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormat.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatManager.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatManager.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReaderSource.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatReaderSource.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatWriter.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioFormatWriter.h"
    "../../../../../modules/juce_audio_formats/format/juce_AudioSubsectionReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_AudioSubsectionReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_BufferingAudioFormatReader.cpp"
    "../../../../../modules/juce_audio_formats/format/juce_BufferingAudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/format/juce_MemoryMappedAudioFormatReader.h"
    "../../../../../modules/juce_audio_formats/sampler/juce_Sampler.cpp"
    "../../../../../modules/juce_audio_formats/sampler/juce_Sampler.h"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.cpp"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.mm"
    "../../../../../modules/juce_audio_formats/juce_audio_formats.h"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormatManager.cpp"
    "../../../../../modules/juce_audio_processors/format/juce_AudioPluginFormatManager.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/lilv/lilv.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/lilv/lilvmm.hpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/common.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/tree.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/zix/tree.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/collections.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/filesystem.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/filesystem.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/instance.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/lib.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/lilv_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/node.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/plugin.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/pluginclass.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/port.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/query.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/scalepoint.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/state.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/ui.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/util.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src/world.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom-test-utils.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom-test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/atom.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/forge-overflow-test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/forge.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/atom/util.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/buf-size/buf-size.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/attributes.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/lv2.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/core/lv2_util.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/data-access/data-access.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/dynmanifest/dynmanifest.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/event/event-helpers.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/event/event.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/instance-access/instance-access.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/log/log.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/log/logger.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/midi/midi.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/morph/morph.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/options/options.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/parameters/parameters.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/patch/patch.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/port-groups/port-groups.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/port-props/port-props.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/presets/presets.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/resize-port/resize-port.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/state/state.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/time/time.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/ui/ui.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/units/units.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/uri-map/uri-map.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/urid/urid.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2/lv2/worker/worker.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/serd/serd.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/attributes.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/base64.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/base64.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_sink.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_source.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/byte_source.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/env.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/n3.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/node.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/node.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/reader.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/reader.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serd_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serd_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/serdi.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/stack.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/string.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/string_utils.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/system.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/system.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/uri.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/uri_utils.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd/src/writer.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/sord/sord.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/sord/sordmm.hpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/btree.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/btree.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/common.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/digest.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/digest.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/hash.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/zix/hash.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_internal.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_test.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sord_validate.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sordi.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/sordmm_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src/syntax.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom/sratom/sratom.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom/src/sratom.c"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/juce_lv2_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/README.md"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd_config.h"
    "../../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord_config.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslcontextinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipsleditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslgainreduction.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslhostcommands.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslviewembedding.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/ipslviewscaling.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/pslauextensions.h"
    "../../../../../modules/juce_audio_processors/format_types/pslextensions/pslvst2extensions.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/baseiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/classfactoryhelpers.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fbuffer.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fbuffer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fcommandline.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fdebug.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fdebug.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fobject.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fobject.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstreamer.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstreamer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstring.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/fstring.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/updatehandler.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/source/updatehandler.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/thread/include/flock.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/thread/source/flock.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/base/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/conststringtable.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/conststringtable.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/coreiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/falignpop.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/falignpush.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fplatform.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fstrdefs.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ftypes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknown.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknown.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/funknownimpl.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/futils.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/fvariant.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ibstream.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/icloneable.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ipersistent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ipluginbase.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/iplugincompatibility.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/istringresult.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/iupdatehandler.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/smartpointer.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/typesizecheck.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ustring.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/base/ustring.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/gui/iplugview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/gui/iplugviewcontentscalesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstattributes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstaudioprocessor.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstautomationstate.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstchannelcontextinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstcomponent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstcontextmenu.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstdataexchange.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsteditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstevents.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsthostapplication.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstinterappaudio.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmessage.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmidicontrollers.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstmidilearn.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstnoteexpression.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstparameterchanges.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstparameterfunctionname.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstphysicalui.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstpluginterfacesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstplugview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstprefetchablesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstprocesscontext.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstremapparamid.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstrepresentation.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivsttestplugprovider.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/ivstunits.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vstpshpack4.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vstspeaker.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/vst/vsttypes.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/pluginterfaces/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/samples/vst-utilities/moduleinfotool/source/main.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/commonstringconvert.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/commonstringconvert.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/memorystream.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/memorystream.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/pluginview.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/pluginview.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/readfile.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/common/readfile.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/hostclasses.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/hostclasses.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_linux.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_mac.mm"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/module_win32.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/pluginterfacesupport.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/hosting/pluginterfacesupport.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/json.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/jsoncxx.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfo.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfocreator.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfocreator.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfoparser.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/moduleinfoparser.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/moduleinfo/ReadMe.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/optional.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/stringconvert.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/stringconvert.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/uid.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/vst2persistence.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/utility/vst2persistence.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstbus.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstbus.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponent.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponent.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponentbase.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstcomponentbase.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vsteditcontroller.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vsteditcontroller.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstinitiids.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstparameters.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstparameters.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstpresetfile.cpp"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/source/vst/vstpresetfile.h"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/public.sdk/README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/JUCE_README.md"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/LICENSE.txt"
    "../../../../../modules/juce_audio_processors/format_types/VST3_SDK/README.md"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARACommon.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARACommon.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARAHosting.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_ARAHosting.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AU_Shared.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AudioUnitPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_AudioUnitPluginFormat.mm"
    "../../../../../modules/juce_audio_processors/format_types/juce_LADSPAPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LADSPAPluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LegacyAudioParameter.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2Common.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2PluginFormat_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2Resources.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_LV2SupportLibs.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3Common.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3Headers.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VST3PluginFormat_test.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTCommon.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTMidiEventList.h"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTPluginFormat.cpp"
    "../../../../../modules/juce_audio_processors/format_types/juce_VSTPluginFormat.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioPluginInstance.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioPluginInstance.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorEditorHostContext.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorGraph.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorGraph.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorListener.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameter.h"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameterGroup.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_AudioProcessorParameterGroup.h"
    "../../../../../modules/juce_audio_processors/processors/juce_GenericAudioProcessorEditor.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_GenericAudioProcessorEditor.h"
    "../../../../../modules/juce_audio_processors/processors/juce_HostedAudioProcessorParameter.h"
    "../../../../../modules/juce_audio_processors/processors/juce_PluginDescription.cpp"
    "../../../../../modules/juce_audio_processors/processors/juce_PluginDescription.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_KnownPluginList.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_KnownPluginList.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginDirectoryScanner.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginDirectoryScanner.h"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginListComponent.cpp"
    "../../../../../modules/juce_audio_processors/scanning/juce_PluginListComponent.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARA_utils.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARA_utils.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADebug.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentController.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentController.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARADocumentControllerCommon.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAModelObjects.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAModelObjects.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAPlugInInstanceRoles.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_ARAPlugInInstanceRoles.h"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_AudioProcessor_ARAExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/ARA/juce_AudioProcessor_ARAExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AAXClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AAXClientExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterBool.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterBool.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterChoice.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterChoice.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterFloat.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterFloat.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterInt.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioParameterInt.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorParameterWithID.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorParameterWithID.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorValueTreeState.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_AudioProcessorValueTreeState.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_ExtensionsVisitor.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_FlagCache.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_ParameterAttachments.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_ParameterAttachments.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_PluginHostType.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_PluginHostType.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_RangedAudioParameter.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_RangedAudioParameter.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST2ClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST2ClientExtensions.h"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST3ClientExtensions.cpp"
    "../../../../../modules/juce_audio_processors/utilities/juce_VST3ClientExtensions.h"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.mm"
    "../../../../../modules/juce_audio_processors/juce_audio_processors_ara.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors_lv2_libs.cpp"
    "../../../../../modules/juce_audio_processors/juce_audio_processors.h"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDBurner.h"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDReader.cpp"
    "../../../../../modules/juce_audio_utils/audio_cd/juce_AudioCDReader.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioAppComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioAppComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioDeviceSelectorComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioDeviceSelectorComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnail.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnail.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailBase.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailCache.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioThumbnailCache.h"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioVisualiserComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_AudioVisualiserComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_BluetoothMidiDevicePairingDialogue.h"
    "../../../../../modules/juce_audio_utils/gui/juce_KeyboardComponentBase.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_KeyboardComponentBase.h"
    "../../../../../modules/juce_audio_utils/gui/juce_MidiKeyboardComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_MidiKeyboardComponent.h"
    "../../../../../modules/juce_audio_utils/gui/juce_MPEKeyboardComponent.cpp"
    "../../../../../modules/juce_audio_utils/gui/juce_MPEKeyboardComponent.h"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDBurner_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDBurner_windows.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_linux.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_AudioCDReader_windows.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_android.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_ios.mm"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_linux.cpp"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_mac.mm"
    "../../../../../modules/juce_audio_utils/native/juce_BluetoothMidiDevicePairingDialogue_windows.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_AudioProcessorPlayer.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_AudioProcessorPlayer.h"
    "../../../../../modules/juce_audio_utils/players/juce_SoundPlayer.cpp"
    "../../../../../modules/juce_audio_utils/players/juce_SoundPlayer.h"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.cpp"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.mm"
    "../../../../../modules/juce_audio_utils/juce_audio_utils.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2ChainShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2ChainShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2CircleShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2CircleShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2EdgeShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2EdgeShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2PolygonShape.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2PolygonShape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/Shapes/b2Shape.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2BroadPhase.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2BroadPhase.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollideCircle.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollideEdge.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2CollidePolygon.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Collision.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Collision.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Distance.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2Distance.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2DynamicTree.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2DynamicTree.h"
    "../../../../../modules/juce_box2d/box2d/Collision/b2TimeOfImpact.cpp"
    "../../../../../modules/juce_box2d/box2d/Collision/b2TimeOfImpact.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2BlockAllocator.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2BlockAllocator.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Draw.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Draw.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2GrowableStack.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Math.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Math.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Settings.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Settings.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2StackAllocator.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2StackAllocator.h"
    "../../../../../modules/juce_box2d/box2d/Common/b2Timer.cpp"
    "../../../../../modules/juce_box2d/box2d/Common/b2Timer.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndPolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ChainAndPolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2CircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2CircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2Contact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2Contact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ContactSolver.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2ContactSolver.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndPolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2EdgeAndPolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonAndCircleContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonAndCircleContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonContact.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Contacts/b2PolygonContact.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2DistanceJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2DistanceJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2FrictionJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2FrictionJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2GearJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2GearJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2Joint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2Joint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2MouseJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2MouseJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PrismaticJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PrismaticJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PulleyJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2PulleyJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RevoluteJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RevoluteJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RopeJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2RopeJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WeldJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WeldJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WheelJoint.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/Joints/b2WheelJoint.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Body.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Body.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2ContactManager.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2ContactManager.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Fixture.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Fixture.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Island.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2Island.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2TimeStep.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2World.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2World.h"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2WorldCallbacks.cpp"
    "../../../../../modules/juce_box2d/box2d/Dynamics/b2WorldCallbacks.h"
    "../../../../../modules/juce_box2d/box2d/Rope/b2Rope.cpp"
    "../../../../../modules/juce_box2d/box2d/Rope/b2Rope.h"
    "../../../../../modules/juce_box2d/box2d/Box2D.h"
    "../../../../../modules/juce_box2d/box2d/README.txt"
    "../../../../../modules/juce_box2d/utils/juce_Box2DRenderer.cpp"
    "../../../../../modules/juce_box2d/utils/juce_Box2DRenderer.h"
    "../../../../../modules/juce_box2d/juce_box2d.cpp"
    "../../../../../modules/juce_box2d/juce_box2d.h"
    "../../../../../modules/juce_core/containers/juce_AbstractFifo.cpp"
    "../../../../../modules/juce_core/containers/juce_AbstractFifo.h"
    "../../../../../modules/juce_core/containers/juce_Array.h"
    "../../../../../modules/juce_core/containers/juce_ArrayAllocationBase.h"
    "../../../../../modules/juce_core/containers/juce_ArrayBase.cpp"
    "../../../../../modules/juce_core/containers/juce_ArrayBase.h"
    "../../../../../modules/juce_core/containers/juce_DynamicObject.cpp"
    "../../../../../modules/juce_core/containers/juce_DynamicObject.h"
    "../../../../../modules/juce_core/containers/juce_ElementComparator.h"
    "../../../../../modules/juce_core/containers/juce_Enumerate.h"
    "../../../../../modules/juce_core/containers/juce_Enumerate_test.cpp"
    "../../../../../modules/juce_core/containers/juce_FixedSizeFunction.h"
    "../../../../../modules/juce_core/containers/juce_FixedSizeFunction_test.cpp"
    "../../../../../modules/juce_core/containers/juce_HashMap.h"
    "../../../../../modules/juce_core/containers/juce_HashMap_test.cpp"
    "../../../../../modules/juce_core/containers/juce_LinkedListPointer.h"
    "../../../../../modules/juce_core/containers/juce_ListenerList.h"
    "../../../../../modules/juce_core/containers/juce_ListenerList_test.cpp"
    "../../../../../modules/juce_core/containers/juce_NamedValueSet.cpp"
    "../../../../../modules/juce_core/containers/juce_NamedValueSet.h"
    "../../../../../modules/juce_core/containers/juce_Optional.h"
    "../../../../../modules/juce_core/containers/juce_Optional_test.cpp"
    "../../../../../modules/juce_core/containers/juce_OwnedArray.cpp"
    "../../../../../modules/juce_core/containers/juce_OwnedArray.h"
    "../../../../../modules/juce_core/containers/juce_PropertySet.cpp"
    "../../../../../modules/juce_core/containers/juce_PropertySet.h"
    "../../../../../modules/juce_core/containers/juce_ReferenceCountedArray.cpp"
    "../../../../../modules/juce_core/containers/juce_ReferenceCountedArray.h"
    "../../../../../modules/juce_core/containers/juce_ScopedValueSetter.h"
    "../../../../../modules/juce_core/containers/juce_SingleThreadedAbstractFifo.h"
    "../../../../../modules/juce_core/containers/juce_SortedSet.h"
    "../../../../../modules/juce_core/containers/juce_Span.h"
    "../../../../../modules/juce_core/containers/juce_SparseSet.cpp"
    "../../../../../modules/juce_core/containers/juce_SparseSet.h"
    "../../../../../modules/juce_core/containers/juce_Variant.cpp"
    "../../../../../modules/juce_core/containers/juce_Variant.h"
    "../../../../../modules/juce_core/detail/juce_CallbackListenerList.h"
    "../../../../../modules/juce_core/files/juce_AndroidDocument.h"
    "../../../../../modules/juce_core/files/juce_common_MimeTypes.cpp"
    "../../../../../modules/juce_core/files/juce_common_MimeTypes.h"
    "../../../../../modules/juce_core/files/juce_DirectoryIterator.cpp"
    "../../../../../modules/juce_core/files/juce_DirectoryIterator.h"
    "../../../../../modules/juce_core/files/juce_File.cpp"
    "../../../../../modules/juce_core/files/juce_File.h"
    "../../../../../modules/juce_core/files/juce_FileFilter.cpp"
    "../../../../../modules/juce_core/files/juce_FileFilter.h"
    "../../../../../modules/juce_core/files/juce_FileInputStream.cpp"
    "../../../../../modules/juce_core/files/juce_FileInputStream.h"
    "../../../../../modules/juce_core/files/juce_FileOutputStream.cpp"
    "../../../../../modules/juce_core/files/juce_FileOutputStream.h"
    "../../../../../modules/juce_core/files/juce_FileSearchPath.cpp"
    "../../../../../modules/juce_core/files/juce_FileSearchPath.h"
    "../../../../../modules/juce_core/files/juce_MemoryMappedFile.h"
    "../../../../../modules/juce_core/files/juce_RangedDirectoryIterator.cpp"
    "../../../../../modules/juce_core/files/juce_RangedDirectoryIterator.h"
    "../../../../../modules/juce_core/files/juce_TemporaryFile.cpp"
    "../../../../../modules/juce_core/files/juce_TemporaryFile.h"
    "../../../../../modules/juce_core/files/juce_WildcardFileFilter.cpp"
    "../../../../../modules/juce_core/files/juce_WildcardFileFilter.h"
    "../../../../../modules/juce_core/json/juce_JSON.cpp"
    "../../../../../modules/juce_core/json/juce_JSON.h"
    "../../../../../modules/juce_core/json/juce_JSONSerialisation.h"
    "../../../../../modules/juce_core/json/juce_JSONSerialisation_test.cpp"
    "../../../../../modules/juce_core/json/juce_JSONUtils.cpp"
    "../../../../../modules/juce_core/json/juce_JSONUtils.h"
    "../../../../../modules/juce_core/logging/juce_FileLogger.cpp"
    "../../../../../modules/juce_core/logging/juce_FileLogger.h"
    "../../../../../modules/juce_core/logging/juce_Logger.cpp"
    "../../../../../modules/juce_core/logging/juce_Logger.h"
    "../../../../../modules/juce_core/maths/juce_BigInteger.cpp"
    "../../../../../modules/juce_core/maths/juce_BigInteger.h"
    "../../../../../modules/juce_core/maths/juce_Expression.cpp"
    "../../../../../modules/juce_core/maths/juce_Expression.h"
    "../../../../../modules/juce_core/maths/juce_MathsFunctions.h"
    "../../../../../modules/juce_core/maths/juce_MathsFunctions_test.cpp"
    "../../../../../modules/juce_core/maths/juce_NormalisableRange.h"
    "../../../../../modules/juce_core/maths/juce_Random.cpp"
    "../../../../../modules/juce_core/maths/juce_Random.h"
    "../../../../../modules/juce_core/maths/juce_Range.h"
    "../../../../../modules/juce_core/maths/juce_StatisticsAccumulator.h"
    "../../../../../modules/juce_core/memory/juce_AllocationHooks.cpp"
    "../../../../../modules/juce_core/memory/juce_AllocationHooks.h"
    "../../../../../modules/juce_core/memory/juce_Atomic.h"
    "../../../../../modules/juce_core/memory/juce_ByteOrder.h"
    "../../../../../modules/juce_core/memory/juce_ContainerDeletePolicy.h"
    "../../../../../modules/juce_core/memory/juce_CopyableHeapBlock.h"
    "../../../../../modules/juce_core/memory/juce_HeapBlock.h"
    "../../../../../modules/juce_core/memory/juce_HeavyweightLeakedObjectDetector.h"
    "../../../../../modules/juce_core/memory/juce_LeakedObjectDetector.h"
    "../../../../../modules/juce_core/memory/juce_Memory.h"
    "../../../../../modules/juce_core/memory/juce_MemoryBlock.cpp"
    "../../../../../modules/juce_core/memory/juce_MemoryBlock.h"
    "../../../../../modules/juce_core/memory/juce_OptionalScopedPointer.h"
    "../../../../../modules/juce_core/memory/juce_ReferenceCountedObject.h"
    "../../../../../modules/juce_core/memory/juce_Reservoir.h"
    "../../../../../modules/juce_core/memory/juce_ScopedPointer.h"
    "../../../../../modules/juce_core/memory/juce_SharedResourcePointer.h"
    "../../../../../modules/juce_core/memory/juce_SharedResourcePointer_test.cpp"
    "../../../../../modules/juce_core/memory/juce_Singleton.h"
    "../../../../../modules/juce_core/memory/juce_WeakReference.h"
    "../../../../../modules/juce_core/misc/juce_ConsoleApplication.cpp"
    "../../../../../modules/juce_core/misc/juce_ConsoleApplication.h"
    "../../../../../modules/juce_core/misc/juce_EnumHelpers.h"
    "../../../../../modules/juce_core/misc/juce_EnumHelpers_test.cpp"
    "../../../../../modules/juce_core/misc/juce_Functional.h"
    "../../../../../modules/juce_core/misc/juce_OptionsHelpers.h"
    "../../../../../modules/juce_core/misc/juce_Result.cpp"
    "../../../../../modules/juce_core/misc/juce_Result.h"
    "../../../../../modules/juce_core/misc/juce_RuntimePermissions.cpp"
    "../../../../../modules/juce_core/misc/juce_RuntimePermissions.h"
    "../../../../../modules/juce_core/misc/juce_ScopeGuard.cpp"
    "../../../../../modules/juce_core/misc/juce_ScopeGuard.h"
    "../../../../../modules/juce_core/misc/juce_Uuid.cpp"
    "../../../../../modules/juce_core/misc/juce_Uuid.h"
    "../../../../../modules/juce_core/misc/juce_WindowsRegistry.h"
    "../../../../../modules/juce_core/native/java/README.txt"
    "../../../../../modules/juce_core/native/juce_AndroidDocument_android.cpp"
    "../../../../../modules/juce_core/native/juce_BasicNativeHeaders.h"
    "../../../../../modules/juce_core/native/juce_CFHelpers_mac.h"
    "../../../../../modules/juce_core/native/juce_CommonFile_linux.cpp"
    "../../../../../modules/juce_core/native/juce_ComSmartPtr_windows.h"
    "../../../../../modules/juce_core/native/juce_Files_android.cpp"
    "../../../../../modules/juce_core/native/juce_Files_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Files_mac.mm"
    "../../../../../modules/juce_core/native/juce_Files_windows.cpp"
    "../../../../../modules/juce_core/native/juce_IPAddress_posix.h"
    "../../../../../modules/juce_core/native/juce_JNIHelpers_android.cpp"
    "../../../../../modules/juce_core/native/juce_JNIHelpers_android.h"
    "../../../../../modules/juce_core/native/juce_Misc_android.cpp"
    "../../../../../modules/juce_core/native/juce_NamedPipe_posix.cpp"
    "../../../../../modules/juce_core/native/juce_Network_android.cpp"
    "../../../../../modules/juce_core/native/juce_Network_curl.cpp"
    "../../../../../modules/juce_core/native/juce_Network_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Network_mac.mm"
    "../../../../../modules/juce_core/native/juce_Network_windows.cpp"
    "../../../../../modules/juce_core/native/juce_ObjCHelpers_mac.h"
    "../../../../../modules/juce_core/native/juce_ObjCHelpers_mac_test.mm"
    "../../../../../modules/juce_core/native/juce_PlatformTimer_generic.cpp"
    "../../../../../modules/juce_core/native/juce_PlatformTimer_windows.cpp"
    "../../../../../modules/juce_core/native/juce_PlatformTimerListener.h"
    "../../../../../modules/juce_core/native/juce_Process_mac.mm"
    "../../../../../modules/juce_core/native/juce_Registry_windows.cpp"
    "../../../../../modules/juce_core/native/juce_RuntimePermissions_android.cpp"
    "../../../../../modules/juce_core/native/juce_SharedCode_intel.h"
    "../../../../../modules/juce_core/native/juce_SharedCode_posix.h"
    "../../../../../modules/juce_core/native/juce_Strings_mac.mm"
    "../../../../../modules/juce_core/native/juce_SystemStats_android.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_linux.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_mac.mm"
    "../../../../../modules/juce_core/native/juce_SystemStats_wasm.cpp"
    "../../../../../modules/juce_core/native/juce_SystemStats_windows.cpp"
    "../../../../../modules/juce_core/native/juce_ThreadPriorities_native.h"
    "../../../../../modules/juce_core/native/juce_Threads_android.cpp"
    "../../../../../modules/juce_core/native/juce_Threads_linux.cpp"
    "../../../../../modules/juce_core/native/juce_Threads_mac.mm"
    "../../../../../modules/juce_core/native/juce_Threads_windows.cpp"
    "../../../../../modules/juce_core/network/juce_IPAddress.cpp"
    "../../../../../modules/juce_core/network/juce_IPAddress.h"
    "../../../../../modules/juce_core/network/juce_MACAddress.cpp"
    "../../../../../modules/juce_core/network/juce_MACAddress.h"
    "../../../../../modules/juce_core/network/juce_NamedPipe.cpp"
    "../../../../../modules/juce_core/network/juce_NamedPipe.h"
    "../../../../../modules/juce_core/network/juce_Socket.cpp"
    "../../../../../modules/juce_core/network/juce_Socket.h"
    "../../../../../modules/juce_core/network/juce_URL.cpp"
    "../../../../../modules/juce_core/network/juce_URL.h"
    "../../../../../modules/juce_core/network/juce_WebInputStream.cpp"
    "../../../../../modules/juce_core/network/juce_WebInputStream.h"
    "../../../../../modules/juce_core/serialisation/juce_Serialisation.h"
    "../../../../../modules/juce_core/streams/juce_AndroidDocumentInputSource.h"
    "../../../../../modules/juce_core/streams/juce_BufferedInputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_BufferedInputStream.h"
    "../../../../../modules/juce_core/streams/juce_FileInputSource.cpp"
    "../../../../../modules/juce_core/streams/juce_FileInputSource.h"
    "../../../../../modules/juce_core/streams/juce_InputSource.h"
    "../../../../../modules/juce_core/streams/juce_InputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_InputStream.h"
    "../../../../../modules/juce_core/streams/juce_MemoryInputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_MemoryInputStream.h"
    "../../../../../modules/juce_core/streams/juce_MemoryOutputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_MemoryOutputStream.h"
    "../../../../../modules/juce_core/streams/juce_OutputStream.cpp"
    "../../../../../modules/juce_core/streams/juce_OutputStream.h"
    "../../../../../modules/juce_core/streams/juce_SubregionStream.cpp"
    "../../../../../modules/juce_core/streams/juce_SubregionStream.h"
    "../../../../../modules/juce_core/streams/juce_URLInputSource.cpp"
    "../../../../../modules/juce_core/streams/juce_URLInputSource.h"
    "../../../../../modules/juce_core/system/juce_CompilerSupport.h"
    "../../../../../modules/juce_core/system/juce_CompilerWarnings.h"
    "../../../../../modules/juce_core/system/juce_PlatformDefs.h"
    "../../../../../modules/juce_core/system/juce_StandardHeader.h"
    "../../../../../modules/juce_core/system/juce_SystemStats.cpp"
    "../../../../../modules/juce_core/system/juce_SystemStats.h"
    "../../../../../modules/juce_core/system/juce_TargetPlatform.h"
    "../../../../../modules/juce_core/text/juce_Base64.cpp"
    "../../../../../modules/juce_core/text/juce_Base64.h"
    "../../../../../modules/juce_core/text/juce_CharacterFunctions.cpp"
    "../../../../../modules/juce_core/text/juce_CharacterFunctions.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_ASCII.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF8.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF8_test.cpp"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF16.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF16_test.cpp"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF32.h"
    "../../../../../modules/juce_core/text/juce_CharPointer_UTF32_test.cpp"
    "../../../../../modules/juce_core/text/juce_Identifier.cpp"
    "../../../../../modules/juce_core/text/juce_Identifier.h"
    "../../../../../modules/juce_core/text/juce_LocalisedStrings.cpp"
    "../../../../../modules/juce_core/text/juce_LocalisedStrings.h"
    "../../../../../modules/juce_core/text/juce_NewLine.h"
    "../../../../../modules/juce_core/text/juce_String.cpp"
    "../../../../../modules/juce_core/text/juce_String.h"
    "../../../../../modules/juce_core/text/juce_StringArray.cpp"
    "../../../../../modules/juce_core/text/juce_StringArray.h"
    "../../../../../modules/juce_core/text/juce_StringPairArray.cpp"
    "../../../../../modules/juce_core/text/juce_StringPairArray.h"
    "../../../../../modules/juce_core/text/juce_StringPool.cpp"
    "../../../../../modules/juce_core/text/juce_StringPool.h"
    "../../../../../modules/juce_core/text/juce_StringRef.h"
    "../../../../../modules/juce_core/text/juce_TextDiff.cpp"
    "../../../../../modules/juce_core/text/juce_TextDiff.h"
    "../../../../../modules/juce_core/threads/juce_ChildProcess.cpp"
    "../../../../../modules/juce_core/threads/juce_ChildProcess.h"
    "../../../../../modules/juce_core/threads/juce_CriticalSection.h"
    "../../../../../modules/juce_core/threads/juce_DynamicLibrary.h"
    "../../../../../modules/juce_core/threads/juce_HighResolutionTimer.cpp"
    "../../../../../modules/juce_core/threads/juce_HighResolutionTimer.h"
    "../../../../../modules/juce_core/threads/juce_InterProcessLock.h"
    "../../../../../modules/juce_core/threads/juce_Process.h"
    "../../../../../modules/juce_core/threads/juce_ReadWriteLock.cpp"
    "../../../../../modules/juce_core/threads/juce_ReadWriteLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedReadLock.h"
    "../../../../../modules/juce_core/threads/juce_ScopedWriteLock.h"
    "../../../../../modules/juce_core/threads/juce_SpinLock.h"
    "../../../../../modules/juce_core/threads/juce_Thread.cpp"
    "../../../../../modules/juce_core/threads/juce_Thread.h"
    "../../../../../modules/juce_core/threads/juce_ThreadLocalValue.h"
    "../../../../../modules/juce_core/threads/juce_ThreadPool.cpp"
    "../../../../../modules/juce_core/threads/juce_ThreadPool.h"
    "../../../../../modules/juce_core/threads/juce_TimeSliceThread.cpp"
    "../../../../../modules/juce_core/threads/juce_TimeSliceThread.h"
    "../../../../../modules/juce_core/threads/juce_WaitableEvent.cpp"
    "../../../../../modules/juce_core/threads/juce_WaitableEvent.h"
    "../../../../../modules/juce_core/time/juce_PerformanceCounter.cpp"
    "../../../../../modules/juce_core/time/juce_PerformanceCounter.h"
    "../../../../../modules/juce_core/time/juce_RelativeTime.cpp"
    "../../../../../modules/juce_core/time/juce_RelativeTime.h"
    "../../../../../modules/juce_core/time/juce_Time.cpp"
    "../../../../../modules/juce_core/time/juce_Time.h"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTest.cpp"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTest.h"
    "../../../../../modules/juce_core/unit_tests/juce_UnitTestCategories.h"
    "../../../../../modules/juce_core/xml/juce_XmlDocument.cpp"
    "../../../../../modules/juce_core/xml/juce_XmlDocument.h"
    "../../../../../modules/juce_core/xml/juce_XmlElement.cpp"
    "../../../../../modules/juce_core/xml/juce_XmlElement.h"
    "../../../../../modules/juce_core/zip/zlib/adler32.c"
    "../../../../../modules/juce_core/zip/zlib/compress.c"
    "../../../../../modules/juce_core/zip/zlib/crc32.c"
    "../../../../../modules/juce_core/zip/zlib/crc32.h"
    "../../../../../modules/juce_core/zip/zlib/deflate.c"
    "../../../../../modules/juce_core/zip/zlib/deflate.h"
    "../../../../../modules/juce_core/zip/zlib/infback.c"
    "../../../../../modules/juce_core/zip/zlib/inffast.c"
    "../../../../../modules/juce_core/zip/zlib/inffast.h"
    "../../../../../modules/juce_core/zip/zlib/inffixed.h"
    "../../../../../modules/juce_core/zip/zlib/inflate.c"
    "../../../../../modules/juce_core/zip/zlib/inflate.h"
    "../../../../../modules/juce_core/zip/zlib/inftrees.c"
    "../../../../../modules/juce_core/zip/zlib/inftrees.h"
    "../../../../../modules/juce_core/zip/zlib/trees.c"
    "../../../../../modules/juce_core/zip/zlib/trees.h"
    "../../../../../modules/juce_core/zip/zlib/uncompr.c"
    "../../../../../modules/juce_core/zip/zlib/zconf.h"
    "../../../../../modules/juce_core/zip/zlib/zconf.in.h"
    "../../../../../modules/juce_core/zip/zlib/zlib.h"
    "../../../../../modules/juce_core/zip/zlib/zutil.c"
    "../../../../../modules/juce_core/zip/zlib/zutil.h"
    "../../../../../modules/juce_core/zip/juce_GZIPCompressorOutputStream.cpp"
    "../../../../../modules/juce_core/zip/juce_GZIPCompressorOutputStream.h"
    "../../../../../modules/juce_core/zip/juce_GZIPDecompressorInputStream.cpp"
    "../../../../../modules/juce_core/zip/juce_GZIPDecompressorInputStream.h"
    "../../../../../modules/juce_core/zip/juce_ZipFile.cpp"
    "../../../../../modules/juce_core/zip/juce_ZipFile.h"
    "../../../../../modules/juce_core/juce_core.cpp"
    "../../../../../modules/juce_core/juce_core.mm"
    "../../../../../modules/juce_core/juce_core_CompilationTime.cpp"
    "../../../../../modules/juce_core/juce_core.h"
    "../../../../../modules/juce_cryptography/encryption/juce_BlowFish.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_BlowFish.h"
    "../../../../../modules/juce_cryptography/encryption/juce_Primes.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_Primes.h"
    "../../../../../modules/juce_cryptography/encryption/juce_RSAKey.cpp"
    "../../../../../modules/juce_cryptography/encryption/juce_RSAKey.h"
    "../../../../../modules/juce_cryptography/hashing/juce_MD5.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_MD5.h"
    "../../../../../modules/juce_cryptography/hashing/juce_SHA256.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_SHA256.h"
    "../../../../../modules/juce_cryptography/hashing/juce_Whirlpool.cpp"
    "../../../../../modules/juce_cryptography/hashing/juce_Whirlpool.h"
    "../../../../../modules/juce_cryptography/juce_cryptography.cpp"
    "../../../../../modules/juce_cryptography/juce_cryptography.mm"
    "../../../../../modules/juce_cryptography/juce_cryptography.h"
    "../../../../../modules/juce_data_structures/app_properties/juce_ApplicationProperties.cpp"
    "../../../../../modules/juce_data_structures/app_properties/juce_ApplicationProperties.h"
    "../../../../../modules/juce_data_structures/app_properties/juce_PropertiesFile.cpp"
    "../../../../../modules/juce_data_structures/app_properties/juce_PropertiesFile.h"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoableAction.cpp"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoableAction.h"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoManager.cpp"
    "../../../../../modules/juce_data_structures/undomanager/juce_UndoManager.h"
    "../../../../../modules/juce_data_structures/values/juce_CachedValue.cpp"
    "../../../../../modules/juce_data_structures/values/juce_CachedValue.h"
    "../../../../../modules/juce_data_structures/values/juce_Value.cpp"
    "../../../../../modules/juce_data_structures/values/juce_Value.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTree.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTree.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreePropertyWithDefault.h"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreePropertyWithDefault_test.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreeSynchroniser.cpp"
    "../../../../../modules/juce_data_structures/values/juce_ValueTreeSynchroniser.h"
    "../../../../../modules/juce_data_structures/juce_data_structures.cpp"
    "../../../../../modules/juce_data_structures/juce_data_structures.mm"
    "../../../../../modules/juce_data_structures/juce_data_structures.h"
    "../../../../../modules/juce_dsp/containers/juce_AudioBlock.h"
    "../../../../../modules/juce_dsp/containers/juce_AudioBlock_test.cpp"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister.h"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister_Impl.h"
    "../../../../../modules/juce_dsp/containers/juce_SIMDRegister_test.cpp"
    "../../../../../modules/juce_dsp/filter_design/juce_FilterDesign.cpp"
    "../../../../../modules/juce_dsp/filter_design/juce_FilterDesign.h"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution.h"
    "../../../../../modules/juce_dsp/frequency/juce_Convolution_test.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_FFT.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_FFT.h"
    "../../../../../modules/juce_dsp/frequency/juce_FFT_test.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Windowing.cpp"
    "../../../../../modules/juce_dsp/frequency/juce_Windowing.h"
    "../../../../../modules/juce_dsp/maths/juce_FastMathApproximations.h"
    "../../../../../modules/juce_dsp/maths/juce_LogRampedValue.h"
    "../../../../../modules/juce_dsp/maths/juce_LogRampedValue_test.cpp"
    "../../../../../modules/juce_dsp/maths/juce_LookupTable.cpp"
    "../../../../../modules/juce_dsp/maths/juce_LookupTable.h"
    "../../../../../modules/juce_dsp/maths/juce_Matrix.cpp"
    "../../../../../modules/juce_dsp/maths/juce_Matrix.h"
    "../../../../../modules/juce_dsp/maths/juce_Matrix_test.cpp"
    "../../../../../modules/juce_dsp/maths/juce_Phase.h"
    "../../../../../modules/juce_dsp/maths/juce_Polynomial.h"
    "../../../../../modules/juce_dsp/maths/juce_SpecialFunctions.cpp"
    "../../../../../modules/juce_dsp/maths/juce_SpecialFunctions.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_avx.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_avx.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_fallback.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_neon.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_neon.h"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_sse.cpp"
    "../../../../../modules/juce_dsp/native/juce_SIMDNativeOps_sse.h"
    "../../../../../modules/juce_dsp/processors/juce_BallisticsFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_BallisticsFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_DelayLine.cpp"
    "../../../../../modules/juce_dsp/processors/juce_DelayLine.h"
    "../../../../../modules/juce_dsp/processors/juce_DryWetMixer.cpp"
    "../../../../../modules/juce_dsp/processors/juce_DryWetMixer.h"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_FIRFilter_test.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FirstOrderTPTFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_FirstOrderTPTFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_IIRFilter_Impl.h"
    "../../../../../modules/juce_dsp/processors/juce_LinkwitzRileyFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_LinkwitzRileyFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_Oversampling.cpp"
    "../../../../../modules/juce_dsp/processors/juce_Oversampling.h"
    "../../../../../modules/juce_dsp/processors/juce_Panner.cpp"
    "../../../../../modules/juce_dsp/processors/juce_Panner.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessContext.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorChain.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorChain_test.cpp"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorDuplicator.h"
    "../../../../../modules/juce_dsp/processors/juce_ProcessorWrapper.h"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableFilter.h"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableTPTFilter.cpp"
    "../../../../../modules/juce_dsp/processors/juce_StateVariableTPTFilter.h"
    "../../../../../modules/juce_dsp/widgets/juce_Bias.h"
    "../../../../../modules/juce_dsp/widgets/juce_Chorus.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Chorus.h"
    "../../../../../modules/juce_dsp/widgets/juce_Compressor.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Compressor.h"
    "../../../../../modules/juce_dsp/widgets/juce_Gain.h"
    "../../../../../modules/juce_dsp/widgets/juce_LadderFilter.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_LadderFilter.h"
    "../../../../../modules/juce_dsp/widgets/juce_Limiter.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Limiter.h"
    "../../../../../modules/juce_dsp/widgets/juce_NoiseGate.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_NoiseGate.h"
    "../../../../../modules/juce_dsp/widgets/juce_Oscillator.h"
    "../../../../../modules/juce_dsp/widgets/juce_Phaser.cpp"
    "../../../../../modules/juce_dsp/widgets/juce_Phaser.h"
    "../../../../../modules/juce_dsp/widgets/juce_Reverb.h"
    "../../../../../modules/juce_dsp/widgets/juce_WaveShaper.h"
    "../../../../../modules/juce_dsp/juce_dsp.cpp"
    "../../../../../modules/juce_dsp/juce_dsp.mm"
    "../../../../../modules/juce_dsp/juce_dsp.h"
    "../../../../../modules/juce_events/broadcasters/juce_ActionBroadcaster.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_ActionBroadcaster.h"
    "../../../../../modules/juce_events/broadcasters/juce_ActionListener.h"
    "../../../../../modules/juce_events/broadcasters/juce_AsyncUpdater.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_AsyncUpdater.h"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeBroadcaster.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeBroadcaster.h"
    "../../../../../modules/juce_events/broadcasters/juce_ChangeListener.h"
    "../../../../../modules/juce_events/broadcasters/juce_LockingAsyncUpdater.cpp"
    "../../../../../modules/juce_events/broadcasters/juce_LockingAsyncUpdater.h"
    "../../../../../modules/juce_events/interprocess/juce_ChildProcessManager.cpp"
    "../../../../../modules/juce_events/interprocess/juce_ChildProcessManager.h"
    "../../../../../modules/juce_events/interprocess/juce_ConnectedChildProcess.cpp"
    "../../../../../modules/juce_events/interprocess/juce_ConnectedChildProcess.h"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnection.cpp"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnection.h"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnectionServer.cpp"
    "../../../../../modules/juce_events/interprocess/juce_InterprocessConnectionServer.h"
    "../../../../../modules/juce_events/interprocess/juce_NetworkServiceDiscovery.cpp"
    "../../../../../modules/juce_events/interprocess/juce_NetworkServiceDiscovery.h"
    "../../../../../modules/juce_events/messages/juce_ApplicationBase.cpp"
    "../../../../../modules/juce_events/messages/juce_ApplicationBase.h"
    "../../../../../modules/juce_events/messages/juce_CallbackMessage.h"
    "../../../../../modules/juce_events/messages/juce_DeletedAtShutdown.cpp"
    "../../../../../modules/juce_events/messages/juce_DeletedAtShutdown.h"
    "../../../../../modules/juce_events/messages/juce_Initialisation.h"
    "../../../../../modules/juce_events/messages/juce_Message.h"
    "../../../../../modules/juce_events/messages/juce_MessageListener.cpp"
    "../../../../../modules/juce_events/messages/juce_MessageListener.h"
    "../../../../../modules/juce_events/messages/juce_MessageManager.cpp"
    "../../../../../modules/juce_events/messages/juce_MessageManager.h"
    "../../../../../modules/juce_events/messages/juce_MountedVolumeListChangeDetector.h"
    "../../../../../modules/juce_events/messages/juce_NotificationType.h"
    "../../../../../modules/juce_events/native/juce_EventLoop_linux.h"
    "../../../../../modules/juce_events/native/juce_EventLoopInternal_linux.h"
    "../../../../../modules/juce_events/native/juce_HiddenMessageWindow_windows.h"
    "../../../../../modules/juce_events/native/juce_MessageManager_ios.mm"
    "../../../../../modules/juce_events/native/juce_MessageManager_mac.mm"
    "../../../../../modules/juce_events/native/juce_MessageQueue_mac.h"
    "../../../../../modules/juce_events/native/juce_Messaging_android.cpp"
    "../../../../../modules/juce_events/native/juce_Messaging_linux.cpp"
    "../../../../../modules/juce_events/native/juce_Messaging_windows.cpp"
    "../../../../../modules/juce_events/native/juce_RunningInUnity.h"
    "../../../../../modules/juce_events/native/juce_ScopedLowPowerModeDisabler.cpp"
    "../../../../../modules/juce_events/native/juce_ScopedLowPowerModeDisabler.h"
    "../../../../../modules/juce_events/native/juce_WinRTWrapper_windows.cpp"
    "../../../../../modules/juce_events/native/juce_WinRTWrapper_windows.h"
    "../../../../../modules/juce_events/timers/juce_MultiTimer.cpp"
    "../../../../../modules/juce_events/timers/juce_MultiTimer.h"
    "../../../../../modules/juce_events/timers/juce_TimedCallback.h"
    "../../../../../modules/juce_events/timers/juce_Timer.cpp"
    "../../../../../modules/juce_events/timers/juce_Timer.h"
    "../../../../../modules/juce_events/juce_events.cpp"
    "../../../../../modules/juce_events/juce_events.mm"
    "../../../../../modules/juce_events/juce_events.h"
    "../../../../../modules/juce_graphics/colour/juce_Colour.cpp"
    "../../../../../modules/juce_graphics/colour/juce_Colour.h"
    "../../../../../modules/juce_graphics/colour/juce_ColourGradient.cpp"
    "../../../../../modules/juce_graphics/colour/juce_ColourGradient.h"
    "../../../../../modules/juce_graphics/colour/juce_Colours.cpp"
    "../../../../../modules/juce_graphics/colour/juce_Colours.h"
    "../../../../../modules/juce_graphics/colour/juce_FillType.cpp"
    "../../../../../modules/juce_graphics/colour/juce_FillType.h"
    "../../../../../modules/juce_graphics/colour/juce_PixelFormats.h"
    "../../../../../modules/juce_graphics/contexts/juce_GraphicsContext.cpp"
    "../../../../../modules/juce_graphics/contexts/juce_GraphicsContext.h"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsContext.h"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsSoftwareRenderer.cpp"
    "../../../../../modules/juce_graphics/contexts/juce_LowLevelGraphicsSoftwareRenderer.h"
    "../../../../../modules/juce_graphics/detail/juce_Ranges.cpp"
    "../../../../../modules/juce_graphics/detail/juce_Ranges.h"
    "../../../../../modules/juce_graphics/effects/juce_DropShadowEffect.cpp"
    "../../../../../modules/juce_graphics/effects/juce_DropShadowEffect.h"
    "../../../../../modules/juce_graphics/effects/juce_GlowEffect.cpp"
    "../../../../../modules/juce_graphics/effects/juce_GlowEffect.h"
    "../../../../../modules/juce_graphics/effects/juce_ImageEffectFilter.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/CBDT/CBDT.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/COLR/COLR.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/COLR/colrv1-closure.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/CPAL/CPAL.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/sbix/sbix.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Color/svg/svg.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/composite-iter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/CompositeGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/glyf-helpers.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/glyf.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/Glyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/GlyphHeader.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/loca.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/path-builder.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/SimpleGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/glyf/SubsetGlyph.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/Coverage.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/CoverageFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/CoverageFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/Common/RangeRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GDEF/GDEF.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/Anchor.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorFormat3.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/AnchorMatrix.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ChainContextPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/Common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ContextPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/CursivePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/CursivePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ExtensionPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/GPOS.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/LigatureArray.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkArray.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkBasePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkBasePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkLigPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkLigPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkMarkPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkMarkPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/MarkRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairPosFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PairValueRecord.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PosLookup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/PosLookupSubTable.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePosFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/SinglePosFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GPOS/ValueFormat.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/AlternateSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ChainContextSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ContextSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ExtensionSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/GSUB.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Ligature.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSet.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/LigatureSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/MultipleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/MultipleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ReverseChainSingleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/ReverseChainSingleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/Sequence.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubst.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubstFormat1.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SingleSubstFormat2.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SubstLookup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/GSUB/SubstLookupSubTable.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Layout/types.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/name/name.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/coord-setter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/VARC.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/OT/Var/VARC/VARC.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/failing-alloc.c"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/harfbuzz-subset.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/harfbuzz.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-ankr-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-bsln-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-feat-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-just-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-kerx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-morx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-opbd-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout-trak-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-layout.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-ltag-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-aat.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-algs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-array.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-atomic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bimap.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-page.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-set-invertible.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-bit-set.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-blob.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-json.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-text-glyphs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-deserialize-text-unicode.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-serialize.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer-verify.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-buffer.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cache.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-cs-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff-interp-dict-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff1-interp-cs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cff2-interp-cs.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-common.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-common.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-config.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-coretext.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-cplusplus.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-debug.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-deprecated.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-directwrite.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-directwrite.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-dispatch.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-draw.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face-builder.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-fallback-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-font.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft-colr.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ft.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gdi.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gdi.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-geometry.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-glib.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-glib.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject-structs.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject-structs.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-gobject.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-graphite2.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-graphite2.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-icu.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-icu.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-iter.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-kern.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-limits.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-machinery.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-meta.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ms-feature-ranges.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-multimap.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-mutex.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-null.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number-parser.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-number.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-object.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-open-file.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-open-type.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-std-str.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff1-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff2-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cff2-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-cmap-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-color.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-color.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-deprecated.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face-table-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-font.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-font.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-gasp-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-glyf-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hdmx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-head-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hhea-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-hmtx-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-kern-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-base-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gdef-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gpos-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gsub-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-gsubgpos.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout-jstf-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-layout.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-map.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-map.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-math.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-maxp-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-meta.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-metrics.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-language-static.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-language.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-name.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-os2-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-os2-unicode-ranges.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-macroman.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-table-v2subset.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-post-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-fallback.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-fallback.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-normalize.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape-normalize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shape.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-fallback.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-joining-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-pua.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic-win1256.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-arabic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-default.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-hangul.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-hebrew.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic-table.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-indic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-khmer-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-khmer.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-myanmar-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-myanmar.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-syllabic.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-syllabic.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-thai.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use-machine.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-use.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-vowel-constraints.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper-vowel-constraints.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-shaper.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-stat-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-tag-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-tag.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-avar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-cvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-fvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-gvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-hvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-mvar-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var-varc-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-var.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot-vorg-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ot.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-outline.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-outline.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint-extents.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint-extents.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-paint.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-pool.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-priority-queue.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-repacker.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-sanitize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-serialize.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set-digest.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-set.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape-plan.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shape.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper-impl.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-shaper.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-static.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-string-array.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-style.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-style.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-accelerator.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff-common.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff1.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-cff2.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-input.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-input.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-iup.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-iup.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-solver.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-instancer-solver.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan-member-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-plan.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-repacker.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset-repacker.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-subset.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ucd-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-ucd.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode-emoji-table.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-unicode.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-uniscribe.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-uniscribe.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-utf.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-vector.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-version.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-blob.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-buffer.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-common.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-face.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-font.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-list.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api-shape.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-api.hh"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb-wasm-shape.cc"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb.h"
    "../../../../../modules/juce_graphics/fonts/harfbuzz/hb.hh"
    "../../../../../modules/juce_graphics/fonts/juce_AttributedString.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_AttributedString.h"
    "../../../../../modules/juce_graphics/fonts/juce_Font.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_Font.h"
    "../../../../../modules/juce_graphics/fonts/juce_FontOptions.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_FontOptions.h"
    "../../../../../modules/juce_graphics/fonts/juce_FunctionPointerDestructor.h"
    "../../../../../modules/juce_graphics/fonts/juce_GlyphArrangement.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_GlyphArrangement.h"
    "../../../../../modules/juce_graphics/fonts/juce_JustifiedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_LruCache.h"
    "../../../../../modules/juce_graphics/fonts/juce_ShapedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_SimpleShapedText.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_TextLayout.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_TextLayout.h"
    "../../../../../modules/juce_graphics/fonts/juce_Typeface.cpp"
    "../../../../../modules/juce_graphics/fonts/juce_Typeface.h"
    "../../../../../modules/juce_graphics/fonts/juce_TypefaceFileCache.h"
    "../../../../../modules/juce_graphics/fonts/juce_TypefaceTestData.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_AffineTransform.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_AffineTransform.h"
    "../../../../../modules/juce_graphics/geometry/juce_BorderSize.h"
    "../../../../../modules/juce_graphics/geometry/juce_EdgeTable.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_EdgeTable.h"
    "../../../../../modules/juce_graphics/geometry/juce_Line.h"
    "../../../../../modules/juce_graphics/geometry/juce_Parallelogram.h"
    "../../../../../modules/juce_graphics/geometry/juce_Parallelogram_test.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_Path.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_Path.h"
    "../../../../../modules/juce_graphics/geometry/juce_PathIterator.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_PathIterator.h"
    "../../../../../modules/juce_graphics/geometry/juce_PathStrokeType.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_PathStrokeType.h"
    "../../../../../modules/juce_graphics/geometry/juce_Point.h"
    "../../../../../modules/juce_graphics/geometry/juce_Rectangle.h"
    "../../../../../modules/juce_graphics/geometry/juce_Rectangle_test.cpp"
    "../../../../../modules/juce_graphics/geometry/juce_RectangleList.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/cderror.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/changes to libjpeg for JUCE.txt"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcapimin.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcapistd.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jccoefct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jccolor.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcdctmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jchuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jchuff.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcinit.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmainct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmarker.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcmaster.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcomapi.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jconfig.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcparam.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcphuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcprepct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jcsample.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jctrans.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdapimin.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdapistd.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdatasrc.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdcoefct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdcolor.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdct.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jddctmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdhuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdhuff.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdinput.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmainct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmarker.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmaster.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdmerge.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdphuff.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdpostct.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdsample.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jdtrans.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jerror.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jerror.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctflt.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctfst.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jfdctint.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctflt.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctfst.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctint.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jidctred.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jinclude.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemmgr.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemnobs.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmemsys.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jmorecfg.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jpegint.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jpeglib.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jquant1.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jquant2.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jutils.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/jversion.h"
    "../../../../../modules/juce_graphics/image_formats/jpglib/transupp.c"
    "../../../../../modules/juce_graphics/image_formats/jpglib/transupp.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/libpng_readme.txt"
    "../../../../../modules/juce_graphics/image_formats/pnglib/png.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/png.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngconf.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngdebug.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngerror.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngget.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pnginfo.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngmem.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngpread.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngpriv.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngread.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrio.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrtran.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngrutil.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngset.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngstruct.h"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngtrans.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwio.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwrite.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwtran.c"
    "../../../../../modules/juce_graphics/image_formats/pnglib/pngwutil.c"
    "../../../../../modules/juce_graphics/image_formats/juce_GIFLoader.cpp"
    "../../../../../modules/juce_graphics/image_formats/juce_JPEGLoader.cpp"
    "../../../../../modules/juce_graphics/image_formats/juce_PNGLoader.cpp"
    "../../../../../modules/juce_graphics/images/juce_Image.cpp"
    "../../../../../modules/juce_graphics/images/juce_Image.h"
    "../../../../../modules/juce_graphics/images/juce_ImageCache.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageCache.h"
    "../../../../../modules/juce_graphics/images/juce_ImageConvolutionKernel.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageConvolutionKernel.h"
    "../../../../../modules/juce_graphics/images/juce_ImageFileFormat.cpp"
    "../../../../../modules/juce_graphics/images/juce_ImageFileFormat.h"
    "../../../../../modules/juce_graphics/images/juce_ScaledImage.h"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsContext_mac.h"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsContext_mac.mm"
    "../../../../../modules/juce_graphics/native/juce_CoreGraphicsHelpers_mac.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DGraphicsContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DGraphicsContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHelpers_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHwndContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DHwndContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImage_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImage_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImageContext_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DImageContext_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DMetrics_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_Direct2DMetrics_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DPixelDataPage_windows.h"
    "../../../../../modules/juce_graphics/native/juce_Direct2DResources_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_DirectWriteTypeface_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_DirectX_windows.h"
    "../../../../../modules/juce_graphics/native/juce_EventTracing.h"
    "../../../../../modules/juce_graphics/native/juce_Fonts_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_freetype.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_linux.cpp"
    "../../../../../modules/juce_graphics/native/juce_Fonts_mac.mm"
    "../../../../../modules/juce_graphics/native/juce_GraphicsContext_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_android.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_linux.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_mac.cpp"
    "../../../../../modules/juce_graphics/native/juce_IconHelpers_windows.cpp"
    "../../../../../modules/juce_graphics/native/juce_RenderingHelpers.h"
    "../../../../../modules/juce_graphics/placement/juce_Justification.h"
    "../../../../../modules/juce_graphics/placement/juce_RectanglePlacement.cpp"
    "../../../../../modules/juce_graphics/placement/juce_RectanglePlacement.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBAlgorithm.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBBase.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBBidiType.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBCodepoint.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBCodepointSequence.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBConfig.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBGeneralCategory.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBLine.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBMirrorLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBParagraph.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBScript.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SBScriptLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Headers/SheenBidi.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiChain.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiChain.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiTypeLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BidiTypeLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketQueue.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketQueue.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/BracketType.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/GeneralCategoryLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/GeneralCategoryLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/IsolatingRun.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/IsolatingRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/LevelRun.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/LevelRun.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/PairingLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/PairingLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunExtrema.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunKind.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunQueue.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/RunQueue.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAlgorithm.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAlgorithm.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBAssert.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBBase.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBBase.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBCodepointSequence.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBCodepointSequence.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLine.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLine.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLog.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBLog.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBMirrorLocator.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBMirrorLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBParagraph.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBParagraph.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBScriptLocator.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SBScriptLocator.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptLookup.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptLookup.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptStack.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/ScriptStack.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/SheenBidi.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/StatusStack.c"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/Source/StatusStack.h"
    "../../../../../modules/juce_graphics/unicode/sheenbidi/JUCE_CHANGES.txt"
    "../../../../../modules/juce_graphics/unicode/juce_Unicode.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeBidi.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeGenerated.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeLine.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeScript.cpp"
    "../../../../../modules/juce_graphics/unicode/juce_UnicodeUtils.cpp"
    "../../../../../modules/juce_graphics/juce_graphics.cpp"
    "../../../../../modules/juce_graphics/juce_graphics.mm"
    "../../../../../modules/juce_graphics/juce_graphics_Harfbuzz.cpp"
    "../../../../../modules/juce_graphics/juce_graphics_Sheenbidi.c"
    "../../../../../modules/juce_graphics/juce_graphics.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityActions.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityEvent.h"
    "../../../../../modules/juce_gui_basics/accessibility/enums/juce_AccessibilityRole.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityCellInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityTableInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityTextInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/interfaces/juce_AccessibilityValueInterface.h"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityHandler.cpp"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityHandler.h"
    "../../../../../modules/juce_gui_basics/accessibility/juce_AccessibilityState.h"
    "../../../../../modules/juce_gui_basics/application/juce_Application.cpp"
    "../../../../../modules/juce_gui_basics/application/juce_Application.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ArrowButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ArrowButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_Button.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_Button.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_DrawableButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_DrawableButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_HyperlinkButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_HyperlinkButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ImageButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ImageButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ShapeButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ShapeButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_TextButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_TextButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToggleButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToggleButton.h"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToolbarButton.cpp"
    "../../../../../modules/juce_gui_basics/buttons/juce_ToolbarButton.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandID.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandInfo.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandInfo.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandManager.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandManager.h"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandTarget.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_ApplicationCommandTarget.h"
    "../../../../../modules/juce_gui_basics/commands/juce_KeyPressMappingSet.cpp"
    "../../../../../modules/juce_gui_basics/commands/juce_KeyPressMappingSet.h"
    "../../../../../modules/juce_gui_basics/components/juce_CachedComponentImage.h"
    "../../../../../modules/juce_gui_basics/components/juce_Component.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_Component.h"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentListener.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentListener.h"
    "../../../../../modules/juce_gui_basics/components/juce_ComponentTraverser.h"
    "../../../../../modules/juce_gui_basics/components/juce_FocusTraverser.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_FocusTraverser.h"
    "../../../../../modules/juce_gui_basics/components/juce_ModalComponentManager.cpp"
    "../../../../../modules/juce_gui_basics/components/juce_ModalComponentManager.h"
    "../../../../../modules/juce_gui_basics/desktop/juce_Desktop.cpp"
    "../../../../../modules/juce_gui_basics/desktop/juce_Desktop.h"
    "../../../../../modules/juce_gui_basics/desktop/juce_Displays.cpp"
    "../../../../../modules/juce_gui_basics/desktop/juce_Displays.h"
    "../../../../../modules/juce_gui_basics/detail/juce_AccessibilityHelpers.cpp"
    "../../../../../modules/juce_gui_basics/detail/juce_AccessibilityHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_AlertWindowHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ButtonAccessibilityHandler.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ComponentHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_CustomMouseCursorInfo.h"
    "../../../../../modules/juce_gui_basics/detail/juce_FocusHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_FocusRestorer.h"
    "../../../../../modules/juce_gui_basics/detail/juce_LookAndFeelHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_MouseInputSourceImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_MouseInputSourceList.h"
    "../../../../../modules/juce_gui_basics/detail/juce_PointerState.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScalingHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedContentSharerImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedContentSharerInterface.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedMessageBoxImpl.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ScopedMessageBoxInterface.h"
    "../../../../../modules/juce_gui_basics/detail/juce_StandardCachedComponentImage.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ToolbarItemDragAndDropOverlayComponent.h"
    "../../../../../modules/juce_gui_basics/detail/juce_TopLevelWindowManager.h"
    "../../../../../modules/juce_gui_basics/detail/juce_ViewportHelpers.h"
    "../../../../../modules/juce_gui_basics/detail/juce_WindowingHelpers.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_Drawable.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_Drawable.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableComposite.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableComposite.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableImage.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableImage.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawablePath.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawablePath.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableRectangle.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableRectangle.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableShape.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableShape.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableText.cpp"
    "../../../../../modules/juce_gui_basics/drawables/juce_DrawableText.h"
    "../../../../../modules/juce_gui_basics/drawables/juce_SVGParser.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ContentSharer.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ContentSharer.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsDisplayComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsDisplayComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsList.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_DirectoryContentsList.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileBrowserListener.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooser.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooser.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooserDialogBox.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileChooserDialogBox.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileListComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileListComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilenameComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilenameComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FilePreviewComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileSearchPathListComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileSearchPathListComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileTreeComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_FileTreeComponent.h"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ImagePreviewComponent.cpp"
    "../../../../../modules/juce_gui_basics/filebrowser/juce_ImagePreviewComponent.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_CaretComponent.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_CaretComponent.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyboardFocusTraverser.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyboardFocusTraverser.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyListener.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyListener.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyPress.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_KeyPress.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_ModifierKeys.cpp"
    "../../../../../modules/juce_gui_basics/keyboard/juce_ModifierKeys.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_SystemClipboard.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_TextEditorKeyMapper.h"
    "../../../../../modules/juce_gui_basics/keyboard/juce_TextInputTarget.h"
    "../../../../../modules/juce_gui_basics/layout/juce_AnimatedPosition.h"
    "../../../../../modules/juce_gui_basics/layout/juce_AnimatedPositionBehaviours.h"
    "../../../../../modules/juce_gui_basics/layout/juce_BorderedComponentBoundsConstrainer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_BorderedComponentBoundsConstrainer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentAnimator.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentAnimator.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBoundsConstrainer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBoundsConstrainer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBuilder.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentBuilder.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentMovementWatcher.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ComponentMovementWatcher.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ConcertinaPanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ConcertinaPanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexBox.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexBox.h"
    "../../../../../modules/juce_gui_basics/layout/juce_FlexItem.h"
    "../../../../../modules/juce_gui_basics/layout/juce_Grid.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_Grid.h"
    "../../../../../modules/juce_gui_basics/layout/juce_GridItem.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_GridItem.h"
    "../../../../../modules/juce_gui_basics/layout/juce_GroupComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_GroupComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_MultiDocumentPanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_MultiDocumentPanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableBorderComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableBorderComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableCornerComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableCornerComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableEdgeComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ResizableEdgeComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_ScrollBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_ScrollBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_SidePanel.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_SidePanel.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutManager.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutManager.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutResizerBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableLayoutResizerBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableObjectResizer.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_StretchableObjectResizer.h"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedButtonBar.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedButtonBar.h"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedComponent.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_TabbedComponent.h"
    "../../../../../modules/juce_gui_basics/layout/juce_Viewport.cpp"
    "../../../../../modules/juce_gui_basics/layout/juce_Viewport.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V1.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V1.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V2.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V2.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V3.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V3.h"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V4.cpp"
    "../../../../../modules/juce_gui_basics/lookandfeel/juce_LookAndFeel_V4.h"
    "../../../../../modules/juce_gui_basics/menus/juce_BurgerMenuComponent.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_BurgerMenuComponent.h"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarComponent.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarComponent.h"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarModel.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_MenuBarModel.h"
    "../../../../../modules/juce_gui_basics/menus/juce_PopupMenu.cpp"
    "../../../../../modules/juce_gui_basics/menus/juce_PopupMenu.h"
    "../../../../../modules/juce_gui_basics/misc/juce_BubbleComponent.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_BubbleComponent.h"
    "../../../../../modules/juce_gui_basics/misc/juce_DropShadower.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_DropShadower.h"
    "../../../../../modules/juce_gui_basics/misc/juce_FocusOutline.cpp"
    "../../../../../modules/juce_gui_basics/misc/juce_FocusOutline.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_ComponentDragger.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_ComponentDragger.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropContainer.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropContainer.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_DragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_FileDragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_LassoComponent.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseCursor.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseCursor.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseEvent.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseEvent.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInactivityDetector.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInactivityDetector.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInputSource.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseInputSource.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseListener.cpp"
    "../../../../../modules/juce_gui_basics/mouse/juce_MouseListener.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_SelectedItemSet.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_TextDragAndDropTarget.h"
    "../../../../../modules/juce_gui_basics/mouse/juce_TooltipClient.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_android.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_ios.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_mac.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_Accessibility_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityElement_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityElement_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilitySharedCode_mac.mm"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityTextHelpers.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_AccessibilityTextHelpers_test.cpp"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAExpandCollapseProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAGridItemProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAGridProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAHelpers_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAInvokeProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAProviderBase_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAProviders_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIARangeValueProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIASelectionProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIATextProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAToggleProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIATransformProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAValueProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_UIAWindowProvider_windows.h"
    "../../../../../modules/juce_gui_basics/native/accessibility/juce_WindowsUIAWrapper_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_CGMetalLayerRenderer_mac.h"
    "../../../../../modules/juce_gui_basics/native/juce_ContentSharer_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_ContentSharer_ios.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_DragAndDrop_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_DragAndDrop_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_FileChooser_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_MainMenu_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_MouseCursor_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_MultiTouchMapper.h"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_NativeMessageBox_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_NativeModalWrapperComponent_ios.h"
    "../../../../../modules/juce_gui_basics/native/juce_NSViewComponentPeer_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_PerScreenDisplayLinks_mac.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedDPIAwarenessDisabler.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedDPIAwarenessDisabler.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedThreadDPIAwarenessSetter_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_ScopedWindowAssociation_linux.h"
    "../../../../../modules/juce_gui_basics/native/juce_UIViewComponentPeer_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_VBlank_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_Windowing_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowsHooks_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowsHooks_windows.h"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_android.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_ios.mm"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_mac.mm"
    "../../../../../modules/juce_gui_basics/native/juce_WindowUtils_windows.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XSymbols_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XSymbols_linux.h"
    "../../../../../modules/juce_gui_basics/native/juce_XWindowSystem_linux.cpp"
    "../../../../../modules/juce_gui_basics/native/juce_XWindowSystem_linux.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_MarkerList.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_MarkerList.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinate.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinate.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinatePositioner.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeCoordinatePositioner.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeParallelogram.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeParallelogram.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePoint.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePoint.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePointPath.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativePointPath.h"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeRectangle.cpp"
    "../../../../../modules/juce_gui_basics/positioning/juce_RelativeRectangle.h"
    "../../../../../modules/juce_gui_basics/properties/juce_BooleanPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_BooleanPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_ButtonPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_ButtonPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_ChoicePropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_ChoicePropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_MultiChoicePropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_MultiChoicePropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyPanel.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_PropertyPanel.h"
    "../../../../../modules/juce_gui_basics/properties/juce_SliderPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_SliderPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/properties/juce_TextPropertyComponent.cpp"
    "../../../../../modules/juce_gui_basics/properties/juce_TextPropertyComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ComboBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ComboBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ImageComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ImageComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Label.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Label.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ListBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ListBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ProgressBar.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ProgressBar.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Slider.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Slider.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableHeaderComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableHeaderComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableListBox.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TableListBox.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TextEditor.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TextEditor.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_Toolbar.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_Toolbar.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemComponent.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemComponent.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemFactory.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemPalette.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_ToolbarItemPalette.h"
    "../../../../../modules/juce_gui_basics/widgets/juce_TreeView.cpp"
    "../../../../../modules/juce_gui_basics/widgets/juce_TreeView.h"
    "../../../../../modules/juce_gui_basics/windows/juce_AlertWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_AlertWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_CallOutBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_CallOutBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ComponentPeer.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ComponentPeer.h"
    "../../../../../modules/juce_gui_basics/windows/juce_DialogWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_DialogWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_DocumentWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_DocumentWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_MessageBoxOptions.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_MessageBoxOptions.h"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeMessageBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeMessageBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeScaleFactorNotifier.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_NativeScaleFactorNotifier.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ResizableWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ResizableWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ScopedMessageBox.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ScopedMessageBox.h"
    "../../../../../modules/juce_gui_basics/windows/juce_ThreadWithProgressWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_ThreadWithProgressWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_TooltipWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_TooltipWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_TopLevelWindow.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_TopLevelWindow.h"
    "../../../../../modules/juce_gui_basics/windows/juce_VBlankAttachment.cpp"
    "../../../../../modules/juce_gui_basics/windows/juce_VBlankAttachment.h"
    "../../../../../modules/juce_gui_basics/windows/juce_WindowUtils.h"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.cpp"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.mm"
    "../../../../../modules/juce_gui_basics/juce_gui_basics.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeDocument.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeDocument.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeEditorComponent.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeEditorComponent.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_CPlusPlusCodeTokeniserFunctions.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_LuaCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_LuaCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/code_editor/juce_XMLCodeTokeniser.cpp"
    "../../../../../modules/juce_gui_extra/code_editor/juce_XMLCodeTokeniser.h"
    "../../../../../modules/juce_gui_extra/detail/juce_WebControlRelayEvents.h"
    "../../../../../modules/juce_gui_extra/documents/juce_FileBasedDocument.cpp"
    "../../../../../modules/juce_gui_extra/documents/juce_FileBasedDocument.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_ActiveXControlComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_AndroidViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_HWNDComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_NSViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_UIViewComponent.h"
    "../../../../../modules/juce_gui_extra/embedding/juce_XEmbedComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_AnimatedAppComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_AnimatedAppComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_AppleRemote.h"
    "../../../../../modules/juce_gui_extra/misc/juce_BubbleMessageComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_BubbleMessageComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_ColourSelector.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_ColourSelector.h"
    "../../../../../modules/juce_gui_extra/misc/juce_KeyMappingEditorComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_KeyMappingEditorComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_LiveConstantEditor.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_LiveConstantEditor.h"
    "../../../../../modules/juce_gui_extra/misc/juce_PreferencesPanel.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_PreferencesPanel.h"
    "../../../../../modules/juce_gui_extra/misc/juce_PushNotifications.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_PushNotifications.h"
    "../../../../../modules/juce_gui_extra/misc/juce_RecentlyOpenedFilesList.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_RecentlyOpenedFilesList.h"
    "../../../../../modules/juce_gui_extra/misc/juce_SplashScreen.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_SplashScreen.h"
    "../../../../../modules/juce_gui_extra/misc/juce_SystemTrayIconComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_SystemTrayIconComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebBrowserComponent.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_WebBrowserComponent.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlParameterIndexReceiver.h"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlRelays.cpp"
    "../../../../../modules/juce_gui_extra/misc/juce_WebControlRelays.h"
    "../../../../../modules/juce_gui_extra/native/juce_ActiveXComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_AndroidViewComponent.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_AppleRemote_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_HWNDComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_NSViewComponent_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_NSViewFrameWatcher_mac.h"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_android.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_ios.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_PushNotifications_mac.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_linux.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_mac.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_SystemTrayIcon_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_UIViewComponent_ios.mm"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_android.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_linux.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_mac.mm"
    "../../../../../modules/juce_gui_extra/native/juce_WebBrowserComponent_windows.cpp"
    "../../../../../modules/juce_gui_extra/native/juce_XEmbedComponent_linux.cpp"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.cpp"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.mm"
    "../../../../../modules/juce_gui_extra/juce_gui_extra.h"
    "../../../../../modules/juce_javascript/choc/containers/choc_Value.h"
    "../../../../../modules/juce_javascript/choc/javascript/choc_javascript.h"
    "../../../../../modules/juce_javascript/choc/javascript/choc_javascript_QuickJS.h"
    "../../../../../modules/juce_javascript/choc/math/choc_MathHelpers.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_Assert.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_DisableAllWarnings.h"
    "../../../../../modules/juce_javascript/choc/platform/choc_ReenableAllWarnings.h"
    "../../../../../modules/juce_javascript/choc/text/choc_FloatToString.h"
    "../../../../../modules/juce_javascript/choc/text/choc_JSON.h"
    "../../../../../modules/juce_javascript/choc/text/choc_StringUtilities.h"
    "../../../../../modules/juce_javascript/choc/text/choc_UTF8.h"
    "../../../../../modules/juce_javascript/choc/LICENSE.md"
    "../../../../../modules/juce_javascript/detail/juce_QuickJSHelpers.h"
    "../../../../../modules/juce_javascript/javascript/juce_Javascript_test.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JavascriptEngine.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JavascriptEngine.h"
    "../../../../../modules/juce_javascript/javascript/juce_JSCursor.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JSCursor.h"
    "../../../../../modules/juce_javascript/javascript/juce_JSObject.cpp"
    "../../../../../modules/juce_javascript/javascript/juce_JSObject.h"
    "../../../../../modules/juce_javascript/juce_javascript.cpp"
    "../../../../../modules/juce_javascript/juce_javascript.h"
    "../../../../../modules/juce_opengl/geometry/juce_Draggable3DOrientation.h"
    "../../../../../modules/juce_opengl/geometry/juce_Matrix3D.h"
    "../../../../../modules/juce_opengl/geometry/juce_Quaternion.h"
    "../../../../../modules/juce_opengl/geometry/juce_Vector3D.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_android.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_ios.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_linux.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_mac.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGL_windows.h"
    "../../../../../modules/juce_opengl/native/juce_OpenGLExtensions.h"
    "../../../../../modules/juce_opengl/opengl/juce_gl.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_gl.h"
    "../../../../../modules/juce_opengl/opengl/juce_gles2.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_gles2.h"
    "../../../../../modules/juce_opengl/opengl/juce_khrplatform.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLContext.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLContext.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLFrameBuffer.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLFrameBuffer.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLGraphicsContext.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLGraphicsContext.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLHelpers.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLHelpers.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLImage.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLImage.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLPixelFormat.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLPixelFormat.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLRenderer.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLShaderProgram.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLShaderProgram.h"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLTexture.cpp"
    "../../../../../modules/juce_opengl/opengl/juce_OpenGLTexture.h"
    "../../../../../modules/juce_opengl/opengl/juce_wgl.h"
    "../../../../../modules/juce_opengl/utils/juce_OpenGLAppComponent.cpp"
    "../../../../../modules/juce_opengl/utils/juce_OpenGLAppComponent.h"
    "../../../../../modules/juce_opengl/juce_opengl.cpp"
    "../../../../../modules/juce_opengl/juce_opengl.mm"
    "../../../../../modules/juce_opengl/juce_opengl.h"
    "../../../../../modules/juce_osc/osc/juce_OSCAddress.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCAddress.h"
    "../../../../../modules/juce_osc/osc/juce_OSCArgument.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCArgument.h"
    "../../../../../modules/juce_osc/osc/juce_OSCBundle.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCBundle.h"
    "../../../../../modules/juce_osc/osc/juce_OSCMessage.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCMessage.h"
    "../../../../../modules/juce_osc/osc/juce_OSCReceiver.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCReceiver.h"
    "../../../../../modules/juce_osc/osc/juce_OSCSender.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCSender.h"
    "../../../../../modules/juce_osc/osc/juce_OSCTimeTag.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCTimeTag.h"
    "../../../../../modules/juce_osc/osc/juce_OSCTypes.cpp"
    "../../../../../modules/juce_osc/osc/juce_OSCTypes.h"
    "../../../../../modules/juce_osc/juce_osc.cpp"
    "../../../../../modules/juce_osc/juce_osc.h"
    "../../../../../modules/juce_product_unlocking/in_app_purchases/juce_InAppPurchases.cpp"
    "../../../../../modules/juce_product_unlocking/in_app_purchases/juce_InAppPurchases.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_KeyFileGeneration.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockForm.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockForm.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockStatus.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_OnlineUnlockStatus.h"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_TracktionMarketplaceStatus.cpp"
    "../../../../../modules/juce_product_unlocking/marketplace/juce_TracktionMarketplaceStatus.h"
    "../../../../../modules/juce_product_unlocking/native/juce_InAppPurchases_android.cpp"
    "../../../../../modules/juce_product_unlocking/native/juce_InAppPurchases_ios.cpp"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.cpp"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.mm"
    "../../../../../modules/juce_product_unlocking/juce_product_unlocking.h"
    "../../../../../modules/juce_video/capture/juce_CameraDevice.cpp"
    "../../../../../modules/juce_video/capture/juce_CameraDevice.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_android.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_ios.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_mac.h"
    "../../../../../modules/juce_video/native/juce_CameraDevice_windows.h"
    "../../../../../modules/juce_video/native/juce_Video_android.h"
    "../../../../../modules/juce_video/native/juce_Video_mac.h"
    "../../../../../modules/juce_video/native/juce_Video_windows.h"
    "../../../../../modules/juce_video/playback/juce_VideoComponent.cpp"
    "../../../../../modules/juce_video/playback/juce_VideoComponent.h"
    "../../../../../modules/juce_video/juce_video.cpp"
    "../../../../../modules/juce_video/juce_video.mm"
    "../../../../../modules/juce_video/juce_video.h"
    "../../../JuceLibraryCode/JuceHeader.h"
    PROPERTIES HEADER_FILE_ONLY TRUE)

if( JUCE_BUILD_CONFIGURATION MATCHES "DEBUG" )
    target_compile_options( ${BINARY_NAME} PRIVATE -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override "-fsigned-char" )
endif()

if( JUCE_BUILD_CONFIGURATION MATCHES "RELEASE" )
    target_compile_options( ${BINARY_NAME} PRIVATE -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override "-fsigned-char" )
endif()

find_library(log "log")
find_library(android "android")
find_library(glesv3 "GLESv3")
find_library(egl "EGL")

target_link_libraries( ${BINARY_NAME}

    ${log}
    ${android}
    ${glesv3}
    ${egl}
    "cpufeatures"
    "oboe"
)
