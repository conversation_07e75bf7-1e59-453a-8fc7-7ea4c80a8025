<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
  <dict>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires audio input. If you do not have an audio interface connected it will use the built-in microphone.</string>
    <key>NSCameraUsageDescription</key>
    <string>This app requires access to the camera to function correctly.</string>
    <key>NSBluetoothAlwaysUsageDescription</key>
    <string>This app requires access to Bluetooth to function correctly.</string>
    <key>CFBundleExecutable</key>
    <string>${EXECUTABLE_NAME}</string>
    <key>CFBundleIconFile</key>
    <string>Icon.icns</string>
    <key>CFBundleIdentifier</key>
    <string>com.rmsl.jucedemorunner</string>
    <key>CFBundleName</key>
    <string>DemoRunner</string>
    <key>CFBundleDisplayName</key>
    <string>DemoRunner</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleShortVersionString</key>
    <string>8.0.6</string>
    <key>CFBundleVersion</key>
    <string>8.0.6</string>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright (c) - Raw Material Software Limited</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.developer-tools</string>
  </dict>
</plist>
