<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT name="DemoRunner" projectType="guiapp" defines="JUCE_DEMO_RUNNER=1&#10;JUCE_UNIT_TESTS=1&#10;JUCE_PUSH_NOTIFICATIONS=1"
              bundleIdentifier="com.rmsl.jucedemorunner" version="8.0.6" companyName="Raw Material Software Limited"
              companyCopyright="Copyright (c) - Raw Material Software Limited"
              companyWebsite="https://www.juce.com/" companyEmail="<EMAIL>"
              id="yj7xMM" useAppConfig="0" addUsingNamespaceToJuceHeader="1"
              jucerFormatVersion="1">
  <MAINGROUP id="G8kbr7" name="DemoRunner">
    <GROUP id="{20E3F84A-29E9-D5FF-4559-1A9E4A70CD60}" name="Source">
      <GROUP id="{272A692A-6AFE-68BD-C8E8-63B3D62245B1}" name="Demos">
        <FILE id="jbuZKy" name="DemoPIPs1.cpp" compile="1" resource="0" file="Source/Demos/DemoPIPs1.cpp"/>
        <FILE id="uupvlH" name="DemoPIPs2.cpp" compile="1" resource="0" file="Source/Demos/DemoPIPs2.cpp"/>
        <FILE id="hUgbGw" name="IntroScreen.h" compile="0" resource="0" file="Source/Demos/IntroScreen.h"/>
        <FILE id="Mrb4aB" name="JUCEDemos.cpp" compile="1" resource="0" file="Source/Demos/JUCEDemos.cpp"/>
        <FILE id="JFM1mF" name="JUCEDemos.h" compile="0" resource="0" file="Source/Demos/JUCEDemos.h"/>
      </GROUP>
      <GROUP id="{4488029C-EEC9-B0FE-EC7F-99A571E0A17C}" name="UI">
        <FILE id="pIM8IW" name="DemoContentComponent.cpp" compile="1" resource="0"
              file="Source/UI/DemoContentComponent.cpp"/>
        <FILE id="Tf8KH7" name="DemoContentComponent.h" compile="0" resource="0"
              file="Source/UI/DemoContentComponent.h"/>
        <FILE id="CHJ2WN" name="MainComponent.cpp" compile="1" resource="0"
              file="Source/UI/MainComponent.cpp"/>
        <FILE id="GHBuMQ" name="MainComponent.h" compile="0" resource="0" file="Source/UI/MainComponent.h"/>
        <FILE id="DM4iMI" name="SettingsContent.h" compile="0" resource="0"
              file="Source/UI/SettingsContent.h"/>
      </GROUP>
      <FILE id="fcr468" name="Main.cpp" compile="1" resource="0" file="Source/Main.cpp"/>
      <FILE id="YyqWd2" name="JUCEAppIcon.png" compile="0" resource="0" file="Source/JUCEAppIcon.png"/>
    </GROUP>
  </MAINGROUP>
  <EXPORTFORMATS>
    <XCODE_MAC targetFolder="Builds/MacOSX" smallIcon="YyqWd2" bigIcon="YyqWd2"
               customXcodeResourceFolders="../Assets&#10;../Audio &#10;../DSP &#10;../GUI &#10;../Utilities"
               microphonePermissionNeeded="1" cameraPermissionNeeded="1" applicationCategory="public.app-category.developer-tools"
               iosBluetoothPermissionNeeded="1" extraDefs="JUCE_SILENCE_XCODE_15_LINKER_WARNING=1"
               extraLinkerFlags="-Wl,-weak_reference_mismatches,weak">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" recommendedWarnings="LLVM"/>
        <CONFIGURATION isDebug="0" name="Release" recommendedWarnings="LLVM"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
    <LINUX_MAKE targetFolder="Builds/LinuxMakefile" smallIcon="YyqWd2" bigIcon="YyqWd2">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug"/>
        <CONFIGURATION isDebug="0" name="Release"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </LINUX_MAKE>
    <ANDROIDSTUDIO targetFolder="Builds/Android" androidMinimumSDK="23" microphonePermissionNeeded="1"
                   androidExternalWriteNeeded="1" androidEnableContentSharing="1"
                   androidExtraAssetsFolder="../Assets" smallIcon="YyqWd2" bigIcon="YyqWd2"
                   cameraPermissionNeeded="1" androidReadMediaAudioPermission="1"
                   androidReadMediaImagesPermission="1" androidReadMediaVideoPermission="1"
                   androidBluetoothScanNeeded="1" androidBluetoothAdvertiseNeeded="1"
                   androidBluetoothConnectNeeded="1">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" recommendedWarnings="LLVM" androidAdditionalRawValueResources="Source/accessibilitynotificationicon.png"/>
        <CONFIGURATION isDebug="0" name="Release" recommendedWarnings="LLVM" androidAdditionalRawValueResources="Source/accessibilitynotificationicon.png"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </ANDROIDSTUDIO>
    <XCODE_IPHONE targetFolder="Builds/iOS" UISupportsDocumentBrowser="1" microphonePermissionNeeded="1"
                  cameraPermissionNeeded="1" iCloudPermissions="1" UIFileSharingEnabled="1"
                  customXcodeResourceFolders="../Assets" smallIcon="YyqWd2" bigIcon="YyqWd2"
                  iosBluetoothPermissionNeeded="1" iosScreenOrientation="UIInterfaceOrientationLandscapeLeft,UIInterfaceOrientationLandscapeRight,UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown"
                  iPadScreenOrientation="UIInterfaceOrientationLandscapeLeft,UIInterfaceOrientationLandscapeRight,UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown"
                  UIRequiresFullScreen="0" extraDefs="JUCE_SILENCE_XCODE_15_LINKER_WARNING=1"
                  extraLinkerFlags="-Wl,-weak_reference_mismatches,weak">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" recommendedWarnings="LLVM"/>
        <CONFIGURATION isDebug="0" name="Release" recommendedWarnings="LLVM"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_IPHONE>
    <VS2019 targetFolder="Builds/VisualStudio2019" smallIcon="YyqWd2" bigIcon="YyqWd2"
            extraCompilerFlags="/w44265 /w45038 /w44062">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" targetName="DemoRunner"/>
        <CONFIGURATION isDebug="0" name="Release" useRuntimeLibDLL="0" targetName="DemoRunner"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </VS2019>
    <VS2022 targetFolder="Builds/VisualStudio2022" smallIcon="YyqWd2" bigIcon="YyqWd2"
            extraCompilerFlags="/w44265 /w45038 /w44062">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" targetName="DemoRunner"/>
        <CONFIGURATION isDebug="0" name="Release" useRuntimeLibDLL="0" targetName="DemoRunner"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_video" path="../../modules"/>
        <MODULEPATH id="juce_product_unlocking" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_box2d" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_analytics" path="../../modules"/>
        <MODULEPATH id="juce_animation" path="../../modules"/>
        <MODULEPATH id="juce_javascript" path="../../modules"/>
      </MODULEPATHS>
    </VS2022>
  </EXPORTFORMATS>
  <MODULES>
    <MODULE id="juce_analytics" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_animation" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_audio_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_audio_devices" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_audio_formats" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_audio_processors" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_audio_utils" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_box2d" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_core" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_cryptography" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_data_structures" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_dsp" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_events" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_graphics" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_gui_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_gui_extra" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_javascript" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_opengl" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_osc" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
    <MODULE id="juce_product_unlocking" showAllCode="1" useLocalCopy="0"
            useGlobalPath="0"/>
    <MODULE id="juce_video" showAllCode="1" useLocalCopy="0" useGlobalPath="0"/>
  </MODULES>
  <JUCEOPTIONS JUCE_USE_CAMERA="1" JUCE_USE_MP3AUDIOFORMAT="1" JUCE_ALLOW_STATIC_NULL_VARIABLES="0"
               JUCE_STRICT_REFCOUNTEDPOINTER="1" JUCE_PLUGINHOST_LV2="1" JUCE_PLUGINHOST_VST3="1"/>
  <LIVE_SETTINGS>
    <OSX/>
  </LIVE_SETTINGS>
</JUCERPROJECT>
