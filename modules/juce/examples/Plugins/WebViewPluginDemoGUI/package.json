{"name": "webviewplugin-gui", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.3", "@mui/icons-material": "^5.13.7", "@mui/material": "^5.13.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "juce-framework-frontend": "file:../../../modules/juce_gui_extra/native/javascript", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "zip": "npm-build-zip --destination=../../Assets"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.43.0", "eslint-plugin-react": "^7.32.2", "npm-build-zip": "^1.0.4"}}