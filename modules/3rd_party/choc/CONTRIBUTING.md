## CHOC: contributing & CLA

Apologies in advance, but thanks to several full-time jobs and a small child, I have no spare time to:

- Create a suitable CLA (Contributor License Agreement)
- Manage the process of getting contributors to sign it, and keeping legally-satisfactory records of that process (with all the GDPR implications that come along).
- Spend time considering, reviewing and fixing-up PRs, and dealing diplomatically with poor-quality code, or needy contributors.

So basically: please don't submit any PRs!

**If you find a bug:** great! Please submit a bug report and I'll sort it out asap!
**If you have a feature request:** that's also great.. please feel free to post it as an issue, but don't expect a quick response!

Maybe in the future if there's a huge demand for it, then this situation will change, but in the short-term, I'm afraid you should treat the code here as a read-only resource!